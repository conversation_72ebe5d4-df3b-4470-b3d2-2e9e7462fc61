{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/utils/RPN.ts"], "names": ["FunctionRegistry", "RPNProgram", "RPNCalculator", "compileRPNInternal", "tokens", "registry", "opts", "code", "consts", "varIndex", "Map", "varNames", "propIndex", "propPaths", "pushConst", "v", "push", "OpCode", "PUSH_CONST", "length", "pushVar", "name", "idx", "get", "undefined", "set", "PUSH_VAR", "pushProp", "path", "path<PERSON><PERSON>", "join", "PUSH_PROP", "emitBinary", "op", "unknownIsVar", "unknownIdentifierIsVar", "strictVars", "raw", "t", "trim", "NUM_RE", "test", "parseFloat", "ADD", "SUB", "MUL", "DIV", "MOD", "POW", "NEG", "LT", "LE", "GT", "GE", "EQ", "NEQ", "AND", "OR", "NOT", "startsWith", "parts", "split", "arity", "parseInt", "meta", "getByNameAndArity", "Error", "CALL", "id", "includes", "tokenize", "expr", "out", "re", "m", "exec", "s", "type", "value", "infixToRPN", "output", "opStack", "i", "next", "argCount", "pop", "j", "rawOp", "prev", "isUnary", "op<PERSON>ey", "OPS", "o1", "top", "o2", "assoc", "prec", "fn", "before", "finalArity", "_decorator", "ccclass", "property", "by<PERSON><PERSON>", "byId", "register", "impl", "Number", "isInteger", "map", "existing", "getSingleByName", "size", "Array", "from", "values", "getById", "constructor", "evaluate", "ctx", "varVals", "stack", "ip", "prop", "b", "a", "Math", "pow", "Boolean", "fnId", "args", "getVariables", "serialize", "deserialize", "data", "SerializableRPNProgram", "visible", "String", "toRPNProgram", "fromRPNProgram", "program", "serializable", "isCompiled", "unary", "registerBuiltins", "compile", "isArray", "r", "abs", "floor", "ceil", "round", "sin", "cos", "tan", "min", "max", "x", "lo", "hi", "random", "A", "B", "cond"], "mappings": ";;;8EA2BaA,gB,EAkDAC,U,EAwaAC,a;;;;;;;;AAhQb,WAASC,kBAAT,CAA4BC,MAA5B,EAA8CC,QAA9C,EAA0EC,IAA1E,EAA6G;AAAA;;AAC3G,QAAMC,IAAc,GAAG,EAAvB;AACA,QAAMC,MAAkB,GAAG,EAA3B;AACA,QAAMC,QAAQ,GAAG,IAAIC,GAAJ,EAAjB;AACA,QAAMC,QAAkB,GAAG,EAA3B;AACA,QAAMC,SAAS,GAAG,IAAIF,GAAJ,EAAlB;AACA,QAAMG,SAAqB,GAAG,EAA9B;;AAEA,QAAMC,SAAS,GAAIC,CAAD,IAAiB;AAAEP,MAAAA,MAAM,CAACQ,IAAP,CAAYD,CAAZ;AAAgBR,MAAAA,IAAI,CAACS,IAAL,CAAUC,MAAM,CAACC,UAAjB,EAA6BV,MAAM,CAACW,MAAP,GAAgB,CAA7C;AAAkD,KAAvG;;AACA,QAAMC,OAAO,GAAIC,IAAD,IAAkB;AAChC,UAAIC,GAAG,GAAGb,QAAQ,CAACc,GAAT,CAAaF,IAAb,CAAV;;AACA,UAAIC,GAAG,KAAKE,SAAZ,EAAuB;AAAEF,QAAAA,GAAG,GAAGX,QAAQ,CAACQ,MAAf;AAAuBV,QAAAA,QAAQ,CAACgB,GAAT,CAAaJ,IAAb,EAAmBC,GAAnB;AAAyBX,QAAAA,QAAQ,CAACK,IAAT,CAAcK,IAAd;AAAsB;;AAC/Fd,MAAAA,IAAI,CAACS,IAAL,CAAUC,MAAM,CAACS,QAAjB,EAA2BJ,GAA3B;AACD,KAJD;;AAKA,QAAMK,QAAQ,GAAIC,IAAD,IAAoB;AACnC,UAAMC,OAAO,GAAGD,IAAI,CAACE,IAAL,CAAU,GAAV,CAAhB;AACA,UAAIR,GAAG,GAAGV,SAAS,CAACW,GAAV,CAAcM,OAAd,CAAV;;AACA,UAAIP,GAAG,KAAKE,SAAZ,EAAuB;AAAEF,QAAAA,GAAG,GAAGT,SAAS,CAACM,MAAhB;AAAwBP,QAAAA,SAAS,CAACa,GAAV,CAAcI,OAAd,EAAuBP,GAAvB;AAA6BT,QAAAA,SAAS,CAACG,IAAV,CAAeY,IAAf;AAAuB;;AACrGrB,MAAAA,IAAI,CAACS,IAAL,CAAUC,MAAM,CAACc,SAAjB,EAA4BT,GAA5B;AACD,KALD;;AAMA,QAAMU,UAAU,GAAIC,EAAD,IAAgB1B,IAAI,CAACS,IAAL,CAAUiB,EAAV,CAAnC;;AAEA,QAAMC,YAAY,4BAAG5B,IAAH,oBAAGA,IAAI,CAAE6B,sBAAT,oCAAmC,IAArD;AACA,QAAMC,UAAU,uBAAG9B,IAAH,oBAAGA,IAAI,CAAE8B,UAAT,+BAAuB,IAAvC;;AAEA,SAAK,IAAMC,GAAX,IAAkBjC,MAAlB,EAA0B;AACxB,UAAMkC,CAAC,GAAGD,GAAG,CAACE,IAAJ,EAAV;AACA,UAAI,CAACD,CAAL,EAAQ,SAFgB,CAGxB;;AACA,UAAIE,MAAM,CAACC,IAAP,CAAYH,CAAZ,CAAJ,EAAoB;AAAExB,QAAAA,SAAS,CAAC4B,UAAU,CAACJ,CAAD,CAAX,CAAT;AAA0B;AAAW;;AAC3D,UAAI,UAAUG,IAAV,CAAeH,CAAf,CAAJ,EAAuB;AAAExB,QAAAA,SAAS,CAAC,IAAD,CAAT;AAAiB;AAAW;;AACrD,UAAI,WAAW2B,IAAX,CAAgBH,CAAhB,CAAJ,EAAwB;AAAExB,QAAAA,SAAS,CAAC,KAAD,CAAT;AAAkB;AAAW,OAN/B,CAQxB;;;AACA,cAAQwB,CAAR;AACE,aAAK,GAAL;AAAUN,UAAAA,UAAU,CAACf,MAAM,CAAC0B,GAAR,CAAV;AAAwB;;AAClC,aAAK,GAAL;AAAUX,UAAAA,UAAU,CAACf,MAAM,CAAC2B,GAAR,CAAV;AAAwB;;AAClC,aAAK,GAAL;AAAUZ,UAAAA,UAAU,CAACf,MAAM,CAAC4B,GAAR,CAAV;AAAwB;;AAClC,aAAK,GAAL;AAAUb,UAAAA,UAAU,CAACf,MAAM,CAAC6B,GAAR,CAAV;AAAwB;;AAClC,aAAK,GAAL;AAAUd,UAAAA,UAAU,CAACf,MAAM,CAAC8B,GAAR,CAAV;AAAwB;;AAClC,aAAK,GAAL;AAAUf,UAAAA,UAAU,CAACf,MAAM,CAAC+B,GAAR,CAAV;AAAwB;;AAClC,aAAK,KAAL;AAAYzC,UAAAA,IAAI,CAACS,IAAL,CAAUC,MAAM,CAACgC,GAAjB;AAAuB;AAAU;;AAC7C,aAAK,GAAL;AAAUjB,UAAAA,UAAU,CAACf,MAAM,CAACiC,EAAR,CAAV;AAAuB;;AACjC,aAAK,IAAL;AAAWlB,UAAAA,UAAU,CAACf,MAAM,CAACkC,EAAR,CAAV;AAAuB;;AAClC,aAAK,GAAL;AAAUnB,UAAAA,UAAU,CAACf,MAAM,CAACmC,EAAR,CAAV;AAAuB;;AACjC,aAAK,IAAL;AAAWpB,UAAAA,UAAU,CAACf,MAAM,CAACoC,EAAR,CAAV;AAAuB;;AAClC,aAAK,IAAL;AAAWrB,UAAAA,UAAU,CAACf,MAAM,CAACqC,EAAR,CAAV;AAAuB;;AAClC,aAAK,IAAL;AAAWtB,UAAAA,UAAU,CAACf,MAAM,CAACsC,GAAR,CAAV;AAAwB;;AACnC,aAAK,IAAL;AAAWvB,UAAAA,UAAU,CAACf,MAAM,CAACuC,GAAR,CAAV;AAAwB;;AACnC,aAAK,IAAL;AAAWxB,UAAAA,UAAU,CAACf,MAAM,CAACwC,EAAR,CAAV;AAAuB;;AAClC,aAAK,GAAL;AAAUlD,UAAAA,IAAI,CAACS,IAAL,CAAUC,MAAM,CAACyC,GAAjB;AAAuB;AAhBnC,OATwB,CA4BxB;;;AACA,UAAIpB,CAAC,CAACqB,UAAF,CAAa,OAAb,CAAJ,EAA2B;AACzB,YAAMC,KAAK,GAAGtB,CAAC,CAACuB,KAAF,CAAQ,GAAR,CAAd,CADyB,CAEzB;;AACA,YAAMxC,MAAI,GAAGuC,KAAK,CAAC,CAAD,CAAlB;AACA,YAAME,KAAK,GAAGC,QAAQ,CAACH,KAAK,CAAC,CAAD,CAAN,EAAW,EAAX,CAAtB;AACA,YAAMI,IAAI,GAAG3D,QAAQ,CAAC4D,iBAAT,CAA2B5C,MAA3B,EAAiCyC,KAAjC,CAAb;AACA,YAAI,CAACE,IAAL,EAAW,MAAM,IAAIE,KAAJ,gBAAuB7C,MAAvB,qBAA2CyC,KAA3C,yBAAN;AACXvD,QAAAA,IAAI,CAACS,IAAL,CAAUC,MAAM,CAACkD,IAAjB,EAAuBH,IAAI,CAACI,EAA5B,EAAgCN,KAAhC;AACA;AACD,OAtCuB,CAwCxB;;;AACA,UAAI5B,YAAJ,EAAkB;AAChB;AACA,YAAII,CAAC,CAAC+B,QAAF,CAAW,GAAX,CAAJ,EAAqB;AACnB,cAAMzC,IAAI,GAAGU,CAAC,CAACuB,KAAF,CAAQ,GAAR,CAAb;AACAlC,UAAAA,QAAQ,CAACC,IAAD,CAAR;AACD,SAHD,MAGO;AACLR,UAAAA,OAAO,CAACkB,CAAD,CAAP;AACD;;AACD;AACD;;AACD,YAAM,IAAI4B,KAAJ,qBAA4B5B,CAA5B,QAAN;AACD;;AAED,WAAO,IAAIrC,UAAJ,CAAeM,IAAf,EAAqBC,MAArB,EAA6BG,QAA7B,EAAuCE,SAAvC,EAAkDR,QAAlD,EAA4D+B,UAA5D,CAAP;AACD,G,CAED;;;AAGA,WAASkC,QAAT,CAAkBC,IAAlB,EAAyC;AACvC,QAAMC,GAAY,GAAG,EAArB;AACA,QAAMC,EAAE,GAAG,sFAAX;AACA,QAAIC,CAAJ;;AACA,WAAO,CAACA,CAAC,GAAGD,EAAE,CAACE,IAAH,CAAQJ,IAAR,CAAL,MAAwB,IAA/B,EAAqC;AACnC,UAAMK,CAAC,GAAGF,CAAC,CAAC,CAAD,CAAX;AACA,UAAI,oBAAoBjC,IAApB,CAAyBmC,CAAzB,CAAJ,EAAiCJ,GAAG,CAACxD,IAAJ,CAAS;AAAC6D,QAAAA,IAAI,EAAC,QAAN;AAAgBC,QAAAA,KAAK,EAACF;AAAtB,OAAT,EAAjC,KACK,IAAI,gBAAgBnC,IAAhB,CAAqBmC,CAArB,CAAJ,EAA6BJ,GAAG,CAACxD,IAAJ,CAAS;AAAC6D,QAAAA,IAAI,EAAC,MAAN;AAAcC,QAAAA,KAAK,EAACF;AAApB,OAAT,EAA7B,KACA,IAAIA,CAAC,KAAK,GAAV,EAAeJ,GAAG,CAACxD,IAAJ,CAAS;AAAC6D,QAAAA,IAAI,EAAC,QAAN;AAAgBC,QAAAA,KAAK,EAACF;AAAtB,OAAT,EAAf,KACA,IAAIA,CAAC,KAAK,GAAV,EAAeJ,GAAG,CAACxD,IAAJ,CAAS;AAAC6D,QAAAA,IAAI,EAAC,QAAN;AAAgBC,QAAAA,KAAK,EAACF;AAAtB,OAAT,EAAf,KACA,IAAIA,CAAC,KAAK,GAAV,EAAeJ,GAAG,CAACxD,IAAJ,CAAS;AAAC6D,QAAAA,IAAI,EAAC,OAAN;AAAeC,QAAAA,KAAK,EAACF;AAArB,OAAT,EAAf,KACA,IAAI,mBAAmBnC,IAAnB,CAAwBmC,CAAxB,KAA8BA,CAAC,KAAK,IAApC,IAA4CA,CAAC,KAAK,IAAtD,EAA4DJ,GAAG,CAACxD,IAAJ,CAAS;AAAC6D,QAAAA,IAAI,EAAC,IAAN;AAAYC,QAAAA,KAAK,EAACF;AAAlB,OAAT,EAA5D,KACAJ,GAAG,CAACxD,IAAJ,CAAS;AAAC6D,QAAAA,IAAI,EAAC,OAAN;AAAeC,QAAAA,KAAK,EAACF;AAArB,OAAT;AACN;;AACD,WAAOJ,GAAP;AACD,G,CAED;;;AAsBA,WAASO,UAAT,CAAoBR,IAApB,EAAkClE,QAAlC,EAAwE;AACtE,QAAMD,MAAM,GAAGkE,QAAQ,CAACC,IAAD,CAAvB;AACA,QAAMS,MAAgB,GAAG,EAAzB;AACA,QAAMC,OAAsF,GAAG,EAA/F,CAHsE,CAKtE;;AACA,SAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG9E,MAAM,CAACe,MAA3B,EAAmC+D,CAAC,EAApC,EAAwC;AACtC,UAAM5C,CAAC,GAAGlC,MAAM,CAAC8E,CAAD,CAAhB;;AAEA,UAAI5C,CAAC,CAACuC,IAAF,KAAW,QAAX,IAAuBvC,CAAC,CAACuC,IAAF,KAAW,MAAtC,EAA8C;AAC5CG,QAAAA,MAAM,CAAChE,IAAP,CAAYsB,CAAC,CAACwC,KAAd;AACA;AACD;;AAED,UAAIxC,CAAC,CAACuC,IAAF,KAAW,OAAf,EAAwB;AACtB;AACA,YAAMM,IAAI,GAAG/E,MAAM,CAAC8E,CAAC,GAAC,CAAH,CAAnB;;AACA,YAAIC,IAAI,IAAIA,IAAI,CAACN,IAAL,KAAc,QAA1B,EAAoC;AAClC;AACAI,UAAAA,OAAO,CAACjE,IAAR,CAAa;AAAC6D,YAAAA,IAAI,EAAC,MAAN;AAAcC,YAAAA,KAAK,EAAExC,CAAC,CAACwC,KAAvB;AAA8BM,YAAAA,QAAQ,EAAE;AAAxC,WAAb;AACD,SAHD,MAGO;AACL;AACAJ,UAAAA,MAAM,CAAChE,IAAP,CAAYsB,CAAC,CAACwC,KAAd;AACD;;AACD;AACD;;AAED,UAAIxC,CAAC,CAACuC,IAAF,KAAW,OAAf,EAAwB;AACtB;AACA,eAAOI,OAAO,CAAC9D,MAAR,GAAiB,CAAjB,IAAsB8D,OAAO,CAACA,OAAO,CAAC9D,MAAR,GAAe,CAAhB,CAAP,CAA0B0D,IAA1B,KAAmC,QAAhE,EAA0E;AACxE,cAAM5C,EAAE,GAAGgD,OAAO,CAACI,GAAR,EAAX;AACA,cAAIpD,EAAE,CAAC4C,IAAH,KAAY,IAAhB,EAAsBG,MAAM,CAAChE,IAAP,CAAYiB,EAAE,CAAC6C,KAAf,EAAtB,KACK,IAAI7C,EAAE,CAAC4C,IAAH,KAAY,MAAhB,EAAwB;AAAA;;AAC3B;AACAG,YAAAA,MAAM,CAAChE,IAAP,WAAoBiB,EAAE,CAAC6C,KAAvB,0BAAiC7C,EAAE,CAACmD,QAApC,2BAAgD,CAAhD;AACD;AACF,SATqB,CAUtB;AACA;;;AACA,aAAK,IAAIE,CAAC,GAAGL,OAAO,CAAC9D,MAAR,GAAiB,CAA9B,EAAiCmE,CAAC,IAAI,CAAtC,EAAyCA,CAAC,EAA1C,EAA8C;AAC5C,cAAIL,OAAO,CAACK,CAAD,CAAP,CAAWT,IAAX,KAAoB,QAAxB,EAAkC;AAChC;AACA,gBAAIS,CAAC,GAAG,CAAJ,IAASL,OAAO,CAACK,CAAC,GAAC,CAAH,CAAP,CAAaT,IAAb,KAAsB,MAAnC,EAA2C;AAAA;;AACzCI,cAAAA,OAAO,CAACK,CAAC,GAAC,CAAH,CAAP,CAAaF,QAAb,GAAwB,sBAACH,OAAO,CAACK,CAAC,GAAC,CAAH,CAAP,CAAaF,QAAd,gCAA0B,CAA1B,IAA+B,CAAvD;AACD;;AACD;AACD;AACF;;AACD;AACD;;AAED,UAAI9C,CAAC,CAACuC,IAAF,KAAW,IAAf,EAAqB;AACnB,YAAMU,KAAK,GAAGjD,CAAC,CAACwC,KAAhB,CADmB,CAEnB;;AACA,YAAMU,IAAI,GAAGpF,MAAM,CAAC8E,CAAC,GAAC,CAAH,CAAnB;AACA,YAAMO,OAAO,GAAGF,KAAK,KAAK,GAAV,KACdL,CAAC,KAAK,CAAN,IACCM,IAAI,KAAKA,IAAI,CAACX,IAAL,KAAc,IAAd,IAAsBW,IAAI,CAACX,IAAL,KAAc,QAApC,IAAgDW,IAAI,CAACX,IAAL,KAAc,OAAnE,CAFS,CAAhB;AAKA,YAAMa,KAAK,GAAID,OAAO,IAAIF,KAAK,KAAK,GAAtB,GAA6B,KAA7B,GAAqCA,KAAnD;AAEA,YAAI,EAAEG,KAAK,IAAIC,GAAX,CAAJ,EAAqB,MAAM,IAAIzB,KAAJ,wBAA+BqB,KAA/B,OAAN;AAErB,YAAMK,EAAE,GAAGD,GAAG,CAACD,KAAD,CAAd;;AACA,eAAOT,OAAO,CAAC9D,MAAR,GAAiB,CAAxB,EAA2B;AACzB,cAAM0E,GAAG,GAAGZ,OAAO,CAACA,OAAO,CAAC9D,MAAR,GAAiB,CAAlB,CAAnB;AACA,cAAI0E,GAAG,CAAChB,IAAJ,KAAa,IAAjB,EAAuB;AACvB,cAAMiB,EAAE,GAAGH,GAAG,CAACE,GAAG,CAACf,KAAL,CAAd;AACA,cAAI,CAACgB,EAAL,EAAS;;AACT,cAAKF,EAAE,CAACG,KAAH,KAAa,MAAb,IAAuBH,EAAE,CAACI,IAAH,IAAWF,EAAE,CAACE,IAAtC,IAAgDJ,EAAE,CAACG,KAAH,KAAa,OAAb,IAAwBH,EAAE,CAACI,IAAH,GAAUF,EAAE,CAACE,IAAzF,EAAgG;AAC9FhB,YAAAA,MAAM,CAAChE,IAAP,CAAYiE,OAAO,CAACI,GAAR,GAAeP,KAA3B;AACD,WAFD,MAEO;AACR;;AACDG,QAAAA,OAAO,CAACjE,IAAR,CAAa;AAAC6D,UAAAA,IAAI,EAAC,IAAN;AAAYC,UAAAA,KAAK,EAAEY;AAAnB,SAAb;AACA;AACD;;AAED,UAAIpD,CAAC,CAACuC,IAAF,KAAW,QAAf,EAAyB;AACvBI,QAAAA,OAAO,CAACjE,IAAR,CAAa;AAAC6D,UAAAA,IAAI,EAAC,QAAN;AAAgBC,UAAAA,KAAK,EAAC;AAAtB,SAAb,EADuB,CAEvB;;AACA;AACD;;AAED,UAAIxC,CAAC,CAACuC,IAAF,KAAW,QAAf,EAAyB;AACvB;AACA,eAAOI,OAAO,CAAC9D,MAAR,GAAiB,CAAjB,IAAsB8D,OAAO,CAACA,OAAO,CAAC9D,MAAR,GAAe,CAAhB,CAAP,CAA0B0D,IAA1B,KAAmC,QAAhE,EAA0E;AACxE,cAAM5C,GAAE,GAAGgD,OAAO,CAACI,GAAR,EAAX;;AACA,cAAIpD,GAAE,CAAC4C,IAAH,KAAY,IAAhB,EAAsBG,MAAM,CAAChE,IAAP,CAAYiB,GAAE,CAAC6C,KAAf,EAAtB,KACK,IAAI7C,GAAE,CAAC4C,IAAH,KAAY,MAAhB,EAAwB;AAAA;;AAC3B;AACAG,YAAAA,MAAM,CAAChE,IAAP,WAAoBiB,GAAE,CAAC6C,KAAvB,2BAAiC7C,GAAE,CAACmD,QAApC,4BAAgD,CAAhD;AACD;AACF;;AACD,YAAIH,OAAO,CAAC9D,MAAR,KAAmB,CAAvB,EAA0B,MAAM,IAAI+C,KAAJ,CAAU,wBAAV,CAAN,CAVH,CAWvB;;AACAe,QAAAA,OAAO,CAACI,GAAR,GAZuB,CAavB;;AACA,YAAIJ,OAAO,CAAC9D,MAAR,GAAiB,CAAjB,IAAsB8D,OAAO,CAACA,OAAO,CAAC9D,MAAR,GAAe,CAAhB,CAAP,CAA0B0D,IAA1B,KAAmC,MAA7D,EAAqE;AACnE,cAAMoB,EAAE,GAAGhB,OAAO,CAACI,GAAR,EAAX,CADmE,CAEnE;;AACA,cAAMa,MAAM,GAAG9F,MAAM,CAAC8E,CAAC,GAAC,CAAH,CAArB;AACA,cAAIiB,UAAkB,SAAtB;;AAEA,cAAID,MAAM,IAAIA,MAAM,CAACrB,IAAP,KAAgB,QAA9B,EAAwC;AACtC;AACAsB,YAAAA,UAAU,GAAG,CAAb;AACD,WAHD,MAGO;AAAA;;AACL;AACAA,YAAAA,UAAU,mBAAGF,EAAE,CAACb,QAAN,2BAAkB,CAA5B;AACD;;AAEDJ,UAAAA,MAAM,CAAChE,IAAP,WAAoBiF,EAAE,CAACnB,KAAvB,SAAgCqB,UAAhC;AACD;;AACD;AACD;AACF,KApHqE,CAsHtE;;;AACA,WAAOlB,OAAO,CAAC9D,MAAR,GAAiB,CAAxB,EAA2B;AACzB,UAAMc,IAAE,GAAGgD,OAAO,CAACI,GAAR,EAAX;;AACA,UAAIpD,IAAE,CAAC4C,IAAH,KAAY,QAAZ,IAAwB5C,IAAE,CAAC4C,IAAH,KAAY,QAAxC,EAAkD,MAAM,IAAIX,KAAJ,CAAU,wBAAV,CAAN;AAClD,UAAIjC,IAAE,CAAC4C,IAAH,KAAY,IAAhB,EAAsBG,MAAM,CAAChE,IAAP,CAAYiB,IAAE,CAAC6C,KAAf,EAAtB,KACK,IAAI7C,IAAE,CAAC4C,IAAH,KAAY,MAAhB,EAAwB;AAAA;;AAC3B;AACAG,QAAAA,MAAM,CAAChE,IAAP,WAAoBiB,IAAE,CAAC6C,KAAvB,2BAAgC7C,IAAE,CAACmD,QAAnC,4BAA+C,CAA/C;AACD;AACF;;AACD,WAAOJ,MAAP;AACD,G,CAED;;;;;;;;;;;;;;AAjfSoB,MAAAA,U,OAAAA,U;;;;;2EAHT;AACA;AACA;;;;;OAEM;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBF,U,GAM9B;AASA;;kCAQapG,gB,GAAN,MAAMA,gBAAN,CAAuB;AAAA;AAC5B;AAD4B,eAEpBuG,MAFoB,GAEX,IAAI7F,GAAJ,EAFW;AAAA,eAGpB8F,IAHoB,GAGH,EAHG;AAAA;;AAK5BC,QAAAA,QAAQ,CAACpF,IAAD,EAAeyC,KAAf,EAA8B4C,IAA9B,EAAmD;AACzD,cAAI,CAACC,MAAM,CAACC,SAAP,CAAiB9C,KAAjB,CAAD,IAA4BA,KAAK,GAAG,CAAxC,EAA2C,MAAM,IAAII,KAAJ,CAAU,oBAAV,CAAN;AAC3C,cAAI2C,GAAG,GAAG,KAAKN,MAAL,CAAYhF,GAAZ,CAAgBF,IAAhB,CAAV;;AACA,cAAI,CAACwF,GAAL,EAAU;AAAEA,YAAAA,GAAG,GAAG,IAAInG,GAAJ,EAAN;AAAiB,iBAAK6F,MAAL,CAAY9E,GAAZ,CAAgBJ,IAAhB,EAAsBwF,GAAtB;AAA6B;;AAC1D,cAAMC,QAAQ,GAAGD,GAAG,CAACtF,GAAJ,CAAQuC,KAAR,CAAjB;;AACA,cAAIgD,QAAJ,EAAc;AACZA,YAAAA,QAAQ,CAACJ,IAAT,GAAgBA,IAAhB;AACA,mBAAOI,QAAP;AACD;;AACD,cAAM9C,IAAY,GAAG;AAAEI,YAAAA,EAAE,EAAE,KAAKoC,IAAL,CAAUrF,MAAhB;AAAwBE,YAAAA,IAAxB;AAA8ByC,YAAAA,KAA9B;AAAqC4C,YAAAA;AAArC,WAArB;AACAG,UAAAA,GAAG,CAACpF,GAAJ,CAAQqC,KAAR,EAAeE,IAAf;AACA,eAAKwC,IAAL,CAAUxF,IAAV,CAAegD,IAAf;AACA,iBAAOA,IAAP;AACD;;AAEDC,QAAAA,iBAAiB,CAAC5C,IAAD,EAAeyC,KAAf,EAAkD;AACjE,cAAM+C,GAAG,GAAG,KAAKN,MAAL,CAAYhF,GAAZ,CAAgBF,IAAhB,CAAZ;AACA,iBAAOwF,GAAG,GAAGA,GAAG,CAACtF,GAAJ,CAAQuC,KAAR,CAAH,GAAoBtC,SAA9B;AACD,SAvB2B,CAyB5B;;;AACAuF,QAAAA,eAAe,CAAC1F,IAAD,EAAmC;AAChD,cAAMwF,GAAG,GAAG,KAAKN,MAAL,CAAYhF,GAAZ,CAAgBF,IAAhB,CAAZ;AACA,cAAI,CAACwF,GAAL,EAAU,OAAOrF,SAAP;AACV,cAAIqF,GAAG,CAACG,IAAJ,KAAa,CAAjB,EAAoB,OAAOC,KAAK,CAACC,IAAN,CAAWL,GAAG,CAACM,MAAJ,EAAX,EAAyB,CAAzB,CAAP;AACpB,iBAAO3F,SAAP;AACD;;AAED4F,QAAAA,OAAO,CAAChD,EAAD,EAAqB;AAC1B,iBAAO,KAAKoC,IAAL,CAAUpC,EAAV,CAAP;AACD;;AAnC2B,O,GAsC9B;;;AACKnD,MAAAA,M,0BAAAA,M;AAAAA,QAAAA,M,CAAAA,M;AAAAA,QAAAA,M,CAAAA,M;AAAAA,QAAAA,M,CAAAA,M;AAAAA,QAAAA,M,CAAAA,M;AAAAA,QAAAA,M,CAAAA,M;AAAAA,QAAAA,M,CAAAA,M;AAAAA,QAAAA,M,CAAAA,M;AAAAA,QAAAA,M,CAAAA,M;AAAAA,QAAAA,M,CAAAA,M;AAAAA,QAAAA,M,CAAAA,M;AAAAA,QAAAA,M,CAAAA,M;AAAAA,QAAAA,M,CAAAA,M;AAAAA,QAAAA,M,CAAAA,M;AAAAA,QAAAA,M,CAAAA,M;AAAAA,QAAAA,M,CAAAA,M;AAAAA,QAAAA,M,CAAAA,M;AAAAA,QAAAA,M,CAAAA,M;AAAAA,QAAAA,M,CAAAA,M;AAAAA,QAAAA,M,CAAAA,M;AAAAA,QAAAA,M,CAAAA,M;eAAAA,M;QAAAA,M;;4BAWQhB,U,GAAN,MAAMA,UAAN,CAAiB;AAQtBoH,QAAAA,WAAW,CAAC9G,IAAD,EAAiBC,MAAjB,EAAqCG,QAArC,EAAyDE,SAAzD,EAAgFR,QAAhF,EAA4G+B,UAA5G,EAA+H;AAAA,cAAnBA,UAAmB;AAAnBA,YAAAA,UAAmB,GAAN,IAAM;AAAA;;AAAA,eAPzH7B,IAOyH;AAAA,eANzHC,MAMyH;AAAA,eALzHG,QAKyH;AAAA,eAJzHE,SAIyH;AAJlG;AAIkG,eAHzHR,QAGyH;AAAA,eAFzH+B,UAEyH;AACxI,eAAK7B,IAAL,GAAYA,IAAZ;AACA,eAAKC,MAAL,GAAcA,MAAd;AACA,eAAKG,QAAL,GAAgBA,QAAhB;AACA,eAAKE,SAAL,GAAiBA,SAAjB;AACA,eAAKR,QAAL,GAAgBA,QAAhB;AACA,eAAK+B,UAAL,GAAkBA,UAAlB;AACD;;AAEDkF,QAAAA,QAAQ,CAACC,GAAD,EAA4B;AAClC,cAAMC,OAAmB,GAAG,IAAIP,KAAJ,CAAU,KAAKtG,QAAL,CAAcQ,MAAxB,CAA5B;;AACA,eAAK,IAAI+D,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKvE,QAAL,CAAcQ,MAAlC,EAA0C+D,CAAC,EAA3C,EAA+C;AAC7C,gBAAM7D,KAAI,GAAG,KAAKV,QAAL,CAAcuE,CAAd,CAAb;AACA,gBAAMnE,CAAC,GAAGwG,GAAG,CAAClG,KAAD,CAAb;;AACA,gBAAIN,CAAC,KAAKS,SAAV,EAAqB;AACnB,kBAAI,KAAKY,UAAT,EAAqB,MAAM,IAAI8B,KAAJ,wBAA+B7C,KAA/B,mBAAN;AACrBmG,cAAAA,OAAO,CAACtC,CAAD,CAAP,GAAa,CAAb;AACD,aAHD,MAGO;AACLsC,cAAAA,OAAO,CAACtC,CAAD,CAAP,GAAanE,CAAb;AACD;AACF;;AAED,cAAM0G,KAAiB,GAAG,EAA1B;AACA,cAAMlH,IAAI,GAAG,KAAKA,IAAlB;AACA,cAAImH,EAAE,GAAG,CAAT;;AACA,iBAAOA,EAAE,GAAGnH,IAAI,CAACY,MAAjB,EAAyB;AACvB,oBAAQZ,IAAI,CAACmH,EAAE,EAAH,CAAZ;AACE,mBAAKzG,MAAM,CAACC,UAAZ;AAAwB;AACtB,sBAAMI,GAAG,GAAGf,IAAI,CAACmH,EAAE,EAAH,CAAhB;AAAwBD,kBAAAA,KAAK,CAACzG,IAAN,CAAW,KAAKR,MAAL,CAAYc,GAAZ,CAAX;AAA8B;AACvD;;AACD,mBAAKL,MAAM,CAACS,QAAZ;AAAsB;AACpB,sBAAMJ,IAAG,GAAGf,IAAI,CAACmH,EAAE,EAAH,CAAhB;AAAwBD,kBAAAA,KAAK,CAACzG,IAAN,CAAWwG,OAAO,CAAClG,IAAD,CAAlB;AAA0B;AACnD;;AACD,mBAAKL,MAAM,CAACc,SAAZ;AAAuB;AACrB,sBAAMT,KAAG,GAAGf,IAAI,CAACmH,EAAE,EAAH,CAAhB;AACA,sBAAM9F,IAAI,GAAG,KAAKf,SAAL,CAAeS,KAAf,CAAb;AACA,sBAAIwD,KAAU,GAAGyC,GAAjB;;AACA,uBAAK,IAAMI,IAAX,IAAmB/F,IAAnB,EAAyB;AACvB,wBAAIkD,KAAK,IAAI,OAAOA,KAAP,KAAiB,QAA1B,IAAsC6C,IAAI,IAAI7C,KAAlD,EAAyD;AACvDA,sBAAAA,KAAK,GAAGA,KAAK,CAAC6C,IAAD,CAAb;AACD,qBAFD,MAEO;AACL,0BAAI,KAAKvF,UAAT,EAAqB,MAAM,IAAI8B,KAAJ,wBAA+BtC,IAAI,CAACE,IAAL,CAAU,GAAV,CAA/B,mBAAN;AACrBgD,sBAAAA,KAAK,GAAG,CAAR;AACA;AACD;AACF;;AACD2C,kBAAAA,KAAK,CAACzG,IAAN,CAAW,OAAO8D,KAAP,KAAiB,QAAjB,IAA6B,OAAOA,KAAP,KAAiB,SAA9C,GAA0DA,KAA1D,GAAkE,CAA7E;AACA;AACD;;AACD,mBAAK7D,MAAM,CAAC0B,GAAZ;AAAiB;AAAE,sBAAMiF,CAAC,GAAGjB,MAAM,CAACc,KAAK,CAACpC,GAAN,EAAD,CAAhB;AAA+B,sBAAMwC,CAAC,GAAGlB,MAAM,CAACc,KAAK,CAACpC,GAAN,EAAD,CAAhB;AAA+BoC,kBAAAA,KAAK,CAACzG,IAAN,CAAW6G,CAAC,GAAGD,CAAf;AAAmB;AAAQ;;AAC5G,mBAAK3G,MAAM,CAAC2B,GAAZ;AAAiB;AAAE,sBAAMgF,EAAC,GAAGjB,MAAM,CAACc,KAAK,CAACpC,GAAN,EAAD,CAAhB;;AAA+B,sBAAMwC,EAAC,GAAGlB,MAAM,CAACc,KAAK,CAACpC,GAAN,EAAD,CAAhB;;AAA+BoC,kBAAAA,KAAK,CAACzG,IAAN,CAAW6G,EAAC,GAAGD,EAAf;AAAmB;AAAQ;;AAC5G,mBAAK3G,MAAM,CAAC4B,GAAZ;AAAiB;AAAE,sBAAM+E,GAAC,GAAGjB,MAAM,CAACc,KAAK,CAACpC,GAAN,EAAD,CAAhB;;AAA+B,sBAAMwC,GAAC,GAAGlB,MAAM,CAACc,KAAK,CAACpC,GAAN,EAAD,CAAhB;;AAA+BoC,kBAAAA,KAAK,CAACzG,IAAN,CAAW6G,GAAC,GAAGD,GAAf;AAAmB;AAAQ;;AAC5G,mBAAK3G,MAAM,CAAC6B,GAAZ;AAAiB;AAAE,sBAAM8E,GAAC,GAAGjB,MAAM,CAACc,KAAK,CAACpC,GAAN,EAAD,CAAhB;;AAA+B,sBAAMwC,GAAC,GAAGlB,MAAM,CAACc,KAAK,CAACpC,GAAN,EAAD,CAAhB;;AAA+BoC,kBAAAA,KAAK,CAACzG,IAAN,CAAW6G,GAAC,GAAGD,GAAf;AAAmB;AAAQ;;AAC5G,mBAAK3G,MAAM,CAAC8B,GAAZ;AAAiB;AAAE,sBAAM6E,GAAC,GAAGjB,MAAM,CAACc,KAAK,CAACpC,GAAN,EAAD,CAAhB;;AAA+B,sBAAMwC,GAAC,GAAGlB,MAAM,CAACc,KAAK,CAACpC,GAAN,EAAD,CAAhB;;AAA+BoC,kBAAAA,KAAK,CAACzG,IAAN,CAAW6G,GAAC,GAAGD,GAAf;AAAmB;AAAQ;;AAC5G,mBAAK3G,MAAM,CAAC+B,GAAZ;AAAiB;AAAE,sBAAM4E,GAAC,GAAGjB,MAAM,CAACc,KAAK,CAACpC,GAAN,EAAD,CAAhB;;AAA+B,sBAAMwC,GAAC,GAAGlB,MAAM,CAACc,KAAK,CAACpC,GAAN,EAAD,CAAhB;;AAA+BoC,kBAAAA,KAAK,CAACzG,IAAN,CAAW8G,IAAI,CAACC,GAAL,CAASF,GAAT,EAAYD,GAAZ,CAAX;AAA4B;AAAQ;;AACrH,mBAAK3G,MAAM,CAACgC,GAAZ;AAAiB;AAAE,sBAAM4E,GAAC,GAAGlB,MAAM,CAACc,KAAK,CAACpC,GAAN,EAAD,CAAhB;;AAA+BoC,kBAAAA,KAAK,CAACzG,IAAN,CAAW,CAAC6G,GAAZ;AAAgB;AAAQ;;AAC1E,mBAAK5G,MAAM,CAACiC,EAAZ;AAAgB;AAAE,sBAAM0E,GAAC,GAAGjB,MAAM,CAACc,KAAK,CAACpC,GAAN,EAAD,CAAhB;;AAA+B,sBAAMwC,GAAC,GAAGlB,MAAM,CAACc,KAAK,CAACpC,GAAN,EAAD,CAAhB;;AAA+BoC,kBAAAA,KAAK,CAACzG,IAAN,CAAW6G,GAAC,GAAGD,GAAf;AAAmB;AAAQ;;AAC3G,mBAAK3G,MAAM,CAACkC,EAAZ;AAAgB;AAAE,sBAAMyE,GAAC,GAAGjB,MAAM,CAACc,KAAK,CAACpC,GAAN,EAAD,CAAhB;;AAA+B,sBAAMwC,GAAC,GAAGlB,MAAM,CAACc,KAAK,CAACpC,GAAN,EAAD,CAAhB;;AAA+BoC,kBAAAA,KAAK,CAACzG,IAAN,CAAW6G,GAAC,IAAID,GAAhB;AAAoB;AAAQ;;AAC5G,mBAAK3G,MAAM,CAACmC,EAAZ;AAAgB;AAAE,sBAAMwE,GAAC,GAAGjB,MAAM,CAACc,KAAK,CAACpC,GAAN,EAAD,CAAhB;;AAA+B,sBAAMwC,GAAC,GAAGlB,MAAM,CAACc,KAAK,CAACpC,GAAN,EAAD,CAAhB;;AAA+BoC,kBAAAA,KAAK,CAACzG,IAAN,CAAW6G,GAAC,GAAGD,GAAf;AAAmB;AAAQ;;AAC3G,mBAAK3G,MAAM,CAACoC,EAAZ;AAAgB;AAAE,sBAAMuE,GAAC,GAAGjB,MAAM,CAACc,KAAK,CAACpC,GAAN,EAAD,CAAhB;;AAA+B,sBAAMwC,IAAC,GAAGlB,MAAM,CAACc,KAAK,CAACpC,GAAN,EAAD,CAAhB;;AAA+BoC,kBAAAA,KAAK,CAACzG,IAAN,CAAW6G,IAAC,IAAID,GAAhB;AAAoB;AAAQ;;AAC5G,mBAAK3G,MAAM,CAACqC,EAAZ;AAAgB;AAAE,sBAAMsE,IAAC,GAAGH,KAAK,CAACpC,GAAN,EAAV;;AAAuB,sBAAMwC,IAAC,GAAGJ,KAAK,CAACpC,GAAN,EAAV;;AAAuBoC,kBAAAA,KAAK,CAACzG,IAAN,CAAW6G,IAAC,KAAKD,IAAjB;AAAqB;AAAQ;;AAC7F,mBAAK3G,MAAM,CAACsC,GAAZ;AAAiB;AAAE,sBAAMqE,IAAC,GAAGH,KAAK,CAACpC,GAAN,EAAV;;AAAuB,sBAAMwC,IAAC,GAAGJ,KAAK,CAACpC,GAAN,EAAV;;AAAuBoC,kBAAAA,KAAK,CAACzG,IAAN,CAAW6G,IAAC,KAAKD,IAAjB;AAAqB;AAAQ;;AAC9F,mBAAK3G,MAAM,CAACuC,GAAZ;AAAiB;AAAE,sBAAMoE,IAAC,GAAGI,OAAO,CAACP,KAAK,CAACpC,GAAN,EAAD,CAAjB;;AAAgC,sBAAMwC,IAAC,GAAGG,OAAO,CAACP,KAAK,CAACpC,GAAN,EAAD,CAAjB;;AAAgCoC,kBAAAA,KAAK,CAACzG,IAAN,CAAW6G,IAAC,IAAID,IAAhB;AAAoB;AAAQ;;AAC/G,mBAAK3G,MAAM,CAACwC,EAAZ;AAAgB;AAAE,sBAAMmE,IAAC,GAAGI,OAAO,CAACP,KAAK,CAACpC,GAAN,EAAD,CAAjB;;AAAgC,sBAAMwC,IAAC,GAAGG,OAAO,CAACP,KAAK,CAACpC,GAAN,EAAD,CAAjB;;AAAgCoC,kBAAAA,KAAK,CAACzG,IAAN,CAAW6G,IAAC,IAAID,IAAhB;AAAoB;AAAQ;;AAC9G,mBAAK3G,MAAM,CAACyC,GAAZ;AAAiB;AAAE,sBAAMmE,IAAC,GAAGG,OAAO,CAACP,KAAK,CAACpC,GAAN,EAAD,CAAjB;;AAAgCoC,kBAAAA,KAAK,CAACzG,IAAN,CAAW,CAAC6G,IAAZ;AAAgB;AAAQ;;AAC3E,mBAAK5G,MAAM,CAACkD,IAAZ;AAAkB;AAChB,sBAAM8D,IAAI,GAAG1H,IAAI,CAACmH,EAAE,EAAH,CAAjB;AAAyB,sBAAM5D,KAAK,GAAGvD,IAAI,CAACmH,EAAE,EAAH,CAAlB;;AACzB,sBAAMQ,KAAI,GAAG,IAAIjB,KAAJ,CAAoBnD,KAApB,CAAb;;AACA,uBAAK,IAAIoB,EAAC,GAAGpB,KAAK,GAAG,CAArB,EAAwBoB,EAAC,IAAI,CAA7B,EAAgCA,EAAC,EAAjC,EAAqCgD,KAAI,CAAChD,EAAD,CAAJ,GAAUuC,KAAK,CAACpC,GAAN,EAAV;;AACrC,sBAAMrB,IAAI,GAAG,KAAK3D,QAAL,CAAc+G,OAAd,CAAsBa,IAAtB,CAAb;AACA,sBAAMzD,GAAG,GAAGR,IAAI,CAAC0C,IAAL,CAAUwB,KAAV,CAAZ;AACAT,kBAAAA,KAAK,CAACzG,IAAN,CAAWwD,GAAX;AACA;AACD;;AACD;AAAS,sBAAM,IAAIN,KAAJ,qBAA6B3D,IAAI,CAACmH,EAAE,GAAC,CAAJ,CAAjC,CAAN;AAhDX;AAkDD;;AAED,cAAID,KAAK,CAACtG,MAAN,KAAiB,CAArB,EAAwB,MAAM,IAAI+C,KAAJ,iCAAwCuD,KAAK,CAACtG,MAA9C,cAAN;AACxB,iBAAOsG,KAAK,CAAC,CAAD,CAAZ;AACD;;AAEDU,QAAAA,YAAY,GAAsB;AAAE,iBAAO,KAAKxH,QAAZ;AAAuB,SA1FrC,CA4FtB;;;AACAyH,QAAAA,SAAS,GAAyB;AAChC,iBAAO;AACL7H,YAAAA,IAAI,EAAE,CAAC,GAAG,KAAKA,IAAT,CADD;AAELC,YAAAA,MAAM,EAAE,CAAC,GAAG,KAAKA,MAAT,CAFH;AAGLG,YAAAA,QAAQ,EAAE,CAAC,GAAG,KAAKA,QAAT,CAHL;AAILE,YAAAA,SAAS,EAAE,KAAKA,SAAL,CAAegG,GAAf,CAAmBjF,IAAI,IAAI,CAAC,GAAGA,IAAJ,CAA3B,CAJN;AAKLQ,YAAAA,UAAU,EAAE,KAAKA;AALZ,WAAP;AAOD,SArGqB,CAuGtB;;;AACkB,eAAXiG,WAAW,CAACC,IAAD,EAA6BjI,QAA7B,EAAqE;AACrF,iBAAO,IAAIJ,UAAJ,CACLqI,IAAI,CAAC/H,IADA,EAEL+H,IAAI,CAAC9H,MAFA,EAGL8H,IAAI,CAAC3H,QAHA,EAIL2H,IAAI,CAACzH,SAJA,EAKLR,QALK,EAMLiI,IAAI,CAAClG,UANA,CAAP;AAQD;;AAjHqB,O,GAoHxB;;;wCAEamG,sB,WADZlC,OAAO,CAAC,wBAAD,C,UAELC,QAAQ,CAAC;AAAEzB,QAAAA,IAAI,EAAE,CAAC8B,MAAD,CAAR;AAAkB6B,QAAAA,OAAO,EAAE;AAA3B,OAAD,C,UAGRlC,QAAQ,CAAC;AAAEkC,QAAAA,OAAO,EAAE;AAAX,OAAD,C,UAGRlC,QAAQ,CAAC;AAAEzB,QAAAA,IAAI,EAAE,CAAC4D,MAAD,CAAR;AAAkBD,QAAAA,OAAO,EAAE;AAA3B,OAAD,C,UAGRlC,QAAQ,CAAC;AAAEkC,QAAAA,OAAO,EAAE;AAAX,OAAD,C,UAGRlC,QAAQ,CAAC;AAAEkC,QAAAA,OAAO,EAAE;AAAX,OAAD,C,4BAdX,MACaD,sBADb,CACoC;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;AAAA;;AAgBlC;AACAG,QAAAA,YAAY,CAACrI,QAAD,EAAyC;AACnD,iBAAOJ,UAAU,CAACoI,WAAX,CAAuB;AAC5B9H,YAAAA,IAAI,EAAE,KAAKA,IADiB;AAE5BC,YAAAA,MAAM,EAAE,KAAKA,MAFe;AAG5BG,YAAAA,QAAQ,EAAE,KAAKA,QAHa;AAI5BE,YAAAA,SAAS,EAAE,KAAKA,SAJY;AAK5BuB,YAAAA,UAAU,EAAE,KAAKA;AALW,WAAvB,EAMJ/B,QANI,CAAP;AAOD,SAzBiC,CA2BlC;;;AACqB,eAAdsI,cAAc,CAACC,OAAD,EAA8C;AACjE,cAAMC,YAAY,GAAG,IAAIN,sBAAJ,EAArB;AACA,cAAMD,IAAI,GAAGM,OAAO,CAACR,SAAR,EAAb;AACAS,UAAAA,YAAY,CAACtI,IAAb,GAAoB+H,IAAI,CAAC/H,IAAzB;AACAsI,UAAAA,YAAY,CAACrI,MAAb,GAAsB8H,IAAI,CAAC9H,MAA3B;AACAqI,UAAAA,YAAY,CAAClI,QAAb,GAAwB2H,IAAI,CAAC3H,QAA7B;AACAkI,UAAAA,YAAY,CAAChI,SAAb,GAAyByH,IAAI,CAACzH,SAA9B;AACAgI,UAAAA,YAAY,CAACzG,UAAb,GAA0BkG,IAAI,CAAClG,UAA/B;AACA,iBAAOyG,YAAP;AACD,SArCiC,CAuClC;;;AACAC,QAAAA,UAAU,GAAY;AACpB,iBAAO,KAAKvI,IAAL,CAAUY,MAAV,GAAmB,CAA1B;AACD;;AA1CiC,O;;;;;iBAEV,E;;;;;;;iBAGc,E;;;;;;;iBAGV,E;;;;;;;iBAGG,E;;;;;;;iBAGF,I;;mCA+B/B;;;AAGMqB,MAAAA,M,GAAS,2B;AA0GTmD,MAAAA,G,GAA8B;AAClC;AACA,aAAK;AAAEK,UAAAA,IAAI,EAAE,CAAR;AAAWD,UAAAA,KAAK,EAAE;AAAlB,SAF6B;AAGlC,eAAO;AAAEC,UAAAA,IAAI,EAAE,CAAR;AAAWD,UAAAA,KAAK,EAAE,OAAlB;AAA2BgD,UAAAA,KAAK,EAAE;AAAlC,SAH2B;AAIlC,aAAK;AAAE/C,UAAAA,IAAI,EAAE,CAAR;AAAWD,UAAAA,KAAK,EAAE,OAAlB;AAA2BgD,UAAAA,KAAK,EAAE;AAAlC,SAJ6B;AAKlC,aAAK;AAAE/C,UAAAA,IAAI,EAAE,CAAR;AAAWD,UAAAA,KAAK,EAAE;AAAlB,SAL6B;AAMlC,aAAK;AAAEC,UAAAA,IAAI,EAAE,CAAR;AAAWD,UAAAA,KAAK,EAAE;AAAlB,SAN6B;AAOlC,aAAK;AAAEC,UAAAA,IAAI,EAAE,CAAR;AAAWD,UAAAA,KAAK,EAAE;AAAlB,SAP6B;AAQlC,aAAK;AAAEC,UAAAA,IAAI,EAAE,CAAR;AAAWD,UAAAA,KAAK,EAAE;AAAlB,SAR6B;AASlC,aAAK;AAAEC,UAAAA,IAAI,EAAE,CAAR;AAAWD,UAAAA,KAAK,EAAE;AAAlB,SAT6B;AAUlC,aAAK;AAAEC,UAAAA,IAAI,EAAE,CAAR;AAAWD,UAAAA,KAAK,EAAE;AAAlB,SAV6B;AAWlC,cAAM;AAAEC,UAAAA,IAAI,EAAE,CAAR;AAAWD,UAAAA,KAAK,EAAE;AAAlB,SAX4B;AAYlC,aAAK;AAAEC,UAAAA,IAAI,EAAE,CAAR;AAAWD,UAAAA,KAAK,EAAE;AAAlB,SAZ6B;AAalC,cAAM;AAAEC,UAAAA,IAAI,EAAE,CAAR;AAAWD,UAAAA,KAAK,EAAE;AAAlB,SAb4B;AAclC,cAAM;AAAEC,UAAAA,IAAI,EAAE,CAAR;AAAWD,UAAAA,KAAK,EAAE;AAAlB,SAd4B;AAelC,cAAM;AAAEC,UAAAA,IAAI,EAAE,CAAR;AAAWD,UAAAA,KAAK,EAAE;AAAlB,SAf4B;AAgBlC,cAAM;AAAEC,UAAAA,IAAI,EAAE,CAAR;AAAWD,UAAAA,KAAK,EAAE;AAAlB,SAhB4B;AAiBlC,cAAM;AAAEC,UAAAA,IAAI,EAAE,CAAR;AAAWD,UAAAA,KAAK,EAAE;AAAlB;AAjB4B,O;;+BAwJvB7F,a,GAAN,MAAMA,aAAN,CAAoB;AAGzBmH,QAAAA,WAAW,CAAC2B,gBAAD,EAA0B;AAAA,cAAzBA,gBAAyB;AAAzBA,YAAAA,gBAAyB,GAAN,IAAM;AAAA;;AAAA,eAF5B3I,QAE4B,GAFjB,IAAIL,gBAAJ,EAEiB;AACnC,cAAIgJ,gBAAJ,EAAsB,KAAKA,gBAAL;AACvB;;AAEDC,QAAAA,OAAO,CAAC1E,IAAD,EAA0BjE,IAA1B,EAA6D;AAClE,cAAIF,MAAJ;AACA,cAAI6G,KAAK,CAACiC,OAAN,CAAc3E,IAAd,CAAJ,EAAyBnE,MAAM,GAAGmE,IAAT,CAAzB,KACK;AACHnE,YAAAA,MAAM,GAAG2E,UAAU,CAACR,IAAD,EAAO,KAAKlE,QAAZ,CAAnB;AACD;AACD,iBAAOF,kBAAkB,CAACC,MAAD,EAAS,KAAKC,QAAd,EAAwBC,IAAxB,CAAzB;AACD;;AAEDmG,QAAAA,QAAQ,CAACpF,IAAD,EAAeyC,KAAf,EAA8B4C,IAA9B,EAAiD;AACvD,eAAKrG,QAAL,CAAcoG,QAAd,CAAuBpF,IAAvB,EAA6ByC,KAA7B,EAAoC4C,IAApC;AACD;;AAEOsC,QAAAA,gBAAgB,GAAS;AAC/B,cAAMG,CAAC,GAAG,KAAK9I,QAAf,CAD+B,CAE/B;;AACA8I,UAAAA,CAAC,CAAC1C,QAAF,CAAW,KAAX,EAAkB,CAAlB,EAAqB;AAAA,gBAAC,CAACoB,CAAD,CAAD;AAAA,mBAASC,IAAI,CAACsB,GAAL,CAASzC,MAAM,CAACkB,CAAD,CAAf,CAAT;AAAA,WAArB;AACAsB,UAAAA,CAAC,CAAC1C,QAAF,CAAW,OAAX,EAAoB,CAApB,EAAuB;AAAA,gBAAC,CAACoB,CAAD,CAAD;AAAA,mBAASC,IAAI,CAACuB,KAAL,CAAW1C,MAAM,CAACkB,CAAD,CAAjB,CAAT;AAAA,WAAvB;AACAsB,UAAAA,CAAC,CAAC1C,QAAF,CAAW,MAAX,EAAmB,CAAnB,EAAsB;AAAA,gBAAC,CAACoB,CAAD,CAAD;AAAA,mBAASC,IAAI,CAACwB,IAAL,CAAU3C,MAAM,CAACkB,CAAD,CAAhB,CAAT;AAAA,WAAtB;AACAsB,UAAAA,CAAC,CAAC1C,QAAF,CAAW,OAAX,EAAoB,CAApB,EAAuB;AAAA,gBAAC,CAACoB,CAAD,CAAD;AAAA,mBAASC,IAAI,CAACyB,KAAL,CAAW5C,MAAM,CAACkB,CAAD,CAAjB,CAAT;AAAA,WAAvB;AACAsB,UAAAA,CAAC,CAAC1C,QAAF,CAAW,KAAX,EAAkB,CAAlB,EAAqB;AAAA,gBAAC,CAACoB,CAAD,CAAD;AAAA,mBAASC,IAAI,CAAC0B,GAAL,CAAS7C,MAAM,CAACkB,CAAD,CAAf,CAAT;AAAA,WAArB;AACAsB,UAAAA,CAAC,CAAC1C,QAAF,CAAW,KAAX,EAAkB,CAAlB,EAAqB;AAAA,gBAAC,CAACoB,CAAD,CAAD;AAAA,mBAASC,IAAI,CAAC2B,GAAL,CAAS9C,MAAM,CAACkB,CAAD,CAAf,CAAT;AAAA,WAArB;AACAsB,UAAAA,CAAC,CAAC1C,QAAF,CAAW,KAAX,EAAkB,CAAlB,EAAqB;AAAA,gBAAC,CAACoB,CAAD,CAAD;AAAA,mBAASC,IAAI,CAAC4B,GAAL,CAAS/C,MAAM,CAACkB,CAAD,CAAf,CAAT;AAAA,WAArB,EAT+B,CAU/B;;AACAsB,UAAAA,CAAC,CAAC1C,QAAF,CAAW,KAAX,EAAkB,CAAlB,EAAqB;AAAA,gBAAC,CAACoB,CAAD,EAAID,CAAJ,CAAD;AAAA,mBAAYE,IAAI,CAAC6B,GAAL,CAAShD,MAAM,CAACkB,CAAD,CAAf,EAAoBlB,MAAM,CAACiB,CAAD,CAA1B,CAAZ;AAAA,WAArB;AACAuB,UAAAA,CAAC,CAAC1C,QAAF,CAAW,KAAX,EAAkB,CAAlB,EAAqB;AAAA,gBAAC,CAACoB,CAAD,EAAID,CAAJ,CAAD;AAAA,mBAAYE,IAAI,CAAC8B,GAAL,CAASjD,MAAM,CAACkB,CAAD,CAAf,EAAoBlB,MAAM,CAACiB,CAAD,CAA1B,CAAZ;AAAA,WAArB,EAZ+B,CAa/B;;AACAuB,UAAAA,CAAC,CAAC1C,QAAF,CAAW,OAAX,EAAoB,CAApB,EAAuB;AAAA,gBAAC,CAACoD,CAAD,EAAIC,EAAJ,EAAQC,EAAR,CAAD;AAAA,mBAAiBjC,IAAI,CAAC8B,GAAL,CAASjD,MAAM,CAACmD,EAAD,CAAf,EAAqBhC,IAAI,CAAC6B,GAAL,CAAShD,MAAM,CAACoD,EAAD,CAAf,EAAqBpD,MAAM,CAACkD,CAAD,CAA3B,CAArB,CAAjB;AAAA,WAAvB,EAd+B,CAe/B;;AACAV,UAAAA,CAAC,CAAC1C,QAAF,CAAW,MAAX,EAAmB,CAAnB,EAAsB,MAAMqB,IAAI,CAACkC,MAAL,EAA5B;AACAb,UAAAA,CAAC,CAAC1C,QAAF,CAAW,MAAX,EAAmB,CAAnB,EAAsB,UAAY;AAAA,gBAAX,CAACoB,CAAD,EAAID,CAAJ,CAAW;AAChC,gBAAMqC,CAAC,GAAGtD,MAAM,CAACkB,CAAD,CAAhB;AAAA,gBAAqBqC,CAAC,GAAGvD,MAAM,CAACiB,CAAD,CAA/B;AAAoC,mBAAOqC,CAAC,GAAGnC,IAAI,CAACkC,MAAL,MAAiBE,CAAC,GAAGD,CAArB,CAAX;AACrC,WAFD,EAjB+B,CAoB/B;AACA;;AACAd,UAAAA,CAAC,CAAC1C,QAAF,CAAW,KAAX,EAAkB,CAAlB,EAAqB;AAAA,gBAAC,CAAC0D,IAAD,EAAOtC,CAAP,EAAUD,CAAV,CAAD;AAAA,mBAAmBuC,IAAI,GAAGtC,CAAH,GAAOD,CAA9B;AAAA,WAArB,EAtB+B,CAuB/B;;AACAuB,UAAAA,CAAC,CAAC1C,QAAF,CAAW,IAAX,EAAiB,CAAjB,EAAoB;AAAA,gBAAC,CAAC0D,IAAD,EAAOrF,KAAP,CAAD;AAAA,mBAAoBqF,IAAI,GAAGrF,KAAH,GAAW,CAAnC;AAAA,WAApB,EAxB+B,CAyB/B;;AACAqE,UAAAA,CAAC,CAAC1C,QAAF,CAAW,QAAX,EAAqB,CAArB,EAAwB;AAAA,gBAAC,CAAC0D,IAAD,EAAOtC,CAAP,EAAUD,CAAV,CAAD;AAAA,mBAAmBuC,IAAI,GAAGtC,CAAH,GAAOD,CAA9B;AAAA,WAAxB,EA1B+B,CA2B/B;AACA;AACA;AACA;;AAEAuB,UAAAA,CAAC,CAAC1C,QAAF,CAAW,MAAX,EAAmB,CAAnB,EAAsB;AAAA,gBAAC,CAACoD,CAAD,CAAD;AAAA,mBAAS7B,OAAO,CAAC6B,CAAD,CAAhB;AAAA,WAAtB,EAhC+B,CAkC/B;;AACAV,UAAAA,CAAC,CAAC1C,QAAF,CAAW,MAAX,EAAmB,CAAnB,EAAsB;AAAA,gBAAC,CAACoB,CAAD,EAAGD,CAAH,EAAKtF,CAAL,CAAD;AAAA,mBAAaqE,MAAM,CAACkB,CAAD,CAAN,GAAY,CAAClB,MAAM,CAACiB,CAAD,CAAN,GAAUjB,MAAM,CAACkB,CAAD,CAAjB,IAAwBlB,MAAM,CAACrE,CAAD,CAAvD;AAAA,WAAtB;AACD;;AAxDwB,O,GA2D3B;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;;yBACepC,a", "sourcesContent": ["// rpn_calculator_infix.ts\r\n// Complete TypeScript: Infix -> RPN -> Bytecode VM\r\n// Usage example at bottom.\r\nimport { _decorator, Enum } from \"cc\";\r\nconst { ccclass, property } = _decorator;\r\n\r\nexport type RPNValue = number | boolean;\r\nexport interface RPNContext { [name: string]: RPNValue | any | undefined; }\r\nexport type RPNFn = (args: RPNValue[]) => RPNValue;\r\n\r\n// Serializable program data for editor storage\r\nexport interface SerializedRPNProgram {\r\n  code: number[];\r\n  consts: RPNValue[];\r\n  varNames: string[];\r\n  propPaths: string[][];\r\n  strictVars: boolean;\r\n}\r\n\r\n// -------- Function registry (supporting multiple arities per name) --------\r\ninterface FnMeta {\r\n  id: number;\r\n  name: string;\r\n  arity: number;\r\n  impl: RPNFn;\r\n}\r\n\r\nexport class FunctionRegistry {\r\n  // name -> (arity -> FnMeta)\r\n  private byName = new Map<string, Map<number, FnMeta>>();\r\n  private byId: FnMeta[] = [];\r\n\r\n  register(name: string, arity: number, impl: RPNFn): FnMeta {\r\n    if (!Number.isInteger(arity) || arity < 0) throw new Error('arity must be >= 0');\r\n    let map = this.byName.get(name);\r\n    if (!map) { map = new Map(); this.byName.set(name, map); }\r\n    const existing = map.get(arity);\r\n    if (existing) {\r\n      existing.impl = impl;\r\n      return existing;\r\n    }\r\n    const meta: FnMeta = { id: this.byId.length, name, arity, impl };\r\n    map.set(arity, meta);\r\n    this.byId.push(meta);\r\n    return meta;\r\n  }\r\n\r\n  getByNameAndArity(name: string, arity: number): FnMeta | undefined {\r\n    const map = this.byName.get(name);\r\n    return map ? map.get(arity) : undefined;\r\n  }\r\n\r\n  // convenience: get any meta for a name when only one arity exists\r\n  getSingleByName(name: string): FnMeta | undefined {\r\n    const map = this.byName.get(name);\r\n    if (!map) return undefined;\r\n    if (map.size === 1) return Array.from(map.values())[0];\r\n    return undefined;\r\n  }\r\n\r\n  getById(id: number): FnMeta {\r\n    return this.byId[id];\r\n  }\r\n}\r\n\r\n// ---------------- Bytecode VM ----------------\r\nenum OpCode {\r\n  PUSH_CONST = 0,\r\n  PUSH_VAR = 1,\r\n  PUSH_PROP = 2,  // New: push object property\r\n  ADD = 3, SUB = 4, MUL = 5, DIV = 6, MOD = 7, POW = 8,\r\n  NEG = 9,\r\n  LT = 10, LE = 11, GT = 12, GE = 13, EQ = 14, NEQ = 15,\r\n  AND = 16, OR = 17, NOT = 18,\r\n  CALL = 19,\r\n}\r\n\r\nexport class RPNProgram {\r\n  private readonly code: number[];\r\n  private readonly consts: RPNValue[];\r\n  private readonly varNames: string[];\r\n  private readonly propPaths: string[][]; // New: property access paths like ['bullet', 'speed']\r\n  private readonly registry: FunctionRegistry;\r\n  private readonly strictVars: boolean;\r\n\r\n  constructor(code: number[], consts: RPNValue[], varNames: string[], propPaths: string[][], registry: FunctionRegistry, strictVars = true) {\r\n    this.code = code;\r\n    this.consts = consts;\r\n    this.varNames = varNames;\r\n    this.propPaths = propPaths;\r\n    this.registry = registry;\r\n    this.strictVars = strictVars;\r\n  }\r\n\r\n  evaluate(ctx: RPNContext): RPNValue {\r\n    const varVals: RPNValue[] = new Array(this.varNames.length);\r\n    for (let i = 0; i < this.varNames.length; i++) {\r\n      const name = this.varNames[i];\r\n      const v = ctx[name];\r\n      if (v === undefined) {\r\n        if (this.strictVars) throw new Error(`Missing variable '${name}' in context.`);\r\n        varVals[i] = 0;\r\n      } else {\r\n        varVals[i] = v;\r\n      }\r\n    }\r\n\r\n    const stack: RPNValue[] = [];\r\n    const code = this.code;\r\n    let ip = 0;\r\n    while (ip < code.length) {\r\n      switch (code[ip++] as OpCode) {\r\n        case OpCode.PUSH_CONST: {\r\n          const idx = code[ip++]; stack.push(this.consts[idx]); break;\r\n        }\r\n        case OpCode.PUSH_VAR: {\r\n          const idx = code[ip++]; stack.push(varVals[idx]); break;\r\n        }\r\n        case OpCode.PUSH_PROP: {\r\n          const idx = code[ip++];\r\n          const path = this.propPaths[idx];\r\n          let value: any = ctx;\r\n          for (const prop of path) {\r\n            if (value && typeof value === 'object' && prop in value) {\r\n              value = value[prop];\r\n            } else {\r\n              if (this.strictVars) throw new Error(`Missing property '${path.join('.')}' in context.`);\r\n              value = 0;\r\n              break;\r\n            }\r\n          }\r\n          stack.push(typeof value === 'number' || typeof value === 'boolean' ? value : 0);\r\n          break;\r\n        }\r\n        case OpCode.ADD: { const b = Number(stack.pop()); const a = Number(stack.pop()); stack.push(a + b); break; }\r\n        case OpCode.SUB: { const b = Number(stack.pop()); const a = Number(stack.pop()); stack.push(a - b); break; }\r\n        case OpCode.MUL: { const b = Number(stack.pop()); const a = Number(stack.pop()); stack.push(a * b); break; }\r\n        case OpCode.DIV: { const b = Number(stack.pop()); const a = Number(stack.pop()); stack.push(a / b); break; }\r\n        case OpCode.MOD: { const b = Number(stack.pop()); const a = Number(stack.pop()); stack.push(a % b); break; }\r\n        case OpCode.POW: { const b = Number(stack.pop()); const a = Number(stack.pop()); stack.push(Math.pow(a, b)); break; }\r\n        case OpCode.NEG: { const a = Number(stack.pop()); stack.push(-a); break; }\r\n        case OpCode.LT: { const b = Number(stack.pop()); const a = Number(stack.pop()); stack.push(a < b); break; }\r\n        case OpCode.LE: { const b = Number(stack.pop()); const a = Number(stack.pop()); stack.push(a <= b); break; }\r\n        case OpCode.GT: { const b = Number(stack.pop()); const a = Number(stack.pop()); stack.push(a > b); break; }\r\n        case OpCode.GE: { const b = Number(stack.pop()); const a = Number(stack.pop()); stack.push(a >= b); break; }\r\n        case OpCode.EQ: { const b = stack.pop(); const a = stack.pop(); stack.push(a === b); break; }\r\n        case OpCode.NEQ: { const b = stack.pop(); const a = stack.pop(); stack.push(a !== b); break; }\r\n        case OpCode.AND: { const b = Boolean(stack.pop()); const a = Boolean(stack.pop()); stack.push(a && b); break; }\r\n        case OpCode.OR: { const b = Boolean(stack.pop()); const a = Boolean(stack.pop()); stack.push(a || b); break; }\r\n        case OpCode.NOT: { const a = Boolean(stack.pop()); stack.push(!a); break; }\r\n        case OpCode.CALL: {\r\n          const fnId = code[ip++]; const arity = code[ip++];\r\n          const args = new Array<RPNValue>(arity);\r\n          for (let i = arity - 1; i >= 0; i--) args[i] = stack.pop() as RPNValue;\r\n          const meta = this.registry.getById(fnId);\r\n          const out = meta.impl(args);\r\n          stack.push(out);\r\n          break;\r\n        }\r\n        default: throw new Error(`Unknown opcode ${(code[ip-1])}`);\r\n      }\r\n    }\r\n\r\n    if (stack.length !== 1) throw new Error(`Invalid program: stack has ${stack.length} values.`);\r\n    return stack[0];\r\n  }\r\n\r\n  getVariables(): readonly string[] { return this.varNames; }\r\n\r\n  // Serialize program data for editor storage\r\n  serialize(): SerializedRPNProgram {\r\n    return {\r\n      code: [...this.code],\r\n      consts: [...this.consts],\r\n      varNames: [...this.varNames],\r\n      propPaths: this.propPaths.map(path => [...path]),\r\n      strictVars: this.strictVars\r\n    };\r\n  }\r\n\r\n  // Create program from serialized data\r\n  static deserialize(data: SerializedRPNProgram, registry: FunctionRegistry): RPNProgram {\r\n    return new RPNProgram(\r\n      data.code,\r\n      data.consts,\r\n      data.varNames,\r\n      data.propPaths,\r\n      registry,\r\n      data.strictVars\r\n    );\r\n  }\r\n}\r\n\r\n// Serializable RPNProgram for Cocos Creator editor\r\n@ccclass('SerializableRPNProgram')\r\nexport class SerializableRPNProgram {\r\n  @property({ type: [Number], visible: false })\r\n  public code: number[] = [];\r\n\r\n  @property({ visible: false })\r\n  public consts: (number | boolean)[] = [];\r\n\r\n  @property({ type: [String], visible: false })\r\n  public varNames: string[] = [];\r\n\r\n  @property({ visible: false })\r\n  public propPaths: string[][] = [];\r\n\r\n  @property({ visible: false })\r\n  public strictVars: boolean = true;\r\n\r\n  // Convert to runtime RPNProgram\r\n  toRPNProgram(registry: FunctionRegistry): RPNProgram {\r\n    return RPNProgram.deserialize({\r\n      code: this.code,\r\n      consts: this.consts,\r\n      varNames: this.varNames,\r\n      propPaths: this.propPaths,\r\n      strictVars: this.strictVars\r\n    }, registry);\r\n  }\r\n\r\n  // Create from RPNProgram\r\n  static fromRPNProgram(program: RPNProgram): SerializableRPNProgram {\r\n    const serializable = new SerializableRPNProgram();\r\n    const data = program.serialize();\r\n    serializable.code = data.code;\r\n    serializable.consts = data.consts;\r\n    serializable.varNames = data.varNames;\r\n    serializable.propPaths = data.propPaths;\r\n    serializable.strictVars = data.strictVars;\r\n    return serializable;\r\n  }\r\n\r\n  // Check if this serializable program is valid/compiled\r\n  isCompiled(): boolean {\r\n    return this.code.length > 0;\r\n  }\r\n}\r\n\r\n// ---------------- Compiler from RPN tokens to bytecode ----------------\r\nexport interface CompileOptions { unknownIdentifierIsVar?: boolean; strictVars?: boolean; }\r\n\r\nconst NUM_RE = /^(?:[-+]?\\d+(?:\\.\\d+)?)$/i;\r\n\r\nfunction compileRPNInternal(tokens: string[], registry: FunctionRegistry, opts?: CompileOptions): RPNProgram {\r\n  const code: number[] = [];\r\n  const consts: RPNValue[] = [];\r\n  const varIndex = new Map<string, number>();\r\n  const varNames: string[] = [];\r\n  const propIndex = new Map<string, number>();\r\n  const propPaths: string[][] = [];\r\n\r\n  const pushConst = (v: RPNValue) => { consts.push(v); code.push(OpCode.PUSH_CONST, consts.length - 1); };\r\n  const pushVar = (name: string) => {\r\n    let idx = varIndex.get(name);\r\n    if (idx === undefined) { idx = varNames.length; varIndex.set(name, idx); varNames.push(name); }\r\n    code.push(OpCode.PUSH_VAR, idx);\r\n  };\r\n  const pushProp = (path: string[]) => {\r\n    const pathKey = path.join('.');\r\n    let idx = propIndex.get(pathKey);\r\n    if (idx === undefined) { idx = propPaths.length; propIndex.set(pathKey, idx); propPaths.push(path); }\r\n    code.push(OpCode.PUSH_PROP, idx);\r\n  };\r\n  const emitBinary = (op: OpCode) => code.push(op);\r\n\r\n  const unknownIsVar = opts?.unknownIdentifierIsVar ?? true;\r\n  const strictVars = opts?.strictVars ?? true;\r\n\r\n  for (const raw of tokens) {\r\n    const t = raw.trim();\r\n    if (!t) continue;\r\n    // constants\r\n    if (NUM_RE.test(t)) { pushConst(parseFloat(t)); continue; }\r\n    if (/^true$/i.test(t)) { pushConst(true); continue; }\r\n    if (/^false$/i.test(t)) { pushConst(false); continue; }\r\n\r\n    // Operators\r\n    switch (t) {\r\n      case '+': emitBinary(OpCode.ADD); continue;\r\n      case '-': emitBinary(OpCode.SUB); continue;\r\n      case '*': emitBinary(OpCode.MUL); continue;\r\n      case '/': emitBinary(OpCode.DIV); continue;\r\n      case '%': emitBinary(OpCode.MOD); continue;\r\n      case '^': emitBinary(OpCode.POW); continue;\r\n      case 'neg': code.push(OpCode.NEG); continue; // unary neg\r\n      case '<': emitBinary(OpCode.LT); continue;\r\n      case '<=': emitBinary(OpCode.LE); continue;\r\n      case '>': emitBinary(OpCode.GT); continue;\r\n      case '>=': emitBinary(OpCode.GE); continue;\r\n      case '==': emitBinary(OpCode.EQ); continue;\r\n      case '!=': emitBinary(OpCode.NEQ); continue;\r\n      case '&&': emitBinary(OpCode.AND); continue;\r\n      case '||': emitBinary(OpCode.OR); continue;\r\n      case '!': code.push(OpCode.NOT); continue;\r\n    }\r\n\r\n    // Function token produced by infix parser: format FUNC:name:arity\r\n    if (t.startsWith('FUNC:')) {\r\n      const parts = t.split(':');\r\n      // parts[0] == 'FUNC'\r\n      const name = parts[1];\r\n      const arity = parseInt(parts[2], 10);\r\n      const meta = registry.getByNameAndArity(name, arity);\r\n      if (!meta) throw new Error(`Function '${name}' with arity ${arity} is not registered.`);\r\n      code.push(OpCode.CALL, meta.id, arity);\r\n      continue;\r\n    }\r\n\r\n    // identifier -> variable, property access, or error\r\n    if (unknownIsVar) {\r\n      // Check if it's a property access (contains dots)\r\n      if (t.includes('.')) {\r\n        const path = t.split('.');\r\n        pushProp(path);\r\n      } else {\r\n        pushVar(t);\r\n      }\r\n      continue;\r\n    }\r\n    throw new Error(`Unknown token '${t}'.`);\r\n  }\r\n\r\n  return new RPNProgram(code, consts, varNames, propPaths, registry, strictVars);\r\n}\r\n\r\n// ---------------- Infix -> RPN (Shunting-yard) ----------------\r\ntype Token = { type: 'number'|'ident'|'op'|'lparen'|'rparen'|'comma'|'bool', value: string };\r\n\r\nfunction tokenize(expr: string): Token[] {\r\n  const out: Token[] = [];\r\n  const re = /\\s*([0-9]*\\.?[0-9]+|[A-Za-z_][A-Za-z0-9_.]*|<=|>=|==|!=|&&|\\|\\||[+\\-*/%^(),!<>])\\s*/g;\r\n  let m: RegExpExecArray | null;\r\n  while ((m = re.exec(expr)) !== null) {\r\n    const s = m[1];\r\n    if (/^[0-9]*\\.?[0-9]+$/.test(s)) out.push({type:'number', value:s});\r\n    else if (/^true|false$/i.test(s)) out.push({type:'bool', value:s});\r\n    else if (s === '(') out.push({type:'lparen', value:s});\r\n    else if (s === ')') out.push({type:'rparen', value:s});\r\n    else if (s === ',') out.push({type:'comma', value:s});\r\n    else if (/^[+\\-*/%^!<>]=?$/.test(s) || s === '&&' || s === '||') out.push({type:'op', value:s});\r\n    else out.push({type:'ident', value:s});\r\n  }\r\n  return out;\r\n}\r\n\r\n// Operator properties\r\ninterface OpInfo { prec: number; assoc: 'left'|'right'|'none'; unary?: boolean; }\r\nconst OPS: Record<string, OpInfo> = {\r\n  // unary '!' handled separately; unary '-' will be 'neg'\r\n  '^': { prec: 7, assoc: 'right' },\r\n  'neg': { prec: 6, assoc: 'right', unary: true },\r\n  '!': { prec: 6, assoc: 'right', unary: true },\r\n  '*': { prec: 5, assoc: 'left' },\r\n  '/': { prec: 5, assoc: 'left' },\r\n  '%': { prec: 5, assoc: 'left' },\r\n  '+': { prec: 4, assoc: 'left' },\r\n  '-': { prec: 4, assoc: 'left' },\r\n  '<': { prec: 3, assoc: 'left' },\r\n  '<=': { prec: 3, assoc: 'left' },\r\n  '>': { prec: 3, assoc: 'left' },\r\n  '>=': { prec: 3, assoc: 'left' },\r\n  '==': { prec: 2, assoc: 'left' },\r\n  '!=': { prec: 2, assoc: 'left' },\r\n  '&&': { prec: 1, assoc: 'left' },\r\n  '||': { prec: 0, assoc: 'left' },\r\n};\r\n\r\nfunction infixToRPN(expr: string, registry: FunctionRegistry): string[] {\r\n  const tokens = tokenize(expr);\r\n  const output: string[] = [];\r\n  const opStack: Array<{type:'op'|'func'|'lparen'|'rparen', value: string, argCount?: number}> = [];\r\n\r\n  // helper: when an identifier is followed by '(', it's a function name\r\n  for (let i = 0; i < tokens.length; i++) {\r\n    const t = tokens[i];\r\n\r\n    if (t.type === 'number' || t.type === 'bool') {\r\n      output.push(t.value);\r\n      continue;\r\n    }\r\n\r\n    if (t.type === 'ident') {\r\n      // If next token is lparen -> function\r\n      const next = tokens[i+1];\r\n      if (next && next.type === 'lparen') {\r\n        // push function onto opStack; track argument count (start at 1 for first arg, will increment on commas)\r\n        opStack.push({type:'func', value: t.value, argCount: 1});\r\n      } else {\r\n        // variable\r\n        output.push(t.value);\r\n      }\r\n      continue;\r\n    }\r\n\r\n    if (t.type === 'comma') {\r\n      // pop operators to output until left paren\r\n      while (opStack.length > 0 && opStack[opStack.length-1].type !== 'lparen') {\r\n        const op = opStack.pop()!;\r\n        if (op.type === 'op') output.push(op.value);\r\n        else if (op.type === 'func') {\r\n          // shouldn't reach here on comma, but handle defensively\r\n          output.push(`FUNC:${op.value}:${(op.argCount ?? 0)}`);\r\n        }\r\n      }\r\n      // increment argCount of function on stack (function must be below the left paren)\r\n      // find nearest func below the lparen\r\n      for (let j = opStack.length - 1; j >= 0; j--) {\r\n        if (opStack[j].type === 'lparen') {\r\n          // Look for function below the lparen\r\n          if (j > 0 && opStack[j-1].type === 'func') {\r\n            opStack[j-1].argCount = (opStack[j-1].argCount ?? 1) + 1;\r\n          }\r\n          break;\r\n        }\r\n      }\r\n      continue;\r\n    }\r\n\r\n    if (t.type === 'op') {\r\n      const rawOp = t.value;\r\n      // detect unary minus: if '-' and (start or after lparen or after comma or after another operator)\r\n      const prev = tokens[i-1];\r\n      const isUnary = rawOp === '-' && (\r\n        i === 0 ||\r\n        (prev && (prev.type === 'op' || prev.type === 'lparen' || prev.type === 'comma'))\r\n      );\r\n\r\n      const opKey = (isUnary && rawOp === '-') ? 'neg' : rawOp;\r\n\r\n      if (!(opKey in OPS)) throw new Error(`Unknown operator '${rawOp}'`);\r\n\r\n      const o1 = OPS[opKey];\r\n      while (opStack.length > 0) {\r\n        const top = opStack[opStack.length - 1];\r\n        if (top.type !== 'op') break;\r\n        const o2 = OPS[top.value];\r\n        if (!o2) break;\r\n        if ((o1.assoc === 'left' && o1.prec <= o2.prec) || (o1.assoc === 'right' && o1.prec < o2.prec)) {\r\n          output.push(opStack.pop()!.value);\r\n        } else break;\r\n      }\r\n      opStack.push({type:'op', value: opKey});\r\n      continue;\r\n    }\r\n\r\n    if (t.type === 'lparen') {\r\n      opStack.push({type:'lparen', value:'('});\r\n      // if the token before lparen is a function, we already pushed the function earlier.\r\n      continue;\r\n    }\r\n\r\n    if (t.type === 'rparen') {\r\n      // pop until left paren\r\n      while (opStack.length > 0 && opStack[opStack.length-1].type !== 'lparen') {\r\n        const op = opStack.pop()!;\r\n        if (op.type === 'op') output.push(op.value);\r\n        else if (op.type === 'func') {\r\n          // shouldn't normally be here\r\n          output.push(`FUNC:${op.value}:${(op.argCount ?? 0)}`);\r\n        }\r\n      }\r\n      if (opStack.length === 0) throw new Error('Mismatched parentheses');\r\n      // pop the left paren\r\n      opStack.pop();\r\n      // if top is a function, pop it to output with correct arity\r\n      if (opStack.length > 0 && opStack[opStack.length-1].type === 'func') {\r\n        const fn = opStack.pop()!;\r\n        // determine if there was any argument: check token before current index (i)\r\n        const before = tokens[i-1];\r\n        let finalArity: number;\r\n\r\n        if (before && before.type === 'lparen') {\r\n          // empty function call like f()\r\n          finalArity = 0;\r\n        } else {\r\n          // function has arguments, use the argCount we've been tracking\r\n          finalArity = fn.argCount ?? 1;\r\n        }\r\n\r\n        output.push(`FUNC:${fn.value}:${finalArity}`);\r\n      }\r\n      continue;\r\n    }\r\n  }\r\n\r\n  // pop remaining ops\r\n  while (opStack.length > 0) {\r\n    const op = opStack.pop()!;\r\n    if (op.type === 'lparen' || op.type === 'rparen') throw new Error('Mismatched parentheses');\r\n    if (op.type === 'op') output.push(op.value);\r\n    else if (op.type === 'func') {\r\n      // function with no parentheses? shouldn't happen\r\n      output.push(`FUNC:${op.value}:${op.argCount ?? 0}`);\r\n    }\r\n  }\r\n  return output;\r\n}\r\n\r\n// ---------------- Public RPNCalculator integrating infix parser ----------------\r\nexport class RPNCalculator {\r\n  readonly registry = new FunctionRegistry();\r\n\r\n  constructor(registerBuiltins = true) {\r\n    if (registerBuiltins) this.registerBuiltins();\r\n  }\r\n\r\n  compile(expr: string | string[], opts?: CompileOptions): RPNProgram {\r\n    let tokens: string[];\r\n    if (Array.isArray(expr)) tokens = expr;\r\n    else {\r\n      tokens = infixToRPN(expr, this.registry);\r\n    }\r\n    return compileRPNInternal(tokens, this.registry, opts);\r\n  }\r\n\r\n  register(name: string, arity: number, impl: RPNFn): void {\r\n    this.registry.register(name, arity, impl);\r\n  }\r\n\r\n  private registerBuiltins(): void {\r\n    const r = this.registry;\r\n    // unary\r\n    r.register('abs', 1, ([a]) => Math.abs(Number(a)));\r\n    r.register('floor', 1, ([a]) => Math.floor(Number(a)));\r\n    r.register('ceil', 1, ([a]) => Math.ceil(Number(a)));\r\n    r.register('round', 1, ([a]) => Math.round(Number(a)));\r\n    r.register('sin', 1, ([a]) => Math.sin(Number(a)));\r\n    r.register('cos', 1, ([a]) => Math.cos(Number(a)));\r\n    r.register('tan', 1, ([a]) => Math.tan(Number(a)));\r\n    // min/max 2-arity\r\n    r.register('min', 2, ([a, b]) => Math.min(Number(a), Number(b)));\r\n    r.register('max', 2, ([a, b]) => Math.max(Number(a), Number(b)));\r\n    // clamp(x, lo, hi)\r\n    r.register('clamp', 3, ([x, lo, hi]) => Math.max(Number(lo), Math.min(Number(hi), Number(x))));\r\n    // rand: support rand() and rand(a,b)\r\n    r.register('rand', 0, () => Math.random());\r\n    r.register('rand', 2, ([a, b]) => {\r\n      const A = Number(a), B = Number(b); return A + Math.random() * (B - A);\r\n    });\r\n    // Conditional functions\r\n    // select ternary: sel(cond, a, b) - same as cond ? a : b\r\n    r.register('sel', 3, ([cond, a, b]) => (cond ? a : b));\r\n    // if function: if(cond, value) - returns value if cond is true, 0 otherwise\r\n    r.register('if', 2, ([cond, value]) => (cond ? value : 0));\r\n    // ifelse function: ifelse(cond, true_val, false_val) - alias for sel\r\n    r.register('ifelse', 3, ([cond, a, b]) => (cond ? a : b));\r\n    // // when function: when(cond, value) - returns value if cond is true, undefined otherwise\r\n    // r.register('when', 2, ([cond, value]) => (cond ? value : 0));\r\n    // // unless function: unless(cond, value) - returns value if cond is false, 0 otherwise\r\n    // r.register('unless', 2, ([cond, value]) => (!cond ? value : 0));\r\n\r\n    r.register('bool', 1, ([x]) => Boolean(x));\r\n    \r\n    // lerp\r\n    r.register('lerp', 3, ([a,b,t]) => Number(a) + (Number(b)-Number(a)) * Number(t));\r\n  }\r\n}\r\n\r\n// ---------------- Example usage ----------------\r\n/*\r\nconst calc = new RPNCalculator();\r\n\r\nconst p1 = calc.compile('(a + b) * 2');\r\nconsole.log('vars:', p1.getVariables()); // [ 'a', 'b' ]\r\nconsole.log('eval1', p1.evaluate({ a: 3, b: 4 })); // 14\r\n\r\nconst p2 = calc.compile('sin(x) + cos(y)');\r\nconsole.log('eval2', p2.evaluate({ x: Math.PI/2, y: 0 })); // 2\r\n\r\nconst p3 = calc.compile('rand(0, 100) + min(3,5) * max(7,9)');\r\nconsole.log('eval3', p3.evaluate({})); // rand + 3*9\r\n\r\n// custom function\r\nconst calc2 = new RPNCalculator();\r\ncalc2.register('sum3', 3, ([a,b,c]) => Number(a)+Number(b)+Number(c));\r\nconst p4 = calc2.compile('sum3(1,2,3) * 2');\r\nconsole.log('eval4', p4.evaluate({})); // 12\r\n\r\n// unary minus\r\nconst p5 = calc.compile('-a * 5');\r\nconsole.log('eval5', p5.evaluate({ a: 2 })); // -10\r\n\r\n// 伤害计算\r\nconst damage = calc.compile('player.attack * sel(critical, 2, 1) * game.multipliers.damage');\r\ndamage.evaluate({ \r\n  player: { attack: 50 }, \r\n  critical: true, \r\n  game: { multipliers: { damage: 1.2 } } \r\n}); // 结果: 120\r\n\r\n// 深度嵌套\r\nconst position = calc.compile('player.stats.position.x + player.stats.position.y');\r\nposition.evaluate({ \r\n  player: { \r\n    stats: { \r\n      position: { x: 100, y: 200 } \r\n    } \r\n  } \r\n}); // 结果: 300\r\n*/\r\n\r\n// Export default if you like\r\nexport default RPNCalculator;\r\n"]}