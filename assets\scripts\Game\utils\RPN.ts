// rpn_calculator_infix.ts
// Complete TypeScript: Infix -> RPN -> Bytecode VM
// Usage example at bottom.

export type RPNValue = number | boolean;
export interface RPNContext { [name: string]: RPNValue | any | undefined; }
export type RPNFn = (args: RPNValue[]) => RPNValue;

// -------- Function registry (supporting multiple arities per name) --------
interface FnMeta {
  id: number;
  name: string;
  arity: number;
  impl: RPNFn;
}

export class FunctionRegistry {
  // name -> (arity -> FnMeta)
  private byName = new Map<string, Map<number, FnMeta>>();
  private byId: FnMeta[] = [];

  register(name: string, arity: number, impl: RPNFn): FnMeta {
    if (!Number.isInteger(arity) || arity < 0) throw new Error('arity must be >= 0');
    let map = this.byName.get(name);
    if (!map) { map = new Map(); this.byName.set(name, map); }
    const existing = map.get(arity);
    if (existing) {
      existing.impl = impl;
      return existing;
    }
    const meta: FnMeta = { id: this.byId.length, name, arity, impl };
    map.set(arity, meta);
    this.byId.push(meta);
    return meta;
  }

  getByNameAndArity(name: string, arity: number): FnMeta | undefined {
    const map = this.byName.get(name);
    return map ? map.get(arity) : undefined;
  }

  // convenience: get any meta for a name when only one arity exists
  getSingleByName(name: string): FnMeta | undefined {
    const map = this.byName.get(name);
    if (!map) return undefined;
    if (map.size === 1) return Array.from(map.values())[0];
    return undefined;
  }

  getById(id: number): FnMeta {
    return this.byId[id];
  }
}

// ---------------- Bytecode VM ----------------
enum OpCode {
  PUSH_CONST = 0,
  PUSH_VAR = 1,
  PUSH_PROP = 2,  // New: push object property
  ADD = 3, SUB = 4, MUL = 5, DIV = 6, MOD = 7, POW = 8,
  NEG = 9,
  LT = 10, LE = 11, GT = 12, GE = 13, EQ = 14, NEQ = 15,
  AND = 16, OR = 17, NOT = 18,
  CALL = 19,
}

export class RPNProgram {
  private readonly code: number[];
  private readonly consts: RPNValue[];
  private readonly varNames: string[];
  private readonly propPaths: string[][]; // New: property access paths like ['bullet', 'speed']
  private readonly registry: FunctionRegistry;
  private readonly strictVars: boolean;

  constructor(code: number[], consts: RPNValue[], varNames: string[], propPaths: string[][], registry: FunctionRegistry, strictVars = true) {
    this.code = code;
    this.consts = consts;
    this.varNames = varNames;
    this.propPaths = propPaths;
    this.registry = registry;
    this.strictVars = strictVars;
  }

  evaluate(ctx: RPNContext): RPNValue {
    const varVals: RPNValue[] = new Array(this.varNames.length);
    for (let i = 0; i < this.varNames.length; i++) {
      const name = this.varNames[i];
      const v = ctx[name];
      if (v === undefined) {
        if (this.strictVars) throw new Error(`Missing variable '${name}' in context.`);
        varVals[i] = 0;
      } else {
        varVals[i] = v;
      }
    }

    const stack: RPNValue[] = [];
    const code = this.code;
    let ip = 0;
    while (ip < code.length) {
      switch (code[ip++] as OpCode) {
        case OpCode.PUSH_CONST: {
          const idx = code[ip++]; stack.push(this.consts[idx]); break;
        }
        case OpCode.PUSH_VAR: {
          const idx = code[ip++]; stack.push(varVals[idx]); break;
        }
        case OpCode.PUSH_PROP: {
          const idx = code[ip++];
          const path = this.propPaths[idx];
          let value: any = ctx;
          for (const prop of path) {
            if (value && typeof value === 'object' && prop in value) {
              value = value[prop];
            } else {
              if (this.strictVars) throw new Error(`Missing property '${path.join('.')}' in context.`);
              value = 0;
              break;
            }
          }
          stack.push(typeof value === 'number' || typeof value === 'boolean' ? value : 0);
          break;
        }
        case OpCode.ADD: { const b = Number(stack.pop()); const a = Number(stack.pop()); stack.push(a + b); break; }
        case OpCode.SUB: { const b = Number(stack.pop()); const a = Number(stack.pop()); stack.push(a - b); break; }
        case OpCode.MUL: { const b = Number(stack.pop()); const a = Number(stack.pop()); stack.push(a * b); break; }
        case OpCode.DIV: { const b = Number(stack.pop()); const a = Number(stack.pop()); stack.push(a / b); break; }
        case OpCode.MOD: { const b = Number(stack.pop()); const a = Number(stack.pop()); stack.push(a % b); break; }
        case OpCode.POW: { const b = Number(stack.pop()); const a = Number(stack.pop()); stack.push(Math.pow(a, b)); break; }
        case OpCode.NEG: { const a = Number(stack.pop()); stack.push(-a); break; }
        case OpCode.LT: { const b = Number(stack.pop()); const a = Number(stack.pop()); stack.push(a < b); break; }
        case OpCode.LE: { const b = Number(stack.pop()); const a = Number(stack.pop()); stack.push(a <= b); break; }
        case OpCode.GT: { const b = Number(stack.pop()); const a = Number(stack.pop()); stack.push(a > b); break; }
        case OpCode.GE: { const b = Number(stack.pop()); const a = Number(stack.pop()); stack.push(a >= b); break; }
        case OpCode.EQ: { const b = stack.pop(); const a = stack.pop(); stack.push(a === b); break; }
        case OpCode.NEQ: { const b = stack.pop(); const a = stack.pop(); stack.push(a !== b); break; }
        case OpCode.AND: { const b = Boolean(stack.pop()); const a = Boolean(stack.pop()); stack.push(a && b); break; }
        case OpCode.OR: { const b = Boolean(stack.pop()); const a = Boolean(stack.pop()); stack.push(a || b); break; }
        case OpCode.NOT: { const a = Boolean(stack.pop()); stack.push(!a); break; }
        case OpCode.CALL: {
          const fnId = code[ip++]; const arity = code[ip++];
          const args = new Array<RPNValue>(arity);
          for (let i = arity - 1; i >= 0; i--) args[i] = stack.pop() as RPNValue;
          const meta = this.registry.getById(fnId);
          const out = meta.impl(args);
          stack.push(out);
          break;
        }
        default: throw new Error(`Unknown opcode ${(code[ip-1])}`);
      }
    }

    if (stack.length !== 1) throw new Error(`Invalid program: stack has ${stack.length} values.`);
    return stack[0];
  }

  getVariables(): readonly string[] { return this.varNames; }
}

// ---------------- Compiler from RPN tokens to bytecode ----------------
export interface CompileOptions { unknownIdentifierIsVar?: boolean; strictVars?: boolean; }

const NUM_RE = /^(?:[-+]?\d+(?:\.\d+)?)$/i;

function compileRPNInternal(tokens: string[], registry: FunctionRegistry, opts?: CompileOptions): RPNProgram {
  const code: number[] = [];
  const consts: RPNValue[] = [];
  const varIndex = new Map<string, number>();
  const varNames: string[] = [];
  const propIndex = new Map<string, number>();
  const propPaths: string[][] = [];

  const pushConst = (v: RPNValue) => { consts.push(v); code.push(OpCode.PUSH_CONST, consts.length - 1); };
  const pushVar = (name: string) => {
    let idx = varIndex.get(name);
    if (idx === undefined) { idx = varNames.length; varIndex.set(name, idx); varNames.push(name); }
    code.push(OpCode.PUSH_VAR, idx);
  };
  const pushProp = (path: string[]) => {
    const pathKey = path.join('.');
    let idx = propIndex.get(pathKey);
    if (idx === undefined) { idx = propPaths.length; propIndex.set(pathKey, idx); propPaths.push(path); }
    code.push(OpCode.PUSH_PROP, idx);
  };
  const emitBinary = (op: OpCode) => code.push(op);

  const unknownIsVar = opts?.unknownIdentifierIsVar ?? true;
  const strictVars = opts?.strictVars ?? true;

  for (const raw of tokens) {
    const t = raw.trim();
    if (!t) continue;
    // constants
    if (NUM_RE.test(t)) { pushConst(parseFloat(t)); continue; }
    if (/^true$/i.test(t)) { pushConst(true); continue; }
    if (/^false$/i.test(t)) { pushConst(false); continue; }

    // Operators
    switch (t) {
      case '+': emitBinary(OpCode.ADD); continue;
      case '-': emitBinary(OpCode.SUB); continue;
      case '*': emitBinary(OpCode.MUL); continue;
      case '/': emitBinary(OpCode.DIV); continue;
      case '%': emitBinary(OpCode.MOD); continue;
      case '^': emitBinary(OpCode.POW); continue;
      case 'neg': code.push(OpCode.NEG); continue; // unary neg
      case '<': emitBinary(OpCode.LT); continue;
      case '<=': emitBinary(OpCode.LE); continue;
      case '>': emitBinary(OpCode.GT); continue;
      case '>=': emitBinary(OpCode.GE); continue;
      case '==': emitBinary(OpCode.EQ); continue;
      case '!=': emitBinary(OpCode.NEQ); continue;
      case '&&': emitBinary(OpCode.AND); continue;
      case '||': emitBinary(OpCode.OR); continue;
      case '!': code.push(OpCode.NOT); continue;
    }

    // Function token produced by infix parser: format FUNC:name:arity
    if (t.startsWith('FUNC:')) {
      const parts = t.split(':');
      // parts[0] == 'FUNC'
      const name = parts[1];
      const arity = parseInt(parts[2], 10);
      const meta = registry.getByNameAndArity(name, arity);
      if (!meta) throw new Error(`Function '${name}' with arity ${arity} is not registered.`);
      code.push(OpCode.CALL, meta.id, arity);
      continue;
    }

    // identifier -> variable, property access, or error
    if (unknownIsVar) {
      // Check if it's a property access (contains dots)
      if (t.includes('.')) {
        const path = t.split('.');
        pushProp(path);
      } else {
        pushVar(t);
      }
      continue;
    }
    throw new Error(`Unknown token '${t}'.`);
  }

  return new RPNProgram(code, consts, varNames, propPaths, registry, strictVars);
}

// ---------------- Infix -> RPN (Shunting-yard) ----------------
type Token = { type: 'number'|'ident'|'op'|'lparen'|'rparen'|'comma'|'bool', value: string };

function tokenize(expr: string): Token[] {
  const out: Token[] = [];
  const re = /\s*([0-9]*\.?[0-9]+|[A-Za-z_][A-Za-z0-9_.]*|<=|>=|==|!=|&&|\|\||[+\-*/%^(),!<>])\s*/g;
  let m: RegExpExecArray | null;
  while ((m = re.exec(expr)) !== null) {
    const s = m[1];
    if (/^[0-9]*\.?[0-9]+$/.test(s)) out.push({type:'number', value:s});
    else if (/^true|false$/i.test(s)) out.push({type:'bool', value:s});
    else if (s === '(') out.push({type:'lparen', value:s});
    else if (s === ')') out.push({type:'rparen', value:s});
    else if (s === ',') out.push({type:'comma', value:s});
    else if (/^[+\-*/%^!<>]=?$/.test(s) || s === '&&' || s === '||') out.push({type:'op', value:s});
    else out.push({type:'ident', value:s});
  }
  return out;
}

// Operator properties
interface OpInfo { prec: number; assoc: 'left'|'right'|'none'; unary?: boolean; }
const OPS: Record<string, OpInfo> = {
  // unary '!' handled separately; unary '-' will be 'neg'
  '^': { prec: 7, assoc: 'right' },
  'neg': { prec: 6, assoc: 'right', unary: true },
  '!': { prec: 6, assoc: 'right', unary: true },
  '*': { prec: 5, assoc: 'left' },
  '/': { prec: 5, assoc: 'left' },
  '%': { prec: 5, assoc: 'left' },
  '+': { prec: 4, assoc: 'left' },
  '-': { prec: 4, assoc: 'left' },
  '<': { prec: 3, assoc: 'left' },
  '<=': { prec: 3, assoc: 'left' },
  '>': { prec: 3, assoc: 'left' },
  '>=': { prec: 3, assoc: 'left' },
  '==': { prec: 2, assoc: 'left' },
  '!=': { prec: 2, assoc: 'left' },
  '&&': { prec: 1, assoc: 'left' },
  '||': { prec: 0, assoc: 'left' },
};

function infixToRPN(expr: string, registry: FunctionRegistry): string[] {
  const tokens = tokenize(expr);
  const output: string[] = [];
  const opStack: Array<{type:'op'|'func'|'lparen'|'rparen', value: string, argCount?: number}> = [];

  // helper: when an identifier is followed by '(', it's a function name
  for (let i = 0; i < tokens.length; i++) {
    const t = tokens[i];

    if (t.type === 'number' || t.type === 'bool') {
      output.push(t.value);
      continue;
    }

    if (t.type === 'ident') {
      // If next token is lparen -> function
      const next = tokens[i+1];
      if (next && next.type === 'lparen') {
        // push function onto opStack; track argument count (start at 1 for first arg, will increment on commas)
        opStack.push({type:'func', value: t.value, argCount: 1});
      } else {
        // variable
        output.push(t.value);
      }
      continue;
    }

    if (t.type === 'comma') {
      // pop operators to output until left paren
      while (opStack.length > 0 && opStack[opStack.length-1].type !== 'lparen') {
        const op = opStack.pop()!;
        if (op.type === 'op') output.push(op.value);
        else if (op.type === 'func') {
          // shouldn't reach here on comma, but handle defensively
          output.push(`FUNC:${op.value}:${(op.argCount ?? 0)}`);
        }
      }
      // increment argCount of function on stack (function must be below the left paren)
      // find nearest func below the lparen
      for (let j = opStack.length - 1; j >= 0; j--) {
        if (opStack[j].type === 'lparen') {
          // Look for function below the lparen
          if (j > 0 && opStack[j-1].type === 'func') {
            opStack[j-1].argCount = (opStack[j-1].argCount ?? 1) + 1;
          }
          break;
        }
      }
      continue;
    }

    if (t.type === 'op') {
      const rawOp = t.value;
      // detect unary minus: if '-' and (start or after lparen or after comma or after another operator)
      const prev = tokens[i-1];
      const isUnary = rawOp === '-' && (
        i === 0 ||
        (prev && (prev.type === 'op' || prev.type === 'lparen' || prev.type === 'comma'))
      );

      const opKey = (isUnary && rawOp === '-') ? 'neg' : rawOp;

      if (!(opKey in OPS)) throw new Error(`Unknown operator '${rawOp}'`);

      const o1 = OPS[opKey];
      while (opStack.length > 0) {
        const top = opStack[opStack.length - 1];
        if (top.type !== 'op') break;
        const o2 = OPS[top.value];
        if (!o2) break;
        if ((o1.assoc === 'left' && o1.prec <= o2.prec) || (o1.assoc === 'right' && o1.prec < o2.prec)) {
          output.push(opStack.pop()!.value);
        } else break;
      }
      opStack.push({type:'op', value: opKey});
      continue;
    }

    if (t.type === 'lparen') {
      opStack.push({type:'lparen', value:'('});
      // if the token before lparen is a function, we already pushed the function earlier.
      continue;
    }

    if (t.type === 'rparen') {
      // pop until left paren
      while (opStack.length > 0 && opStack[opStack.length-1].type !== 'lparen') {
        const op = opStack.pop()!;
        if (op.type === 'op') output.push(op.value);
        else if (op.type === 'func') {
          // shouldn't normally be here
          output.push(`FUNC:${op.value}:${(op.argCount ?? 0)}`);
        }
      }
      if (opStack.length === 0) throw new Error('Mismatched parentheses');
      // pop the left paren
      opStack.pop();
      // if top is a function, pop it to output with correct arity
      if (opStack.length > 0 && opStack[opStack.length-1].type === 'func') {
        const fn = opStack.pop()!;
        // determine if there was any argument: check token before current index (i)
        const before = tokens[i-1];
        let finalArity: number;

        if (before && before.type === 'lparen') {
          // empty function call like f()
          finalArity = 0;
        } else {
          // function has arguments, use the argCount we've been tracking
          finalArity = fn.argCount ?? 1;
        }

        output.push(`FUNC:${fn.value}:${finalArity}`);
      }
      continue;
    }
  }

  // pop remaining ops
  while (opStack.length > 0) {
    const op = opStack.pop()!;
    if (op.type === 'lparen' || op.type === 'rparen') throw new Error('Mismatched parentheses');
    if (op.type === 'op') output.push(op.value);
    else if (op.type === 'func') {
      // function with no parentheses? shouldn't happen
      output.push(`FUNC:${op.value}:${op.argCount ?? 0}`);
    }
  }
  return output;
}

// ---------------- Public RPNCalculator integrating infix parser ----------------
export class RPNCalculator {
  readonly registry = new FunctionRegistry();

  constructor(registerBuiltins = true) {
    if (registerBuiltins) this.registerBuiltins();
  }

  compile(expr: string | string[], opts?: CompileOptions): RPNProgram {
    let tokens: string[];
    if (Array.isArray(expr)) tokens = expr;
    else {
      tokens = infixToRPN(expr, this.registry);
    }
    return compileRPNInternal(tokens, this.registry, opts);
  }

  register(name: string, arity: number, impl: RPNFn): void {
    this.registry.register(name, arity, impl);
  }

  private registerBuiltins(): void {
    const r = this.registry;
    // unary
    r.register('abs', 1, ([a]) => Math.abs(Number(a)));
    r.register('floor', 1, ([a]) => Math.floor(Number(a)));
    r.register('ceil', 1, ([a]) => Math.ceil(Number(a)));
    r.register('round', 1, ([a]) => Math.round(Number(a)));
    r.register('sin', 1, ([a]) => Math.sin(Number(a)));
    r.register('cos', 1, ([a]) => Math.cos(Number(a)));
    r.register('tan', 1, ([a]) => Math.tan(Number(a)));
    // min/max 2-arity
    r.register('min', 2, ([a, b]) => Math.min(Number(a), Number(b)));
    r.register('max', 2, ([a, b]) => Math.max(Number(a), Number(b)));
    // clamp(x, lo, hi)
    r.register('clamp', 3, ([x, lo, hi]) => Math.max(Number(lo), Math.min(Number(hi), Number(x))));
    // rand: support rand() and rand(a,b)
    r.register('rand', 0, () => Math.random());
    r.register('rand', 2, ([a, b]) => {
      const A = Number(a), B = Number(b); return A + Math.random() * (B - A);
    });
    // Conditional functions
    // select ternary: sel(cond, a, b) - same as cond ? a : b
    r.register('sel', 3, ([cond, a, b]) => (cond ? a : b));
    // if function: if(cond, value) - returns value if cond is true, 0 otherwise
    r.register('if', 2, ([cond, value]) => (cond ? value : 0));
    // ifelse function: ifelse(cond, true_val, false_val) - alias for sel
    r.register('ifelse', 3, ([cond, a, b]) => (cond ? a : b));
    // // when function: when(cond, value) - returns value if cond is true, undefined otherwise
    // r.register('when', 2, ([cond, value]) => (cond ? value : 0));
    // // unless function: unless(cond, value) - returns value if cond is false, 0 otherwise
    // r.register('unless', 2, ([cond, value]) => (!cond ? value : 0));

    r.register('bool', 1, ([x]) => Boolean(x));
    
    // lerp
    r.register('lerp', 3, ([a,b,t]) => Number(a) + (Number(b)-Number(a)) * Number(t));
  }
}

// ---------------- Example usage ----------------
/*
const calc = new RPNCalculator();

const p1 = calc.compile('(a + b) * 2');
console.log('vars:', p1.getVariables()); // [ 'a', 'b' ]
console.log('eval1', p1.evaluate({ a: 3, b: 4 })); // 14

const p2 = calc.compile('sin(x) + cos(y)');
console.log('eval2', p2.evaluate({ x: Math.PI/2, y: 0 })); // 2

const p3 = calc.compile('rand(0, 100) + min(3,5) * max(7,9)');
console.log('eval3', p3.evaluate({})); // rand + 3*9

// custom function
const calc2 = new RPNCalculator();
calc2.register('sum3', 3, ([a,b,c]) => Number(a)+Number(b)+Number(c));
const p4 = calc2.compile('sum3(1,2,3) * 2');
console.log('eval4', p4.evaluate({})); // 12

// unary minus
const p5 = calc.compile('-a * 5');
console.log('eval5', p5.evaluate({ a: 2 })); // -10

// 伤害计算
const damage = calc.compile('player.attack * sel(critical, 2, 1) * game.multipliers.damage');
damage.evaluate({ 
  player: { attack: 50 }, 
  critical: true, 
  game: { multipliers: { damage: 1.2 } } 
}); // 结果: 120

// 深度嵌套
const position = calc.compile('player.stats.position.x + player.stats.position.y');
position.evaluate({ 
  player: { 
    stats: { 
      position: { x: 100, y: 200 } 
    } 
  } 
}); // 结果: 300
*/

// Export default if you like
export default RPNCalculator;
