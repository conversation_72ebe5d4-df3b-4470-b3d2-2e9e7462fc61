2025-9-4 18:12:57-debug: start **** info
2025-9-4 18:12:57-log: Cannot access game frame or container.
2025-9-4 18:12:57-debug: asset-db:require-engine-code (397ms)
2025-9-4 18:12:57-log: meshopt wasm decoder initialized
2025-9-4 18:12:57-log: [bullet]:bullet wasm lib loaded.
2025-9-4 18:12:57-log: [box2d]:box2d wasm lib loaded.
2025-9-4 18:12:57-log: Cocos Creator v3.8.6
2025-9-4 18:12:57-log: Using legacy pipeline
2025-9-4 18:12:57-log: Forward render pipeline initialized.
2025-9-4 18:12:57-debug: [Assets Memory track]: asset-db:worker-init: initEngine start:30.80MB, end 80.02MB, increase: 49.22MB
2025-9-4 18:12:58-debug: [Assets Memory track]: asset-db-plugin-register: builder start:84.32MB, end 288.96MB, increase: 204.65MB
2025-9-4 18:12:58-debug: [Assets Memory track]: asset-db-plugin-register: project start:80.78MB, end 287.24MB, increase: 206.45MB
2025-9-4 18:12:57-debug: [Assets Memory track]: asset-db-plugin-register: programming start:80.93MB, end 84.29MB, increase: 3.36MB
2025-9-4 18:12:58-debug: [Assets Memory track]: asset-db:worker-init: initPlugin start:80.05MB, end 287.26MB, increase: 207.22MB
2025-9-4 18:12:58-debug: run package(google-play) handler(enable) start
2025-9-4 18:12:58-debug: run package(google-play) handler(enable) success!
2025-9-4 18:12:58-debug: run package(harmonyos-next) handler(enable) start
2025-9-4 18:12:58-debug: run package(harmonyos-next) handler(enable) success!
2025-9-4 18:12:58-debug: run package(honor-mini-game) handler(enable) success!
2025-9-4 18:12:58-debug: run package(huawei-agc) handler(enable) start
2025-9-4 18:12:58-debug: run package(huawei-agc) handler(enable) success!
2025-9-4 18:12:58-debug: run package(huawei-quick-game) handler(enable) start
2025-9-4 18:12:58-debug: run package(huawei-quick-game) handler(enable) success!
2025-9-4 18:12:58-debug: run package(ios) handler(enable) start
2025-9-4 18:12:58-debug: run package(honor-mini-game) handler(enable) start
2025-9-4 18:12:58-debug: run package(linux) handler(enable) start
2025-9-4 18:12:58-debug: run package(ios) handler(enable) success!
2025-9-4 18:12:58-debug: run package(linux) handler(enable) success!
2025-9-4 18:12:58-debug: run package(mac) handler(enable) success!
2025-9-4 18:12:58-debug: run package(mac) handler(enable) start
2025-9-4 18:12:58-debug: run package(migu-mini-game) handler(enable) start
2025-9-4 18:12:58-debug: run package(native) handler(enable) success!
2025-9-4 18:12:58-debug: run package(migu-mini-game) handler(enable) success!
2025-9-4 18:12:58-debug: run package(native) handler(enable) start
2025-9-4 18:12:58-debug: run package(ohos) handler(enable) start
2025-9-4 18:12:58-debug: run package(ohos) handler(enable) success!
2025-9-4 18:12:58-debug: run package(oppo-mini-game) handler(enable) success!
2025-9-4 18:12:58-debug: run package(runtime-dev-tools) handler(enable) start
2025-9-4 18:12:58-debug: run package(oppo-mini-game) handler(enable) start
2025-9-4 18:12:58-debug: run package(runtime-dev-tools) handler(enable) success!
2025-9-4 18:12:58-debug: run package(taobao-mini-game) handler(enable) start
2025-9-4 18:12:58-debug: run package(taobao-mini-game) handler(enable) success!
2025-9-4 18:12:58-debug: run package(vivo-mini-game) handler(enable) start
2025-9-4 18:12:58-debug: run package(vivo-mini-game) handler(enable) success!
2025-9-4 18:12:58-debug: run package(web-desktop) handler(enable) success!
2025-9-4 18:12:58-debug: run package(web-desktop) handler(enable) start
2025-9-4 18:12:58-debug: run package(web-mobile) handler(enable) start
2025-9-4 18:12:58-debug: run package(wechatgame) handler(enable) start
2025-9-4 18:12:58-debug: run package(web-mobile) handler(enable) success!
2025-9-4 18:12:58-debug: run package(wechatprogram) handler(enable) start
2025-9-4 18:12:58-debug: run package(wechatgame) handler(enable) success!
2025-9-4 18:12:58-debug: run package(windows) handler(enable) success!
2025-9-4 18:12:58-debug: run package(xiaomi-quick-game) handler(enable) start
2025-9-4 18:12:58-debug: run package(xiaomi-quick-game) handler(enable) success!
2025-9-4 18:12:58-debug: run package(wechatprogram) handler(enable) success!
2025-9-4 18:12:58-debug: run package(cocos-service) handler(enable) success!
2025-9-4 18:12:58-debug: run package(cocos-service) handler(enable) start
2025-9-4 18:12:58-debug: run package(im-plugin) handler(enable) start
2025-9-4 18:12:58-debug: run package(im-plugin) handler(enable) success!
2025-9-4 18:12:58-debug: run package(fix-asset-default-userdata) handler(enable) start
2025-9-4 18:12:58-debug: run package(fix-asset-default-userdata) handler(enable) success!
2025-9-4 18:12:58-debug: run package(emitter-editor) handler(enable) start
2025-9-4 18:12:58-debug: run package(emitter-editor) handler(enable) success!
2025-9-4 18:12:58-debug: run package(windows) handler(enable) start
2025-9-4 18:12:58-debug: run package(level-editor) handler(enable) start
2025-9-4 18:12:58-debug: run package(localization-editor) handler(enable) start
2025-9-4 18:12:58-debug: run package(level-editor) handler(enable) success!
2025-9-4 18:12:58-debug: run package(localization-editor) handler(enable) success!
2025-9-4 18:12:58-debug: asset-db:worker-init: initPlugin (1076ms)
2025-9-4 18:12:58-debug: run package(placeholder) handler(enable) start
2025-9-4 18:12:58-debug: run package(placeholder) handler(enable) success!
2025-9-4 18:12:58-debug: [Assets Memory track]: asset-db:worker-init start:30.79MB, end 296.86MB, increase: 266.07MB
2025-9-4 18:12:58-debug: Run asset db hook programming:beforePreStart success!
2025-9-4 18:12:58-debug: Run asset db hook engine-extends:beforePreStart success!
2025-9-4 18:12:58-debug: Run asset db hook programming:beforePreStart ...
2025-9-4 18:12:58-debug: Run asset db hook engine-extends:beforePreStart ...
2025-9-4 18:12:58-debug: asset-db:worker-init (1603ms)
2025-9-4 18:12:58-debug: asset-db-hook-programming-beforePreStart (55ms)
2025-9-4 18:12:58-debug: asset-db-hook-engine-extends-beforePreStart (54ms)
2025-9-4 18:12:58-debug: Preimport db internal success
2025-9-4 18:12:58-debug: Preimport db assets success
2025-9-4 18:12:58-debug: Run asset db hook programming:afterPreStart ...
2025-9-4 18:12:58-debug: starting packer-driver...
2025-9-4 18:13:03-debug: initialize scripting environment...
2025-9-4 18:13:03-debug: [[Executor]] prepare before lock
2025-9-4 18:13:03-debug: Set detail map pack:///resolution-detail-map.json: {}
2025-9-4 18:13:03-debug: [[Executor]] prepare after unlock
2025-9-4 18:13:03-debug: Run asset db hook programming:afterPreStart success!
2025-9-4 18:13:03-debug: Run asset db hook engine-extends:afterPreStart success!
2025-9-4 18:13:03-debug: Run asset db hook engine-extends:afterPreStart ...
2025-9-4 18:13:03-debug: Start up the 'internal' database...
2025-9-4 18:13:03-debug: asset-db-hook-programming-afterPreStart (5188ms)
2025-9-4 18:13:03-debug: asset-db:worker-effect-data-processing (204ms)
2025-9-4 18:13:03-debug: asset-db-hook-engine-extends-afterPreStart (205ms)
2025-9-4 18:13:03-debug: Start up the 'assets' database...
2025-9-4 18:13:03-debug: asset-db:worker-startup-database[internal] (5386ms)
2025-9-4 18:13:03-debug: [Assets Memory track]: asset-db:worker-init: startup start:175.00MB, end 192.85MB, increase: 17.85MB
2025-9-4 18:13:03-debug: lazy register asset handler *
2025-9-4 18:13:03-debug: lazy register asset handler text
2025-9-4 18:13:03-debug: lazy register asset handler dragonbones
2025-9-4 18:13:03-debug: lazy register asset handler directory
2025-9-4 18:13:03-debug: lazy register asset handler spine-data
2025-9-4 18:13:03-debug: lazy register asset handler dragonbones-atlas
2025-9-4 18:13:03-debug: lazy register asset handler json
2025-9-4 18:13:03-debug: lazy register asset handler terrain
2025-9-4 18:13:03-debug: lazy register asset handler javascript
2025-9-4 18:13:03-debug: lazy register asset handler typescript
2025-9-4 18:13:03-debug: lazy register asset handler sprite-frame
2025-9-4 18:13:03-debug: lazy register asset handler scene
2025-9-4 18:13:03-debug: lazy register asset handler prefab
2025-9-4 18:13:03-debug: lazy register asset handler tiled-map
2025-9-4 18:13:03-debug: lazy register asset handler sign-image
2025-9-4 18:13:03-debug: lazy register asset handler image
2025-9-4 18:13:03-debug: lazy register asset handler alpha-image
2025-9-4 18:13:03-debug: lazy register asset handler buffer
2025-9-4 18:13:03-debug: lazy register asset handler texture-cube
2025-9-4 18:13:03-debug: lazy register asset handler erp-texture-cube
2025-9-4 18:13:03-debug: lazy register asset handler render-texture
2025-9-4 18:13:03-debug: lazy register asset handler rt-sprite-frame
2025-9-4 18:13:03-debug: lazy register asset handler gltf
2025-9-4 18:13:03-debug: lazy register asset handler texture
2025-9-4 18:13:03-debug: lazy register asset handler texture-cube-face
2025-9-4 18:13:03-debug: lazy register asset handler gltf-mesh
2025-9-4 18:13:03-debug: lazy register asset handler gltf-animation
2025-9-4 18:13:03-debug: lazy register asset handler gltf-scene
2025-9-4 18:13:03-debug: lazy register asset handler gltf-material
2025-9-4 18:13:03-debug: lazy register asset handler gltf-skeleton
2025-9-4 18:13:03-debug: lazy register asset handler material
2025-9-4 18:13:03-debug: lazy register asset handler fbx
2025-9-4 18:13:03-debug: lazy register asset handler gltf-embeded-image
2025-9-4 18:13:03-debug: lazy register asset handler effect-header
2025-9-4 18:13:03-debug: lazy register asset handler physics-material
2025-9-4 18:13:03-debug: lazy register asset handler audio-clip
2025-9-4 18:13:03-debug: lazy register asset handler effect
2025-9-4 18:13:03-debug: lazy register asset handler animation-clip
2025-9-4 18:13:03-debug: lazy register asset handler ttf-font
2025-9-4 18:13:03-debug: lazy register asset handler bitmap-font
2025-9-4 18:13:03-debug: lazy register asset handler animation-graph-variant
2025-9-4 18:13:03-debug: lazy register asset handler animation-graph
2025-9-4 18:13:03-debug: lazy register asset handler animation-mask
2025-9-4 18:13:03-debug: lazy register asset handler particle
2025-9-4 18:13:03-debug: lazy register asset handler sprite-atlas
2025-9-4 18:13:03-debug: lazy register asset handler render-pipeline
2025-9-4 18:13:03-debug: lazy register asset handler auto-atlas
2025-9-4 18:13:03-debug: lazy register asset handler render-flow
2025-9-4 18:13:03-debug: lazy register asset handler label-atlas
2025-9-4 18:13:03-debug: lazy register asset handler render-stage
2025-9-4 18:13:03-debug: lazy register asset handler instantiation-material
2025-9-4 18:13:03-debug: lazy register asset handler instantiation-animation
2025-9-4 18:13:03-debug: lazy register asset handler instantiation-mesh
2025-9-4 18:13:03-debug: lazy register asset handler instantiation-skeleton
2025-9-4 18:13:03-debug: lazy register asset handler video-clip
2025-9-4 18:13:04-debug: asset-db:worker-startup-database[assets] (5383ms)
2025-9-4 18:13:04-debug: asset-db:start-database (5459ms)
2025-9-4 18:13:04-debug: asset-db:ready (8466ms)
2025-9-4 18:13:04-debug: fix the bug of updateDefaultUserData
2025-9-4 18:13:04-debug: init worker message success
2025-9-4 18:13:04-debug: programming:execute-script (3ms)
2025-9-4 18:13:04-debug: [Build Memory track]: builder:worker-init start:197.39MB, end 209.91MB, increase: 12.51MB
2025-9-4 18:13:04-debug: builder:worker-init (267ms)
2025-9-4 18:14:44-debug: refresh db internal success
2025-9-4 18:14:44-debug: %cImport%c: E:\M2Game\Client\assets\editor\emitter\EmitterEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-4 18:14:44-debug: refresh db assets success
2025-9-4 18:14:44-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-4 18:14:44-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-4 18:14:44-debug: asset-db:refresh-all-database (145ms)
2025-9-4 18:20:37-debug: refresh db internal success
2025-9-4 18:20:37-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-4 18:20:37-debug: refresh db assets success
2025-9-4 18:20:37-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-4 18:20:37-debug: asset-db:refresh-all-database (141ms)
2025-9-4 18:22:29-debug: refresh db internal success
2025-9-4 18:22:29-debug: refresh db assets success
2025-9-4 18:22:29-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-4 18:22:29-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-4 18:22:29-debug: asset-db:refresh-all-database (139ms)
2025-9-4 18:22:29-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-4 18:23:44-debug: refresh db internal success
2025-9-4 18:23:45-debug: refresh db assets success
2025-9-4 18:23:45-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-4 18:23:45-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-4 18:23:45-debug: asset-db:refresh-all-database (129ms)
2025-9-4 18:23:45-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-4 18:23:45-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-4 18:23:49-debug: refresh db internal success
2025-9-4 18:23:49-debug: refresh db assets success
2025-9-4 18:23:49-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-4 18:23:49-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-4 18:23:49-debug: asset-db:refresh-all-database (130ms)
2025-9-4 18:24:16-debug: refresh db internal success
2025-9-4 18:24:16-debug: refresh db assets success
2025-9-4 18:24:16-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-4 18:24:16-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-4 18:24:16-debug: asset-db:refresh-all-database (121ms)
2025-9-4 18:24:16-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-4 18:24:16-debug: %cImport%c: E:\M2Game\Client\assets\scenes\BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-4 18:24:16-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (3ms)
2025-9-4 18:30:17-debug: refresh db internal success
2025-9-4 18:30:17-debug: %cImport%c: E:\M2Game\Client\assets\editor\emitter\EmitterEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-4 18:30:17-debug: refresh db assets success
2025-9-4 18:30:17-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-4 18:30:17-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-4 18:30:17-debug: asset-db:refresh-all-database (146ms)
2025-9-4 18:30:17-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-4 18:30:17-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-4 18:31:56-debug: refresh db internal success
2025-9-4 18:31:56-debug: %cImport%c: E:\M2Game\Client\assets\editor\emitter\EmitterEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-4 18:31:56-debug: refresh db assets success
2025-9-4 18:31:56-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-4 18:31:56-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-4 18:31:56-debug: asset-db:refresh-all-database (143ms)
2025-9-4 18:31:56-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-4 18:31:56-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-4 18:32:01-debug: refresh db internal success
2025-9-4 18:32:01-debug: refresh db assets success
2025-9-4 18:32:01-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-4 18:32:01-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-4 18:32:01-debug: asset-db:refresh-all-database (100ms)
2025-9-4 18:32:01-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-4 18:32:01-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-4 18:32:29-debug: refresh db internal success
2025-9-4 18:32:29-debug: refresh db assets success
2025-9-4 18:32:29-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-4 18:32:29-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-4 18:32:29-debug: asset-db:refresh-all-database (132ms)
2025-9-4 18:32:29-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-4 18:32:29-debug: asset-db-hook-engine-extends-afterRefresh (3ms)
2025-9-4 18:32:46-debug: refresh db internal success
2025-9-4 18:32:46-debug: refresh db assets success
2025-9-4 18:32:46-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-4 18:32:46-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-4 18:32:46-debug: asset-db:refresh-all-database (123ms)
2025-9-4 18:32:46-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-4 18:32:46-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-4 18:33:51-debug: refresh db internal success
2025-9-4 18:33:51-debug: %cImport%c: E:\M2Game\Client\assets\editor\emitter\EmitterEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-4 18:33:51-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-4 18:33:51-debug: refresh db assets success
2025-9-4 18:33:51-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-4 18:33:51-debug: asset-db:refresh-all-database (136ms)
2025-9-4 18:33:51-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-4 18:33:51-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-4 18:34:15-debug: Query all assets info in project
2025-9-4 18:34:15-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-4 18:34:15-debug: Skip compress image, progress: 0%
2025-9-4 18:34:15-debug: Init all bundles start..., progress: 0%
2025-9-4 18:34:15-debug: Num of bundles: 3..., progress: 0%
2025-9-4 18:34:15-debug: 查询 Asset Bundle start, progress: 0%
2025-9-4 18:34:15-debug: // ---- build task 查询 Asset Bundle ----
2025-9-4 18:34:15-debug: Init bundle root assets start..., progress: 0%
2025-9-4 18:34:15-debug:   Number of all scripts: 237
2025-9-4 18:34:15-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-4 18:34:15-debug:   Number of other assets: 1910
2025-9-4 18:34:15-debug: Init bundle root assets success..., progress: 0%
2025-9-4 18:34:15-debug:   Number of all scenes: 8
2025-9-4 18:34:15-debug: // ---- build task 查询 Asset Bundle ---- (21ms)
2025-9-4 18:34:15-debug: [Build Memory track]: 查询 Asset Bundle start:220.01MB, end 221.51MB, increase: 1.49MB
2025-9-4 18:34:15-debug: 查询 Asset Bundle start, progress: 5%
2025-9-4 18:34:15-log: run build task 查询 Asset Bundle success in 21 ms√, progress: 5%
2025-9-4 18:34:15-debug: // ---- build task 查询 Asset Bundle ----
2025-9-4 18:34:15-debug: // ---- build task 查询 Asset Bundle ---- (5ms)
2025-9-4 18:34:15-debug: [Build Memory track]: 查询 Asset Bundle start:221.54MB, end 221.79MB, increase: 259.10KB
2025-9-4 18:34:15-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-4 18:34:15-log: run build task 查询 Asset Bundle success in 5 ms√, progress: 10%
2025-9-4 18:34:15-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-4 18:34:15-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-9-4 18:34:15-log: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 12%
2025-9-4 18:34:15-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:221.83MB, end 221.86MB, increase: 31.97KB
2025-9-4 18:34:15-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-4 18:34:15-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-4 18:34:15-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-9-4 18:34:15-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-9-4 18:34:15-debug: [Build Memory track]: 填充脚本数据到 settings.json start:221.90MB, end 221.93MB, increase: 31.19KB
2025-9-4 18:34:15-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-4 18:34:15-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-4 18:34:15-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (4ms)
2025-9-4 18:34:15-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:221.96MB, end 222.26MB, increase: 301.74KB
2025-9-4 18:34:15-log: run build task 整理部分构建选项内数据到 settings.json success in 4 ms√, progress: 15%
2025-9-4 18:34:16-debug: Query all assets info in project
2025-9-4 18:34:16-debug: Query all assets info in project
2025-9-4 18:34:16-debug: Query all assets info in project
2025-9-4 18:34:16-debug: Query all assets info in project
2025-9-4 18:34:16-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-4 18:34:16-debug: Skip compress image, progress: 0%
2025-9-4 18:34:16-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-4 18:34:16-debug: Skip compress image, progress: 0%
2025-9-4 18:34:16-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-4 18:34:16-debug: Skip compress image, progress: 0%
2025-9-4 18:34:16-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-4 18:34:16-debug: Skip compress image, progress: 0%
2025-9-4 18:34:16-debug: Init all bundles start..., progress: 0%
2025-9-4 18:34:16-debug: 查询 Asset Bundle start, progress: 0%
2025-9-4 18:34:16-debug: Num of bundles: 3..., progress: 0%
2025-9-4 18:34:16-debug: // ---- build task 查询 Asset Bundle ----
2025-9-4 18:34:16-debug: Init bundle root assets start..., progress: 0%
2025-9-4 18:34:16-debug: Num of bundles: 3..., progress: 0%
2025-9-4 18:34:16-debug: Init all bundles start..., progress: 0%
2025-9-4 18:34:16-debug: // ---- build task 查询 Asset Bundle ----
2025-9-4 18:34:16-debug: Init bundle root assets start..., progress: 0%
2025-9-4 18:34:16-debug: 查询 Asset Bundle start, progress: 0%
2025-9-4 18:34:16-debug: Num of bundles: 3..., progress: 0%
2025-9-4 18:34:16-debug: Init all bundles start..., progress: 0%
2025-9-4 18:34:16-debug: // ---- build task 查询 Asset Bundle ----
2025-9-4 18:34:16-debug: Init bundle root assets start..., progress: 0%
2025-9-4 18:34:16-debug: 查询 Asset Bundle start, progress: 0%
2025-9-4 18:34:16-debug: Init all bundles start..., progress: 0%
2025-9-4 18:34:16-debug: // ---- build task 查询 Asset Bundle ----
2025-9-4 18:34:16-debug: Num of bundles: 3..., progress: 0%
2025-9-4 18:34:16-debug: 查询 Asset Bundle start, progress: 0%
2025-9-4 18:34:16-debug: Init bundle root assets start..., progress: 0%
2025-9-4 18:34:16-debug:   Number of all scenes: 8
2025-9-4 18:34:16-debug:   Number of all scripts: 237
2025-9-4 18:34:16-debug: Init bundle root assets success..., progress: 0%
2025-9-4 18:34:16-debug: // ---- build task 查询 Asset Bundle ---- (21ms)
2025-9-4 18:34:16-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-4 18:34:16-log: run build task 查询 Asset Bundle success in 21 ms√, progress: 5%
2025-9-4 18:34:16-debug: [Build Memory track]: 查询 Asset Bundle start:222.14MB, end 222.42MB, increase: 286.98KB
2025-9-4 18:34:16-debug: // ---- build task 查询 Asset Bundle ----
2025-9-4 18:34:16-debug:   Number of other assets: 1910
2025-9-4 18:34:16-debug: 查询 Asset Bundle start, progress: 5%
2025-9-4 18:34:16-debug: // ---- build task 查询 Asset Bundle ---- (6ms)
2025-9-4 18:34:16-debug: [Build Memory track]: 查询 Asset Bundle start:222.45MB, end 222.71MB, increase: 268.38KB
2025-9-4 18:34:16-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-4 18:34:16-log: run build task 查询 Asset Bundle success in 6 ms√, progress: 10%
2025-9-4 18:34:16-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-4 18:34:16-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-4 18:34:16-debug:   Number of all scripts: 237
2025-9-4 18:34:16-debug:   Number of other assets: 1910
2025-9-4 18:34:16-debug: Init bundle root assets success..., progress: 0%
2025-9-4 18:34:16-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-4 18:34:16-debug:   Number of all scenes: 8
2025-9-4 18:34:16-debug:   Number of all scripts: 237
2025-9-4 18:34:16-debug:   Number of other assets: 1910
2025-9-4 18:34:16-debug: Init bundle root assets success..., progress: 0%
2025-9-4 18:34:16-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-4 18:34:16-debug:   Number of all scenes: 8
2025-9-4 18:34:16-debug:   Number of all scripts: 237
2025-9-4 18:34:16-debug:   Number of other assets: 1910
2025-9-4 18:34:16-debug: Init bundle root assets success..., progress: 0%
2025-9-4 18:34:16-debug:   Number of all scenes: 8
2025-9-4 18:34:16-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (69ms)
2025-9-4 18:34:16-log: run build task 整理部分构建选项内数据到 settings.json success in 69 ms√, progress: 12%
2025-9-4 18:34:16-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:222.76MB, end 207.21MB, increase: -15919.59KB
2025-9-4 18:34:16-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-4 18:34:16-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-4 18:34:16-log: run build task 查询 Asset Bundle success in √, progress: 5%
2025-9-4 18:34:16-debug: 查询 Asset Bundle start, progress: 5%
2025-9-4 18:34:16-debug: // ---- build task 查询 Asset Bundle ----
2025-9-4 18:34:16-debug: [Build Memory track]: 查询 Asset Bundle start:207.30MB, end 207.31MB, increase: 14.50KB
2025-9-4 18:34:16-log: run build task 查询 Asset Bundle success in √, progress: 5%
2025-9-4 18:34:16-log: run build task 查询 Asset Bundle success in √, progress: 5%
2025-9-4 18:34:16-debug: [Build Memory track]: 查询 Asset Bundle start:207.35MB, end 207.37MB, increase: 15.74KB
2025-9-4 18:34:16-debug: 查询 Asset Bundle start, progress: 5%
2025-9-4 18:34:16-debug: // ---- build task 查询 Asset Bundle ----
2025-9-4 18:34:16-debug: 查询 Asset Bundle start, progress: 5%
2025-9-4 18:34:16-debug: // ---- build task 查询 Asset Bundle ----
2025-9-4 18:34:16-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-9-4 18:34:16-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-4 18:34:16-debug: [Build Memory track]: 填充脚本数据到 settings.json start:207.24MB, end 207.44MB, increase: 195.52KB
2025-9-4 18:34:16-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-9-4 18:34:16-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-4 18:34:16-debug: // ---- build task 查询 Asset Bundle ---- (3ms)
2025-9-4 18:34:16-log: run build task 查询 Asset Bundle success in 3 ms√, progress: 10%
2025-9-4 18:34:16-debug: [Build Memory track]: 查询 Asset Bundle start:207.40MB, end 208.18MB, increase: 796.14KB
2025-9-4 18:34:16-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-4 18:34:16-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-4 18:34:16-log: run build task 查询 Asset Bundle success in √, progress: 10%
2025-9-4 18:34:16-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-4 18:34:16-log: run build task 查询 Asset Bundle success in √, progress: 10%
2025-9-4 18:34:16-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-4 18:34:16-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-4 18:34:16-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-4 18:34:16-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-9-4 18:34:16-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-4 18:34:16-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-9-4 18:34:16-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:208.30MB, end 208.34MB, increase: 37.23KB
2025-9-4 18:34:16-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-4 18:34:16-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-9-4 18:34:16-log: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 12%
2025-9-4 18:34:16-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-4 18:34:16-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-4 18:34:16-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-9-4 18:34:16-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-9-4 18:34:16-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-4 18:34:16-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-9-4 18:34:16-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-4 18:34:16-debug: [Build Memory track]: 填充脚本数据到 settings.json start:208.48MB, end 208.50MB, increase: 18.87KB
2025-9-4 18:34:16-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-4 18:34:16-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-4 18:34:16-debug: // ---- build task 填充脚本数据到 settings.json ---- (2ms)
2025-9-4 18:34:16-log: run build task 填充脚本数据到 settings.json success in 2 ms√, progress: 13%
2025-9-4 18:34:16-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-4 18:34:16-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-4 18:34:16-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-9-4 18:34:16-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-9-4 18:34:16-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-4 18:34:16-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-4 18:34:16-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-9-4 18:34:16-log: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 15%
2025-9-4 18:34:16-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:208.64MB, end 208.95MB, increase: 317.80KB
2025-9-4 18:34:16-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 15%
2025-9-4 18:34:16-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 15%
2025-9-4 18:34:16-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 15%
2025-9-4 18:35:07-debug: refresh db internal success
2025-9-4 18:35:08-debug: refresh db assets success
2025-9-4 18:35:08-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-4 18:35:08-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-4 18:35:08-debug: asset-db:refresh-all-database (124ms)
2025-9-4 18:35:08-debug: asset-db:worker-effect-data-processing (3ms)
2025-9-4 18:35:08-debug: asset-db-hook-engine-extends-afterRefresh (3ms)
2025-9-4 18:37:21-debug: refresh db internal success
2025-9-4 18:37:21-debug: %cImport%c: E:\M2Game\Client\assets\editor\emitter\EmitterEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-4 18:37:21-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-4 18:37:21-debug: refresh db assets success
2025-9-4 18:37:21-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-4 18:37:21-debug: asset-db:refresh-all-database (133ms)
2025-9-4 18:37:21-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-4 18:37:21-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-4 18:38:10-debug: refresh db internal success
2025-9-4 18:38:10-debug: %cImport%c: E:\M2Game\Client\assets\editor\emitter\EmitterEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-4 18:38:10-debug: refresh db assets success
2025-9-4 18:38:10-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-4 18:38:10-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-4 18:38:10-debug: asset-db:refresh-all-database (128ms)
2025-9-4 18:38:10-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-4 18:38:10-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-4 18:38:27-debug: refresh db internal success
2025-9-4 18:38:27-debug: %cImport%c: E:\M2Game\Client\assets\editor\emitter\EmitterEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-4 18:38:27-debug: refresh db assets success
2025-9-4 18:38:27-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-4 18:38:27-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-4 18:38:27-debug: asset-db:refresh-all-database (105ms)
2025-9-4 18:38:27-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-4 18:38:27-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-4 18:38:50-debug: refresh db internal success
2025-9-4 18:38:50-debug: %cImport%c: E:\M2Game\Client\assets\editor\emitter\EmitterEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-4 18:38:50-debug: refresh db assets success
2025-9-4 18:38:50-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-4 18:38:50-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-4 18:38:50-debug: asset-db:refresh-all-database (133ms)
2025-9-4 18:38:50-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-4 18:38:50-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-4 18:41:00-debug: Query all assets info in project
2025-9-4 18:41:00-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-4 18:41:00-debug: Skip compress image, progress: 0%
2025-9-4 18:41:00-debug: Num of bundles: 3..., progress: 0%
2025-9-4 18:41:00-debug: Init all bundles start..., progress: 0%
2025-9-4 18:41:00-debug: 查询 Asset Bundle start, progress: 0%
2025-9-4 18:41:00-debug: // ---- build task 查询 Asset Bundle ----
2025-9-4 18:41:00-debug: Init bundle root assets start..., progress: 0%
2025-9-4 18:41:00-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-4 18:41:00-debug:   Number of all scenes: 8
2025-9-4 18:41:00-debug:   Number of other assets: 1910
2025-9-4 18:41:00-debug: Init bundle root assets success..., progress: 0%
2025-9-4 18:41:00-debug:   Number of all scripts: 237
2025-9-4 18:41:00-debug: // ---- build task 查询 Asset Bundle ---- (29ms)
2025-9-4 18:41:00-log: run build task 查询 Asset Bundle success in 29 ms√, progress: 5%
2025-9-4 18:41:00-debug: 查询 Asset Bundle start, progress: 5%
2025-9-4 18:41:00-debug: [Build Memory track]: 查询 Asset Bundle start:221.42MB, end 222.43MB, increase: 1.01MB
2025-9-4 18:41:00-debug: // ---- build task 查询 Asset Bundle ----
2025-9-4 18:41:00-debug: // ---- build task 查询 Asset Bundle ---- (3ms)
2025-9-4 18:41:00-debug: [Build Memory track]: 查询 Asset Bundle start:222.46MB, end 221.93MB, increase: -536.40KB
2025-9-4 18:41:00-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-4 18:41:00-log: run build task 查询 Asset Bundle success in 3 ms√, progress: 10%
2025-9-4 18:41:00-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-4 18:41:00-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-9-4 18:41:00-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:221.96MB, end 222.01MB, increase: 46.00KB
2025-9-4 18:41:00-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-4 18:41:00-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-4 18:41:00-debug: refresh db internal success
2025-9-4 18:41:00-log: run build task 填充脚本数据到 settings.json success in 31 ms√, progress: 13%
2025-9-4 18:41:00-debug: // ---- build task 填充脚本数据到 settings.json ---- (31ms)
2025-9-4 18:41:00-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-4 18:41:00-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-4 18:41:00-debug: [Build Memory track]: 填充脚本数据到 settings.json start:222.03MB, end 223.33MB, increase: 1.30MB
2025-9-4 18:41:00-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (4ms)
2025-9-4 18:41:00-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:223.36MB, end 222.66MB, increase: -719.11KB
2025-9-4 18:41:00-log: run build task 整理部分构建选项内数据到 settings.json success in 4 ms√, progress: 15%
2025-9-4 18:41:00-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-4 18:41:00-debug: refresh db assets success
2025-9-4 18:41:00-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-4 18:41:00-debug: asset-db:refresh-all-database (179ms)
2025-9-4 18:43:32-debug: refresh db internal success
2025-9-4 18:43:32-debug: %cImport%c: E:\M2Game\Client\assets\editor\emitter\EmitterEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-4 18:43:32-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-4 18:43:32-debug: refresh db assets success
2025-9-4 18:43:32-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-4 18:43:32-debug: asset-db:refresh-all-database (143ms)
2025-9-4 18:43:32-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-4 18:43:32-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-4 18:44:09-debug: refresh db internal success
2025-9-4 18:44:09-debug: refresh db assets success
2025-9-4 18:44:09-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-4 18:44:09-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-4 18:44:09-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-4 18:44:09-debug: asset-db:refresh-all-database (111ms)
2025-9-4 18:44:09-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-4 18:44:30-debug: refresh db internal success
2025-9-4 18:44:30-debug: refresh db assets success
2025-9-4 18:44:30-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-4 18:44:30-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-4 18:44:30-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-4 18:44:30-debug: asset-db:refresh-all-database (105ms)
2025-9-4 18:44:30-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-4 18:44:52-debug: refresh db internal success
2025-9-4 18:44:52-debug: %cImport%c: E:\M2Game\Client\assets\editor\emitter\EmitterEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-4 18:44:52-debug: refresh db assets success
2025-9-4 18:44:52-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-4 18:44:52-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-4 18:44:52-debug: asset-db:worker-effect-data-processing (3ms)
2025-9-4 18:44:52-debug: asset-db-hook-engine-extends-afterRefresh (3ms)
2025-9-4 18:44:52-debug: asset-db:refresh-all-database (136ms)
2025-9-4 18:47:20-debug: refresh db internal success
2025-9-4 18:47:20-debug: %cImport%c: E:\M2Game\Client\assets\editor\emitter\EmitterEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-4 18:47:20-debug: refresh db assets success
2025-9-4 18:47:20-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-4 18:47:20-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-4 18:47:20-debug: asset-db:refresh-all-database (146ms)
2025-9-4 18:47:20-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-4 18:47:20-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-4 18:47:26-debug: refresh db internal success
2025-9-4 18:47:26-debug: refresh db assets success
2025-9-4 18:47:26-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-4 18:47:26-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-4 18:47:26-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-4 18:47:26-debug: asset-db:refresh-all-database (115ms)
2025-9-4 18:47:26-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-4 18:47:27-debug: refresh db internal success
2025-9-4 18:47:27-debug: refresh db assets success
2025-9-4 18:47:27-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-4 18:47:27-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-4 18:47:27-debug: asset-db:refresh-all-database (146ms)
2025-9-4 18:47:27-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-4 18:47:27-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-4 18:48:16-debug: refresh db internal success
2025-9-4 18:48:16-debug: refresh db assets success
2025-9-4 18:48:16-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-4 18:48:16-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-4 18:48:16-debug: asset-db:refresh-all-database (129ms)
2025-9-4 18:48:16-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-4 18:50:02-debug: refresh db internal success
2025-9-4 18:50:02-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-4 18:50:02-debug: refresh db assets success
2025-9-4 18:50:02-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-4 18:50:02-debug: asset-db:refresh-all-database (136ms)
2025-9-4 18:50:02-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-4 18:50:02-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-4 18:52:38-debug: refresh db internal success
2025-9-4 18:52:38-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-4 18:52:38-debug: refresh db assets success
2025-9-4 18:52:38-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-4 18:52:38-debug: asset-db:refresh-all-database (155ms)
2025-9-4 18:52:38-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-4 18:52:38-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-4 18:53:03-debug: refresh db internal success
2025-9-4 18:53:03-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-4 18:53:03-debug: refresh db assets success
2025-9-4 18:53:03-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-4 18:53:03-debug: asset-db:refresh-all-database (97ms)
2025-9-4 18:53:03-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-4 18:53:03-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-4 18:54:39-debug: refresh db internal success
2025-9-4 18:54:39-debug: refresh db assets success
2025-9-4 18:54:39-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-4 18:54:39-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-4 18:54:39-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-4 18:54:39-debug: asset-db:refresh-all-database (115ms)
2025-9-4 18:54:39-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-4 18:54:43-debug: refresh db internal success
2025-9-4 18:54:43-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-4 18:54:43-debug: refresh db assets success
2025-9-4 18:54:43-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-4 18:54:43-debug: asset-db:refresh-all-database (120ms)
2025-9-4 18:54:43-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-4 18:54:51-debug: refresh db internal success
2025-9-4 18:54:51-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-4 18:54:51-debug: refresh db assets success
2025-9-4 18:54:51-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-4 18:54:51-debug: asset-db:refresh-all-database (95ms)
2025-9-4 18:56:13-debug: refresh db internal success
2025-9-4 18:56:13-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-4 18:56:13-debug: refresh db assets success
2025-9-4 18:56:13-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-4 18:56:13-debug: asset-db:refresh-all-database (118ms)
2025-9-4 18:56:17-debug: refresh db internal success
2025-9-4 18:56:18-debug: refresh db assets success
2025-9-4 18:56:18-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-4 18:56:18-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-4 18:56:18-debug: asset-db:refresh-all-database (116ms)
2025-9-4 18:56:45-debug: refresh db internal success
2025-9-4 18:56:46-debug: refresh db assets success
2025-9-4 18:56:46-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-4 18:56:46-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-4 18:56:46-debug: asset-db:refresh-all-database (117ms)
2025-9-4 18:57:38-debug: refresh db internal success
2025-9-4 18:57:38-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-4 18:57:38-debug: refresh db assets success
2025-9-4 18:57:38-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-4 18:57:38-debug: asset-db:refresh-all-database (115ms)
2025-9-4 18:58:16-debug: refresh db internal success
2025-9-4 18:58:16-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-4 18:58:16-debug: refresh db assets success
2025-9-4 18:58:16-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-4 18:58:16-debug: asset-db:refresh-all-database (115ms)
2025-9-4 18:58:16-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-4 18:59:36-debug: refresh db internal success
2025-9-4 18:59:36-debug: refresh db assets success
2025-9-4 18:59:36-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-4 18:59:36-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-4 18:59:36-debug: asset-db:refresh-all-database (122ms)
2025-9-4 18:59:36-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-4 18:59:36-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-4 18:59:49-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\prefabs\emitter\EmitterExample.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-4 18:59:49-debug: asset-db:reimport-assetb92946b3-aaf8-470f-812a-923a1d1c99f1 (5ms)
2025-9-4 18:59:58-debug: refresh db internal success
2025-9-4 18:59:58-debug: refresh db assets success
2025-9-4 18:59:58-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-4 18:59:58-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-4 18:59:58-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-4 18:59:58-debug: asset-db:refresh-all-database (100ms)
2025-9-4 18:59:58-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-4 19:00:20-debug: refresh db internal success
2025-9-4 19:00:20-debug: refresh db assets success
2025-9-4 19:00:20-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-4 19:00:20-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-4 19:00:20-debug: asset-db:refresh-all-database (105ms)
2025-9-4 19:00:20-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-4 19:00:29-debug: refresh db internal success
2025-9-4 19:00:29-debug: refresh db assets success
2025-9-4 19:00:29-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-4 19:00:29-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-4 19:00:29-debug: asset-db:refresh-all-database (101ms)
2025-9-4 19:00:29-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-4 19:00:36-debug: refresh db internal success
2025-9-4 19:00:36-debug: refresh db assets success
2025-9-4 19:00:36-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-4 19:00:36-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-4 19:00:36-debug: asset-db:worker-effect-data-processing (4ms)
2025-9-4 19:00:36-debug: asset-db:refresh-all-database (130ms)
2025-9-4 19:00:36-debug: asset-db-hook-engine-extends-afterRefresh (4ms)
2025-9-4 19:00:46-debug: refresh db internal success
2025-9-4 19:00:46-debug: refresh db assets success
2025-9-4 19:00:46-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-4 19:00:46-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-4 19:00:46-debug: asset-db:refresh-all-database (116ms)
2025-9-4 19:00:46-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-4 19:01:12-debug: refresh db internal success
2025-9-4 19:01:12-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\prefabs\emitter\EmitterExample.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-4 19:01:12-debug: refresh db assets success
2025-9-4 19:01:12-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-4 19:01:12-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-4 19:01:12-debug: asset-db:refresh-all-database (139ms)
2025-9-4 19:01:12-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-4 19:01:12-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-4 19:01:36-debug: refresh db internal success
2025-9-4 19:01:36-debug: refresh db assets success
2025-9-4 19:01:36-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-4 19:01:36-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-4 19:01:36-debug: asset-db:refresh-all-database (142ms)
2025-9-4 19:01:36-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-4 19:01:36-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-4 19:04:56-debug: refresh db internal success
2025-9-4 19:04:57-debug: refresh db assets success
2025-9-4 19:04:57-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-4 19:04:57-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-4 19:04:57-debug: asset-db:refresh-all-database (122ms)
2025-9-4 19:05:14-debug: refresh db internal success
2025-9-4 19:05:14-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\prefabs\emitter
background: #aaff85; color: #000;
color: #000;
2025-9-4 19:05:14-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\prefabs\emitter\EmitterExample.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-4 19:05:14-debug: refresh db assets success
2025-9-4 19:05:14-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-4 19:05:14-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-4 19:05:14-debug: asset-db:refresh-all-database (135ms)
2025-9-4 19:05:14-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-4 19:05:14-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-4 19:06:22-debug: refresh db internal success
2025-9-4 19:06:22-debug: refresh db assets success
2025-9-4 19:06:22-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-4 19:06:22-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-4 19:06:22-debug: asset-db:refresh-all-database (99ms)
2025-9-4 19:06:22-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-4 19:06:22-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-4 19:06:32-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\prefabs\emitter\EmitterExample.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-4 19:06:32-debug: asset-db:reimport-assetb92946b3-aaf8-470f-812a-923a1d1c99f1 (3ms)
2025-9-4 19:06:39-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\prefabs\emitter\EmitterExample.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-4 19:06:39-debug: asset-db:reimport-assetb92946b3-aaf8-470f-812a-923a1d1c99f1 (3ms)
2025-9-4 19:06:51-debug: refresh db internal success
2025-9-4 19:06:52-debug: refresh db assets success
2025-9-4 19:06:52-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-4 19:06:52-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-4 19:06:52-debug: asset-db:refresh-all-database (113ms)
2025-9-4 19:06:52-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-4 19:06:52-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-4 19:07:30-debug: refresh db internal success
2025-9-4 19:07:30-debug: refresh db assets success
2025-9-4 19:07:30-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-4 19:07:30-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-4 19:07:30-debug: asset-db:refresh-all-database (100ms)
2025-9-4 19:07:30-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-4 19:07:30-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-4 19:09:32-debug: refresh db internal success
2025-9-4 19:09:32-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-4 19:09:32-debug: refresh db assets success
2025-9-4 19:09:32-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-4 19:09:32-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-4 19:09:32-debug: asset-db:refresh-all-database (135ms)
2025-9-4 19:09:32-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-4 19:09:38-debug: refresh db internal success
2025-9-4 19:09:38-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-4 19:09:38-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-4 19:09:38-debug: refresh db assets success
2025-9-4 19:09:38-debug: asset-db:refresh-all-database (101ms)
2025-9-4 19:09:38-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-4 19:09:38-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-4 19:13:12-debug: refresh db internal success
2025-9-4 19:13:12-debug: refresh db assets success
2025-9-4 19:13:12-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-4 19:13:12-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-4 19:13:12-debug: asset-db:refresh-all-database (115ms)
2025-9-4 19:13:16-debug: %cImport%c: E:\M2Game\Client\assets\scenes\BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-4 19:13:16-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (3ms)
2025-9-4 19:13:17-debug: refresh db internal success
2025-9-4 19:13:17-debug: refresh db assets success
2025-9-4 19:13:17-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-4 19:13:17-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-4 19:13:17-debug: asset-db:refresh-all-database (123ms)
2025-9-4 19:13:17-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-4 19:13:37-debug: start refresh asset from E:\M2Game\Client\assets\resources\Game\prefabs\emitter\EmitterExample.prefab...
2025-9-4 19:13:37-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\prefabs\emitter\EmitterExample.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-4 19:13:37-debug: refresh asset E:\M2Game\Client\assets\resources\Game\prefabs\emitter success
2025-9-4 19:13:38-debug: refresh db internal success
2025-9-4 19:13:38-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-4 19:13:38-debug: refresh db assets success
2025-9-4 19:13:38-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-4 19:13:38-debug: asset-db:refresh-all-database (103ms)
2025-9-4 19:13:52-debug: refresh db internal success
2025-9-4 19:13:52-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-4 19:13:52-debug: refresh db assets success
2025-9-4 19:13:52-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-4 19:13:52-debug: asset-db:refresh-all-database (125ms)
2025-9-4 19:13:52-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-4 19:13:54-debug: Query all assets info in project
2025-9-4 19:13:54-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-4 19:13:54-debug: Skip compress image, progress: 0%
2025-9-4 19:13:54-debug: Init all bundles start..., progress: 0%
2025-9-4 19:13:54-debug: Num of bundles: 3..., progress: 0%
2025-9-4 19:13:54-debug: 查询 Asset Bundle start, progress: 0%
2025-9-4 19:13:54-debug: // ---- build task 查询 Asset Bundle ----
2025-9-4 19:13:54-debug: Init bundle root assets start..., progress: 0%
2025-9-4 19:13:54-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-4 19:13:54-debug:   Number of all scenes: 8
2025-9-4 19:13:54-debug:   Number of other assets: 1910
2025-9-4 19:13:54-debug:   Number of all scripts: 237
2025-9-4 19:13:54-debug: Init bundle root assets success..., progress: 0%
2025-9-4 19:13:54-log: run build task 查询 Asset Bundle success in 22 ms√, progress: 5%
2025-9-4 19:13:54-debug: // ---- build task 查询 Asset Bundle ---- (22ms)
2025-9-4 19:13:54-debug: [Build Memory track]: 查询 Asset Bundle start:214.06MB, end 214.46MB, increase: 405.42KB
2025-9-4 19:13:54-debug: 查询 Asset Bundle start, progress: 5%
2025-9-4 19:13:54-debug: // ---- build task 查询 Asset Bundle ----
2025-9-4 19:13:54-debug: // ---- build task 查询 Asset Bundle ---- (1ms)
2025-9-4 19:13:54-log: run build task 查询 Asset Bundle success in 1 ms√, progress: 10%
2025-9-4 19:13:54-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-4 19:13:54-debug: [Build Memory track]: 查询 Asset Bundle start:214.49MB, end 214.73MB, increase: 252.89KB
2025-9-4 19:13:54-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-4 19:13:54-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-9-4 19:13:54-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-4 19:13:54-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:214.76MB, end 214.78MB, increase: 17.42KB
2025-9-4 19:13:54-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-4 19:13:54-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-9-4 19:13:54-debug: [Build Memory track]: 填充脚本数据到 settings.json start:214.81MB, end 214.82MB, increase: 15.60KB
2025-9-4 19:13:54-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-4 19:13:54-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-4 19:13:54-log: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 15%
2025-9-4 19:13:54-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:214.85MB, end 215.14MB, increase: 296.33KB
2025-9-4 19:13:54-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-9-4 19:14:24-debug: start refresh asset from E:\M2Game\Client\assets\resources\Game\prefabs\emitter\EmitterExample.prefab...
2025-9-4 19:14:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\prefabs\emitter\EmitterExample.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-4 19:14:24-debug: refresh asset E:\M2Game\Client\assets\resources\Game\prefabs\emitter success
2025-9-4 19:14:24-debug: refresh db internal success
2025-9-4 19:14:24-debug: refresh db assets success
2025-9-4 19:14:24-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-4 19:14:24-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-4 19:14:24-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-4 19:14:24-debug: asset-db:refresh-all-database (100ms)
2025-9-4 19:14:24-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-4 19:14:46-debug: refresh db internal success
2025-9-4 19:14:46-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-4 19:14:46-debug: refresh db assets success
2025-9-4 19:14:46-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-4 19:14:46-debug: asset-db:refresh-all-database (96ms)
2025-9-4 19:14:46-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-4 19:14:46-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-4 19:15:17-debug: refresh db internal success
2025-9-4 19:15:17-debug: refresh db assets success
2025-9-4 19:15:17-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-4 19:15:17-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-4 19:15:17-debug: asset-db:refresh-all-database (96ms)
2025-9-4 19:15:21-debug: start refresh asset from E:\M2Game\Client\assets\resources\Game\prefabs\emitter\EmitterExample.prefab...
2025-9-4 19:15:21-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\prefabs\emitter\EmitterExample.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-4 19:15:21-debug: refresh asset E:\M2Game\Client\assets\resources\Game\prefabs\emitter success
2025-9-4 19:15:21-debug: refresh db internal success
2025-9-4 19:15:21-debug: refresh db assets success
2025-9-4 19:15:21-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-4 19:15:21-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-4 19:15:21-debug: asset-db:refresh-all-database (109ms)
2025-9-4 19:15:21-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-4 19:15:21-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-4 19:15:35-debug: start refresh asset from E:\M2Game\Client\assets\resources\Game\prefabs\emitter\EmitterExample.prefab...
2025-9-4 19:15:35-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\prefabs\emitter\EmitterExample.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-4 19:15:35-debug: refresh asset E:\M2Game\Client\assets\resources\Game\prefabs\emitter success
2025-9-4 19:15:35-debug: refresh db internal success
2025-9-4 19:15:35-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-4 19:15:35-debug: refresh db assets success
2025-9-4 19:15:35-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-4 19:15:35-debug: asset-db:refresh-all-database (104ms)
2025-9-4 19:15:35-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-4 19:15:35-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-4 19:15:46-debug: Query all assets info in project
2025-9-4 19:15:46-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-4 19:15:46-debug: Skip compress image, progress: 0%
2025-9-4 19:15:46-debug: Init all bundles start..., progress: 0%
2025-9-4 19:15:46-debug: 查询 Asset Bundle start, progress: 0%
2025-9-4 19:15:46-debug: // ---- build task 查询 Asset Bundle ----
2025-9-4 19:15:46-debug: Num of bundles: 3..., progress: 0%
2025-9-4 19:15:46-debug: Init bundle root assets start..., progress: 0%
2025-9-4 19:15:46-debug:   Number of all scripts: 237
2025-9-4 19:15:46-debug:   Number of other assets: 1910
2025-9-4 19:15:46-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-4 19:15:46-debug:   Number of all scenes: 8
2025-9-4 19:15:46-debug: Init bundle root assets success..., progress: 0%
2025-9-4 19:15:46-debug: // ---- build task 查询 Asset Bundle ---- (18ms)
2025-9-4 19:15:46-log: run build task 查询 Asset Bundle success in 18 ms√, progress: 5%
2025-9-4 19:15:46-debug: 查询 Asset Bundle start, progress: 5%
2025-9-4 19:15:46-debug: // ---- build task 查询 Asset Bundle ----
2025-9-4 19:15:46-debug: [Build Memory track]: 查询 Asset Bundle start:207.88MB, end 208.34MB, increase: 467.16KB
2025-9-4 19:15:46-debug: // ---- build task 查询 Asset Bundle ---- (2ms)
2025-9-4 19:15:46-debug: [Build Memory track]: 查询 Asset Bundle start:208.37MB, end 208.61MB, increase: 252.95KB
2025-9-4 19:15:46-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-4 19:15:46-log: run build task 查询 Asset Bundle success in 2 ms√, progress: 10%
2025-9-4 19:15:46-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-4 19:15:46-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-9-4 19:15:46-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:208.64MB, end 208.67MB, increase: 25.96KB
2025-9-4 19:15:46-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-9-4 19:15:46-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-4 19:15:46-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-4 19:15:46-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-9-4 19:15:46-debug: [Build Memory track]: 填充脚本数据到 settings.json start:208.70MB, end 208.71MB, increase: 16.41KB
2025-9-4 19:15:46-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-4 19:15:46-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-4 19:15:46-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-9-4 19:15:46-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 15%
2025-9-4 19:15:46-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:208.74MB, end 209.05MB, increase: 309.74KB
2025-9-4 19:16:11-debug: start refresh asset from E:\M2Game\Client\assets\resources\Game\prefabs\emitter\EmitterExample.prefab...
2025-9-4 19:16:11-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\prefabs\emitter\EmitterExample.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-4 19:16:11-debug: refresh asset E:\M2Game\Client\assets\resources\Game\prefabs\emitter success
2025-9-4 19:16:11-debug: refresh db internal success
2025-9-4 19:16:11-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-4 19:16:11-debug: refresh db assets success
2025-9-4 19:16:11-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-4 19:16:11-debug: asset-db:refresh-all-database (107ms)
2025-9-4 19:16:11-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-4 19:16:26-debug: start refresh asset from E:\M2Game\Client\assets\resources\Game\prefabs\emitter\EmitterExample.prefab...
2025-9-4 19:16:26-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\prefabs\emitter\EmitterExample.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-4 19:16:26-debug: refresh asset E:\M2Game\Client\assets\resources\Game\prefabs\emitter success
2025-9-4 19:16:26-debug: refresh db internal success
2025-9-4 19:16:26-debug: refresh db assets success
2025-9-4 19:16:26-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-4 19:16:26-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-4 19:16:26-debug: asset-db:refresh-all-database (99ms)
2025-9-4 19:16:26-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 01:26:16-debug: refresh db internal success
2025-9-5 01:26:16-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 01:26:16-debug: refresh db assets success
2025-9-5 01:26:16-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 01:26:16-debug: asset-db:refresh-all-database (139ms)
2025-9-5 01:26:16-debug: asset-db:worker-effect-data-processing (6ms)
2025-9-5 01:26:16-debug: asset-db-hook-engine-extends-afterRefresh (6ms)
2025-9-5 09:36:51-debug: refresh db internal success
2025-9-5 09:36:51-debug: %cImport%c: E:\M2Game\Client\assets\editor\emitter\EmitterEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 09:36:51-debug: refresh db assets success
2025-9-5 09:36:51-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 09:36:51-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 09:36:51-debug: asset-db:refresh-all-database (133ms)
2025-9-5 09:36:51-debug: asset-db:worker-effect-data-processing (-1ms)
2025-9-5 09:36:51-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 09:38:47-debug: refresh db internal success
2025-9-5 09:38:47-debug: %cImport%c: E:\M2Game\Client\assets\editor\emitter\EmitterEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 09:38:47-debug: refresh db assets success
2025-9-5 09:38:47-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 09:38:47-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 09:38:47-debug: asset-db:refresh-all-database (129ms)
2025-9-5 09:38:47-debug: asset-db:worker-effect-data-processing (-1ms)
2025-9-5 09:39:38-debug: refresh db internal success
2025-9-5 09:39:38-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\BulletController.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 09:39:38-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 09:39:38-debug: refresh db assets success
2025-9-5 09:39:38-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 09:39:38-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 09:39:38-debug: asset-db:refresh-all-database (157ms)
2025-9-5 09:39:38-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 09:44:04-debug: refresh db internal success
2025-9-5 09:44:04-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 09:44:04-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 09:44:04-debug: refresh db assets success
2025-9-5 09:44:04-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 09:44:04-debug: asset-db:refresh-all-database (147ms)
2025-9-5 09:44:04-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 09:44:04-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 09:44:06-debug: refresh db internal success
2025-9-5 09:44:06-debug: refresh db assets success
2025-9-5 09:44:06-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 09:44:06-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 09:44:06-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 09:44:06-debug: asset-db:refresh-all-database (114ms)
2025-9-5 09:44:06-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 09:44:46-debug: refresh db internal success
2025-9-5 09:44:46-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 09:44:46-debug: refresh db assets success
2025-9-5 09:44:46-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 09:44:46-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 09:44:46-debug: asset-db:refresh-all-database (129ms)
2025-9-5 09:44:46-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 09:44:46-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 09:44:55-debug: refresh db internal success
2025-9-5 09:44:55-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 09:44:55-debug: refresh db assets success
2025-9-5 09:44:55-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 09:44:55-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 09:44:55-debug: asset-db:refresh-all-database (102ms)
2025-9-5 09:50:32-debug: refresh db internal success
2025-9-5 09:50:32-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 09:50:32-debug: refresh db assets success
2025-9-5 09:50:32-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 09:50:32-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 09:50:32-debug: asset-db:refresh-all-database (136ms)
2025-9-5 09:50:51-debug: refresh db internal success
2025-9-5 09:50:51-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 09:50:51-debug: refresh db assets success
2025-9-5 09:50:51-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 09:50:51-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 09:50:51-debug: asset-db:refresh-all-database (123ms)
2025-9-5 09:50:51-debug: asset-db:worker-effect-data-processing (-1ms)
2025-9-5 09:51:17-debug: refresh db internal success
2025-9-5 09:51:17-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 09:51:17-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 09:51:17-debug: refresh db assets success
2025-9-5 09:51:17-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 09:51:17-debug: asset-db:refresh-all-database (100ms)
2025-9-5 09:51:17-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 09:51:17-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 09:51:32-debug: refresh db internal success
2025-9-5 09:51:32-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 09:51:32-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 09:51:32-debug: refresh db assets success
2025-9-5 09:51:32-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 09:51:32-debug: asset-db:refresh-all-database (125ms)
2025-9-5 09:51:32-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 09:51:32-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 09:51:40-debug: refresh db internal success
2025-9-5 09:51:40-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 09:51:40-debug: refresh db assets success
2025-9-5 09:51:40-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 09:51:40-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 09:51:40-debug: asset-db:refresh-all-database (125ms)
2025-9-5 09:51:40-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 09:51:45-debug: refresh db internal success
2025-9-5 09:51:45-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 09:51:45-debug: refresh db assets success
2025-9-5 09:51:45-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 09:51:45-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 09:51:45-debug: asset-db:refresh-all-database (99ms)
2025-9-5 09:51:45-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 09:51:45-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 09:51:51-debug: refresh db internal success
2025-9-5 09:51:52-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 09:51:52-debug: refresh db assets success
2025-9-5 09:51:52-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 09:51:52-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 09:51:52-debug: asset-db:refresh-all-database (105ms)
2025-9-5 09:55:33-debug: refresh db internal success
2025-9-5 09:55:33-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 09:55:33-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 09:55:33-debug: refresh db assets success
2025-9-5 09:55:33-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 09:55:33-debug: asset-db:refresh-all-database (135ms)
2025-9-5 09:55:33-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 09:55:33-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 09:58:37-debug: refresh db internal success
2025-9-5 09:58:37-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 09:58:37-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 09:58:37-debug: refresh db assets success
2025-9-5 09:58:37-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 09:58:37-debug: asset-db:refresh-all-database (127ms)
2025-9-5 09:58:37-debug: asset-db:worker-effect-data-processing (-1ms)
2025-9-5 09:58:55-debug: refresh db internal success
2025-9-5 09:58:55-debug: refresh db assets success
2025-9-5 09:58:55-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 09:58:55-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 09:58:55-debug: asset-db:refresh-all-database (97ms)
2025-9-5 09:58:55-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 09:58:55-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 09:58:59-debug: refresh db internal success
2025-9-5 09:58:59-debug: refresh db assets success
2025-9-5 09:58:59-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 09:58:59-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 09:58:59-debug: asset-db:refresh-all-database (94ms)
2025-9-5 09:58:59-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 09:58:59-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 09:59:01-debug: %cImport%c: E:\M2Game\Client\assets\scenes\BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-5 09:59:01-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (4ms)
2025-9-5 09:59:02-debug: refresh db internal success
2025-9-5 09:59:02-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 09:59:02-debug: refresh db assets success
2025-9-5 09:59:02-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 09:59:02-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 09:59:02-debug: asset-db:refresh-all-database (100ms)
2025-9-5 09:59:02-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 09:59:18-debug: refresh db internal success
2025-9-5 09:59:18-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 09:59:18-debug: refresh db assets success
2025-9-5 09:59:18-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 09:59:18-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 09:59:18-debug: asset-db:refresh-all-database (104ms)
2025-9-5 09:59:18-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 09:59:18-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 10:14:18-debug: refresh db internal success
2025-9-5 10:14:18-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 10:14:18-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 10:14:18-debug: refresh db assets success
2025-9-5 10:14:18-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 10:14:18-debug: asset-db:refresh-all-database (126ms)
2025-9-5 10:14:18-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 10:14:18-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 10:30:31-debug: refresh db internal success
2025-9-5 10:30:31-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 10:30:31-debug: refresh db assets success
2025-9-5 10:30:31-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 10:30:31-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 10:30:31-debug: asset-db:refresh-all-database (132ms)
2025-9-5 10:30:31-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 10:31:40-debug: refresh db internal success
2025-9-5 10:31:40-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 10:31:40-debug: refresh db assets success
2025-9-5 10:31:40-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 10:31:40-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 10:31:40-debug: asset-db:refresh-all-database (136ms)
2025-9-5 10:31:40-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-5 10:31:40-debug: asset-db-hook-engine-extends-afterRefresh (3ms)
2025-9-5 10:31:55-debug: refresh db internal success
2025-9-5 10:31:55-debug: refresh db assets success
2025-9-5 10:31:55-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 10:31:55-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 10:31:55-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 10:31:55-debug: asset-db:refresh-all-database (97ms)
2025-9-5 10:31:55-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 10:31:58-debug: refresh db internal success
2025-9-5 10:31:58-debug: refresh db assets success
2025-9-5 10:31:58-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 10:31:58-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 10:31:58-debug: asset-db:refresh-all-database (97ms)
2025-9-5 10:31:58-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 10:31:58-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-5 10:31:59-debug: refresh db internal success
2025-9-5 10:31:59-debug: refresh db assets success
2025-9-5 10:31:59-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 10:31:59-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 10:31:59-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 10:31:59-debug: asset-db:refresh-all-database (95ms)
2025-9-5 10:31:59-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-5 10:34:55-debug: refresh db internal success
2025-9-5 10:34:55-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 10:34:55-debug: refresh db assets success
2025-9-5 10:34:55-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 10:34:55-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 10:34:55-debug: asset-db:refresh-all-database (143ms)
2025-9-5 10:35:17-debug: start refresh asset from E:\M2Game\Client\assets\resources\Game\prefabs\emitter\EmitterExample.prefab...
2025-9-5 10:35:17-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\prefabs\emitter\EmitterExample.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-5 10:35:17-debug: refresh asset E:\M2Game\Client\assets\resources\Game\prefabs\emitter success
2025-9-5 10:35:17-debug: refresh db internal success
2025-9-5 10:35:17-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 10:35:17-debug: refresh db assets success
2025-9-5 10:35:17-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 10:35:17-debug: asset-db:refresh-all-database (104ms)
2025-9-5 10:35:33-debug: start refresh asset from E:\M2Game\Client\assets\resources\Game\prefabs\emitter\EmitterExample.prefab...
2025-9-5 10:35:33-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\prefabs\emitter\EmitterExample.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-5 10:35:33-debug: refresh asset E:\M2Game\Client\assets\resources\Game\prefabs\emitter success
2025-9-5 10:35:33-debug: refresh db internal success
2025-9-5 10:35:33-debug: refresh db assets success
2025-9-5 10:35:33-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 10:35:33-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 10:35:33-debug: asset-db:refresh-all-database (102ms)
2025-9-5 10:36:28-debug: Query all assets info in project
2025-9-5 10:36:28-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-5 10:36:28-debug: Skip compress image, progress: 0%
2025-9-5 10:36:28-debug: Num of bundles: 3..., progress: 0%
2025-9-5 10:36:28-debug: Init all bundles start..., progress: 0%
2025-9-5 10:36:28-debug: // ---- build task 查询 Asset Bundle ----
2025-9-5 10:36:28-debug: 查询 Asset Bundle start, progress: 0%
2025-9-5 10:36:28-debug: Init bundle root assets start..., progress: 0%
2025-9-5 10:36:28-debug:   Number of all scripts: 237
2025-9-5 10:36:28-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-5 10:36:28-debug: Init bundle root assets success..., progress: 0%
2025-9-5 10:36:28-debug:   Number of all scenes: 8
2025-9-5 10:36:28-debug:   Number of other assets: 1910
2025-9-5 10:36:28-debug: // ---- build task 查询 Asset Bundle ---- (18ms)
2025-9-5 10:36:28-debug: 查询 Asset Bundle start, progress: 5%
2025-9-5 10:36:28-log: run build task 查询 Asset Bundle success in 18 ms√, progress: 5%
2025-9-5 10:36:28-debug: [Build Memory track]: 查询 Asset Bundle start:219.47MB, end 221.78MB, increase: 2.31MB
2025-9-5 10:36:28-debug: // ---- build task 查询 Asset Bundle ----
2025-9-5 10:36:28-debug: // ---- build task 查询 Asset Bundle ---- (1ms)
2025-9-5 10:36:28-debug: [Build Memory track]: 查询 Asset Bundle start:221.81MB, end 220.48MB, increase: -1362.91KB
2025-9-5 10:36:28-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-5 10:36:28-log: run build task 查询 Asset Bundle success in 1 ms√, progress: 10%
2025-9-5 10:36:28-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-5 10:36:28-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-9-5 10:36:28-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-5 10:36:28-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:220.51MB, end 220.53MB, increase: 16.29KB
2025-9-5 10:36:28-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-5 10:36:28-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-9-5 10:36:28-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-9-5 10:36:28-debug: [Build Memory track]: 填充脚本数据到 settings.json start:220.55MB, end 220.58MB, increase: 25.89KB
2025-9-5 10:36:28-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-5 10:36:28-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-5 10:36:28-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-9-5 10:36:28-log: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 15%
2025-9-5 10:36:28-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:220.61MB, end 220.91MB, increase: 308.75KB
2025-9-5 10:37:18-debug: refresh db internal success
2025-9-5 10:37:18-debug: refresh db assets success
2025-9-5 10:37:18-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 10:37:18-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 10:37:18-debug: asset-db:refresh-all-database (123ms)
2025-9-5 10:37:18-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 10:37:18-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 10:37:21-debug: refresh db internal success
2025-9-5 10:37:21-debug: refresh db assets success
2025-9-5 10:37:21-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 10:37:21-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 10:37:21-debug: asset-db:refresh-all-database (149ms)
2025-9-5 10:40:50-debug: refresh db internal success
2025-9-5 10:40:50-debug: %cImport%c: E:\M2Game\Client\assets\editor\emitter\EmitterEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 10:40:50-debug: refresh db assets success
2025-9-5 10:40:50-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 10:40:50-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 10:40:50-debug: asset-db:refresh-all-database (137ms)
2025-9-5 10:40:50-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 10:40:50-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-5 10:42:11-debug: refresh db internal success
2025-9-5 10:42:11-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\BulletSystem.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 10:42:11-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 10:42:11-debug: refresh db assets success
2025-9-5 10:42:11-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 10:42:11-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 10:42:11-debug: asset-db:refresh-all-database (128ms)
2025-9-5 10:42:11-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 10:42:11-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 10:42:41-debug: refresh db internal success
2025-9-5 10:42:41-debug: %cImport%c: E:\M2Game\Client\assets\editor\emitter\EmitterEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 10:42:41-debug: refresh db assets success
2025-9-5 10:42:41-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 10:42:41-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 10:42:41-debug: asset-db:refresh-all-database (127ms)
2025-9-5 10:42:41-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 10:42:41-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 10:43:15-debug: refresh db internal success
2025-9-5 10:43:15-debug: %cImport%c: E:\M2Game\Client\assets\editor\emitter\EmitterEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 10:43:15-debug: refresh db assets success
2025-9-5 10:43:15-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 10:43:15-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 10:43:15-debug: asset-db:refresh-all-database (126ms)
2025-9-5 10:43:47-debug: refresh db internal success
2025-9-5 10:43:47-debug: %cImport%c: E:\M2Game\Client\assets\editor\emitter\EmitterEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 10:43:47-debug: refresh db assets success
2025-9-5 10:43:47-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 10:43:47-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 10:43:47-debug: asset-db:refresh-all-database (132ms)
2025-9-5 10:44:20-debug: refresh db internal success
2025-9-5 10:44:20-debug: %cImport%c: E:\M2Game\Client\assets\editor\emitter\EmitterEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 10:44:20-debug: refresh db assets success
2025-9-5 10:44:20-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 10:44:20-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 10:44:20-debug: asset-db:refresh-all-database (154ms)
2025-9-5 10:44:20-debug: asset-db:worker-effect-data-processing (13ms)
2025-9-5 10:44:20-debug: asset-db-hook-engine-extends-afterRefresh (14ms)
2025-9-5 10:45:05-debug: refresh db internal success
2025-9-5 10:45:05-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\move\Movable.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 10:45:05-debug: refresh db assets success
2025-9-5 10:45:05-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 10:45:05-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 10:45:05-debug: asset-db:refresh-all-database (128ms)
2025-9-5 10:45:05-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 10:45:05-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 10:46:09-debug: refresh db internal success
2025-9-5 10:46:09-debug: %cImport%c: E:\M2Game\Client\assets\editor\emitter\EmitterEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 10:46:09-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 10:46:09-debug: refresh db assets success
2025-9-5 10:46:09-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 10:46:09-debug: asset-db:refresh-all-database (128ms)
2025-9-5 10:46:09-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 10:46:09-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 10:46:35-debug: refresh db internal success
2025-9-5 10:46:35-debug: %cImport%c: E:\M2Game\Client\assets\editor\emitter\EmitterEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 10:46:35-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\move\Movable.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 10:46:35-debug: refresh db assets success
2025-9-5 10:46:35-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 10:46:35-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 10:46:35-debug: asset-db:refresh-all-database (138ms)
2025-9-5 10:46:35-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-5 10:46:35-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-5 10:53:22-debug: refresh db internal success
2025-9-5 10:53:22-debug: %cImport%c: E:\M2Game\Client\assets\editor\emitter\EmitterEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 10:53:22-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 10:53:22-debug: refresh db assets success
2025-9-5 10:53:22-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 10:53:22-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 10:53:22-debug: asset-db:refresh-all-database (136ms)
2025-9-5 10:56:12-debug: refresh db internal success
2025-9-5 10:56:12-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 10:56:12-debug: refresh db assets success
2025-9-5 10:56:12-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 10:56:12-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 10:56:12-debug: asset-db:refresh-all-database (130ms)
2025-9-5 10:56:12-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 10:56:12-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 10:57:52-debug: refresh db internal success
2025-9-5 10:57:52-debug: refresh db assets success
2025-9-5 10:57:52-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 10:57:52-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 10:57:52-debug: asset-db:refresh-all-database (107ms)
2025-9-5 10:57:52-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 10:57:52-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 11:02:23-debug: refresh db internal success
2025-9-5 11:02:23-debug: %cImport%c: E:\M2Game\Client\assets\editor\emitter\EmitterEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:02:23-debug: refresh db assets success
2025-9-5 11:02:23-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 11:02:23-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 11:02:23-debug: asset-db:refresh-all-database (133ms)
2025-9-5 11:05:06-debug: refresh db internal success
2025-9-5 11:05:06-debug: %cImport%c: E:\M2Game\Client\assets\editor\emitter\EmitterEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:05:06-debug: refresh db assets success
2025-9-5 11:05:06-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 11:05:06-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 11:05:06-debug: asset-db:refresh-all-database (139ms)
2025-9-5 11:05:06-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 11:05:06-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 11:07:45-debug: refresh db internal success
2025-9-5 11:07:45-debug: refresh db assets success
2025-9-5 11:07:45-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 11:07:45-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 11:07:45-debug: asset-db:refresh-all-database (101ms)
2025-9-5 11:07:45-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 11:07:50-debug: refresh db internal success
2025-9-5 11:07:50-debug: refresh db assets success
2025-9-5 11:07:50-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 11:07:50-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 11:07:50-debug: asset-db:refresh-all-database (95ms)
2025-9-5 11:07:53-debug: refresh db internal success
2025-9-5 11:07:53-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 11:07:53-debug: refresh db assets success
2025-9-5 11:07:53-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 11:07:53-debug: asset-db:refresh-all-database (113ms)
2025-9-5 11:07:53-debug: asset-db:worker-effect-data-processing (5ms)
2025-9-5 11:07:53-debug: asset-db-hook-engine-extends-afterRefresh (6ms)
2025-9-5 11:07:58-debug: refresh db internal success
2025-9-5 11:07:58-debug: refresh db assets success
2025-9-5 11:07:58-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 11:07:58-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 11:07:58-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 11:07:58-debug: asset-db:refresh-all-database (93ms)
2025-9-5 11:07:58-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 11:08:10-debug: refresh db internal success
2025-9-5 11:08:10-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 11:08:10-debug: refresh db assets success
2025-9-5 11:08:10-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 11:08:10-debug: asset-db:refresh-all-database (96ms)
2025-9-5 11:08:10-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 11:08:10-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 11:08:11-debug: refresh db internal success
2025-9-5 11:08:11-debug: refresh db assets success
2025-9-5 11:08:11-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 11:08:11-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 11:08:11-debug: asset-db:refresh-all-database (94ms)
2025-9-5 11:08:11-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 11:09:37-debug: refresh db internal success
2025-9-5 11:09:37-debug: refresh db assets success
2025-9-5 11:09:37-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 11:09:37-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 11:09:37-debug: asset-db:refresh-all-database (100ms)
2025-9-5 11:09:37-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 11:09:37-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 11:11:48-debug: refresh db internal success
2025-9-5 11:11:48-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\ui\common
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\event
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\Data\bag
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\Data
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\Data\equip
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\Data\fight
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\Data\mail
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\Data\pk
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\ui\common
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\Data\gm
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\ui\common\components
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\ui\main\plane
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\ui\common\components\dropdown
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\ui\common\components\button
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\ui\common\components\list
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\ui\common\components\SelectList
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\ui\main\plane\components\back_pack
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\ui\main\plane\components\display
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\ui\main\plane\components
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\Data\BaseInfo.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\Data\DataEvent.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\Data\GameLevel.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\Data\DataManager.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\event\EventManager.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\Data\bag\Bag.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\Data\equip\Equip.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\Data\fight\Rogue.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\Data\equip\EquipSlots.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\Data\equip\EquipCombine.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\Data\gm\GM.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\Data\mail\Mail.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\ui\common\TopBlockInputUI.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\Data\pk\PK.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\ui\gm\GmButtonUI.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\ui\gm\GmUI.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\ui\main\plane\PlaneCombineResultUI.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\ui\main\plane\PlaneEvent.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\ui\main\plane\PlaneEquipInfoUI.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\ui\main\plane\PlaneTypes.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\ui\main\plane\PlaneUI.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\ui\common\components\button\ButtonPlus.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\ui\common\components\button\DragButton.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\ui\common\components\dropdown\DropDown.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\ui\common\components\list\List.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\ui\common\components\list\ListItem.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\ui\common\components\SelectList\uiSelect.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\ui\common\components\SelectList\uiSelectItem.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\ui\main\plane\components\back_pack\BagItem.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\ui\main\plane\components\back_pack\SortTypeDropdown.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\ui\main\plane\components\back_pack\BagGrid.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\ui\main\plane\components\back_pack\Tabs.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\ui\main\plane\components\display\EquipDisplay.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\ui\main\plane\components\display\CombineDisplay.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\ui\common\TopBlockInputUI.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\Bundle.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\main
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\gm
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\main_shop
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\main_plane
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\main_skyisland
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\main_talent
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\audio
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\texture
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\gm\prefab
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\prefab
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\gm\script
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\main\prefab
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\gm\texture
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\main\script
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\main\audio
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\main\texture
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\main_plane\prefab
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\main_plane\script
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\main_plane\audio
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\main_plane\texture
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\main_shop\audio
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\main_shop\script
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\main_shop\prefab
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\main_shop\texture
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\main_skyisland\audio
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\main_skyisland\prefab
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\main_skyisland\texture
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\main_talent\audio
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\main_skyisland\script
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\main_talent\prefab
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\main_talent\script
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\data
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\main_talent\texture
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\prefab\ui
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\event
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\gm\prefab\ui
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\gm\script\data
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\main\prefab\ui
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\gm\script\ui
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\main\script\ui
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\main_plane\script\ui
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\main_plane\script\module
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\main_shop\prefab\ui
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\main_plane\prefab\ui
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\main_shop\script\ui
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\main_shop\script\data
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\main_skyisland\prefab\ui
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\main_skyisland\script\data
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\main_skyisland\script\ui
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\main_talent\prefab\ui
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\main\ToastUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\main_talent\script\data
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\data\bag
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\main_talent\script\ui
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\data\base
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\data\equip
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\data\DataManager.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\data\fight
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\data\gm
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\data\mail
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\data\pk
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\data\game_level
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\event\EventManager.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\event\DataEvent.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\CommonEntiry.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\components
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\TopBlockInputUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\gm\script\ui\GmButtonUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\gm\script\ui\GmUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\main_plane\script\module\PlaneEvent.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\main_plane\script\module\PlaneTypes.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\main_plane\script\ui\components
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\main_plane\script\ui\PlaneCombineResultUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\main_plane\script\ui\PlaneEquipInfoUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\main_plane\script\ui\PlaneUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\data\bag\Bag.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\data\equip\EquipCombine.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\data\equip\Equip.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\data\base\BaseInfo.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\data\equip\EquipSlots.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\data\fight\Rogue.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\data\game_level\GameLevel.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\data\mail\Mail.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\data\gm\GM.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\data\pk\PK.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\components\list
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\components\dropdown
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\main_plane\script\ui\components\back_pack
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\components\button
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\components\SelectList
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\components\button\ButtonPlus.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\main_plane\script\ui\components\display
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\components\button\DragButton.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\components\dropdown\DropDown.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\components\list\List.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\components\list\ListItem.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\components\SelectList\uiSelectItem.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\components\SelectList\uiSelect.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\main_plane\script\ui\components\back_pack\BagItem.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\main_plane\script\ui\components\back_pack\BagGrid.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\main_plane\script\ui\components\back_pack\SortTypeDropdown.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\main_plane\script\ui\components\back_pack\Tabs.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\main_plane\script\ui\components\display\CombineDisplay.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\main_plane\script\ui\components\display\EquipDisplay.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\animation\ToastUIClose.anim
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\main\ToastUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\prefab\ui\TopBlockInputUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\scripts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\resources\Luban
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\editor\emitter\EmitterEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\animation
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\resources\Luban\tbbuffer.json
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\gm
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\main
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\DevLoginUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\LoadingUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\manager
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\DevLoginUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\gm
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\game
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\res
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\main
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\UIMgr.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\main\dialogue
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\gm\GmUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\main\friend
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\main\BottomUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\main\mail
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\main\pk
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\main\MapModeUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\main\PopupUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\main\TopUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\manager\WaveManager.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\map
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\plane
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\game\MBoomUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\main\BattleUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\main\BuidingUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\main\BuildingInfoUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\main\BottomUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\main\fight
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\main\friend
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\main\dialogue
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\main\MapModeUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\main\mail
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\main\pk
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\main\TalentUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\res\PlaneRes.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\main\WheelSpinnerUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\main\TopUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\main\dialogue\DialogueUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\main\friend\FriendCellUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\main\friend\FriendUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\main\mail\MailUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\main\pk\PKHistoryUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\main\pk\PKUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\map\LevelCondition.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\map\LevelEventUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\boss
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\skill
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\Plane.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\main\fight\RogueUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\main\friend\FriendAddUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\main\dialogue\DialogueUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\main\fight\RogueSelectIcon.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\main\friend\FriendCellUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\main\friend\FriendUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\main\friend\FriendListUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\main\friend\FriendStrangerUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\main\pk\PKHistoryCellUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\main\mail\MailUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\main\pk\PKHistoryUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\main\pk\PKUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\boss\BossEntity.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\boss\BossBase.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\skill\BuffComp.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:48-debug: refresh db assets success
2025-9-5 11:11:48-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 11:11:48-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 11:11:48-debug: asset-db:refresh-all-database (314ms)
2025-9-5 11:11:48-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 11:11:48-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 11:11:49-debug: %cImport%c: E:\M2Game\Client\assets\scenes\BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:11:49-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (2ms)
2025-9-5 11:16:45-debug: refresh db internal success
2025-9-5 11:16:45-debug: %cImport%c: E:\M2Game\Client\assets\editor\gizmos\EmitterArcGizmo.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:16:45-debug: %cImport%c: E:\M2Game\Client\assets\editor\gizmos\GizmoUtils.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:16:45-debug: %cImport%c: E:\M2Game\Client\assets\editor\gizmos\GizmoDrawer.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:16:45-debug: %cImport%c: E:\M2Game\Client\assets\editor\gizmos\GizmoManager.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:16:45-debug: %cImport%c: E:\M2Game\Client\assets\editor\gizmos
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:16:45-debug: %cImport%c: E:\M2Game\Client\assets\editor
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:16:45-debug: refresh db assets success
2025-9-5 11:16:45-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 11:16:45-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 11:16:45-debug: asset-db:refresh-all-database (157ms)
2025-9-5 11:16:45-debug: asset-db:worker-effect-data-processing (3ms)
2025-9-5 11:16:45-debug: asset-db-hook-engine-extends-afterRefresh (3ms)
2025-9-5 11:17:00-debug: refresh db internal success
2025-9-5 11:17:00-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 11:17:00-debug: refresh db assets success
2025-9-5 11:17:00-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 11:17:00-debug: asset-db:refresh-all-database (144ms)
2025-9-5 11:17:00-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 11:17:00-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-5 11:20:07-debug: refresh db internal success
2025-9-5 11:20:07-debug: %cImport%c: E:\M2Game\Client\assets\editor\gizmos\EmitterArcGizmo.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:20:07-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 11:20:07-debug: refresh db assets success
2025-9-5 11:20:07-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 11:20:07-debug: asset-db:refresh-all-database (106ms)
2025-9-5 11:20:07-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 11:20:07-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 11:20:34-debug: start move asset from E:\M2Game\Client\assets\editor\gizmos\EmitterArcGizmo.ts -> E:\M2Game\Client\assets\editor\gizmos\EmitterGizmo.ts...
2025-9-5 11:20:34-debug: %cImport%c: E:\M2Game\Client\assets\editor\gizmos\EmitterGizmo.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:20:34-debug: start refresh asset from E:\M2Game\Client\assets\editor\gizmos\EmitterGizmo.ts...
2025-9-5 11:20:34-debug: refresh asset E:\M2Game\Client\assets\editor\gizmos success
2025-9-5 11:20:34-debug: start refresh asset from E:\M2Game\Client\assets\editor\gizmos...
2025-9-5 11:20:34-debug: %cImport%c: E:\M2Game\Client\assets\editor\gizmos
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:20:34-debug: refresh asset E:\M2Game\Client\assets\editor success
2025-9-5 11:20:34-debug: move asset from E:\M2Game\Client\assets\editor\gizmos\EmitterArcGizmo.ts -> E:\M2Game\Client\assets\editor\gizmos\EmitterGizmo.ts success
2025-9-5 11:20:55-debug: refresh db internal success
2025-9-5 11:20:55-debug: %cImport%c: E:\M2Game\Client\assets\editor\gizmos\EmitterGizmo.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:20:55-debug: refresh db assets success
2025-9-5 11:20:55-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 11:20:55-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 11:20:55-debug: asset-db:refresh-all-database (315ms)
2025-9-5 11:20:55-debug: asset-db:worker-effect-data-processing (170ms)
2025-9-5 11:20:55-debug: asset-db-hook-engine-extends-afterRefresh (171ms)
2025-9-5 11:21:07-debug: refresh db internal success
2025-9-5 11:21:07-debug: refresh db assets success
2025-9-5 11:21:07-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 11:21:07-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 11:21:07-debug: asset-db:refresh-all-database (103ms)
2025-9-5 11:21:16-debug: refresh db internal success
2025-9-5 11:21:16-debug: refresh db assets success
2025-9-5 11:21:16-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 11:21:16-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 11:21:16-debug: asset-db:refresh-all-database (100ms)
2025-9-5 11:21:16-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 11:21:52-debug: refresh db internal success
2025-9-5 11:21:52-debug: %cImport%c: E:\M2Game\Client\assets\editor\gizmos\GizmoManager.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:21:52-debug: refresh db assets success
2025-9-5 11:21:52-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 11:21:52-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 11:21:52-debug: asset-db:refresh-all-database (136ms)
2025-9-5 11:21:52-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 11:21:52-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 11:21:54-debug: refresh db internal success
2025-9-5 11:21:54-debug: refresh db assets success
2025-9-5 11:21:54-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 11:21:54-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 11:21:54-debug: asset-db:refresh-all-database (121ms)
2025-9-5 11:22:10-debug: refresh db internal success
2025-9-5 11:22:10-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 11:22:10-debug: refresh db assets success
2025-9-5 11:22:10-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 11:22:10-debug: asset-db:refresh-all-database (101ms)
2025-9-5 11:22:10-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 11:22:10-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 11:23:16-debug: refresh db internal success
2025-9-5 11:23:16-debug: %cImport%c: E:\M2Game\Client\assets\editor\gizmos\GizmoManager.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:23:16-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 11:23:16-debug: refresh db assets success
2025-9-5 11:23:16-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 11:23:16-debug: asset-db:refresh-all-database (184ms)
2025-9-5 11:23:16-debug: asset-db:worker-effect-data-processing (4ms)
2025-9-5 11:23:16-debug: asset-db-hook-engine-extends-afterRefresh (4ms)
2025-9-5 11:23:28-debug: refresh db internal success
2025-9-5 11:23:28-debug: %cImport%c: E:\M2Game\Client\assets\editor\gizmos\GizmoManager.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:23:28-debug: refresh db assets success
2025-9-5 11:23:28-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 11:23:28-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 11:23:28-debug: asset-db:refresh-all-database (130ms)
2025-9-5 11:23:28-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 11:23:28-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 11:23:39-debug: %cImport%c: E:\M2Game\Client\assets\scenes\BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:23:39-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (2ms)
2025-9-5 11:23:41-debug: %cImport%c: E:\M2Game\Client\assets\scenes\BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:23:41-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (3ms)
2025-9-5 11:24:38-debug: %cImport%c: E:\M2Game\Client\assets\scenes\BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:24:38-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (2ms)
2025-9-5 11:26:09-debug: refresh db internal success
2025-9-5 11:26:10-debug: refresh db assets success
2025-9-5 11:26:10-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 11:26:10-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 11:26:10-debug: asset-db:refresh-all-database (116ms)
2025-9-5 11:26:11-debug: refresh db internal success
2025-9-5 11:26:11-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 11:26:11-debug: refresh db assets success
2025-9-5 11:26:11-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 11:26:11-debug: asset-db:refresh-all-database (123ms)
2025-9-5 11:26:11-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 11:26:11-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 11:26:12-debug: refresh db internal success
2025-9-5 11:26:12-debug: refresh db assets success
2025-9-5 11:26:12-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 11:26:12-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 11:26:12-debug: asset-db:refresh-all-database (119ms)
2025-9-5 11:26:12-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 11:26:15-debug: refresh db internal success
2025-9-5 11:26:15-debug: refresh db assets success
2025-9-5 11:26:15-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 11:26:15-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 11:26:15-debug: asset-db:refresh-all-database (93ms)
2025-9-5 11:26:41-debug: %cImport%c: E:\M2Game\Client\assets\scenes\BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:26:41-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (2ms)
2025-9-5 11:27:06-debug: refresh db internal success
2025-9-5 11:27:06-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 11:27:06-debug: refresh db assets success
2025-9-5 11:27:06-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 11:27:06-debug: asset-db:refresh-all-database (119ms)
2025-9-5 11:27:06-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 11:27:06-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 11:27:34-debug: refresh db internal success
2025-9-5 11:27:34-debug: %cImport%c: E:\M2Game\Client\assets\editor\gizmos\GizmoManager.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:27:34-debug: refresh db assets success
2025-9-5 11:27:34-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 11:27:34-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 11:27:34-debug: asset-db:refresh-all-database (134ms)
2025-9-5 11:27:34-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 11:27:34-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 11:28:00-debug: Query all assets info in project
2025-9-5 11:28:00-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-5 11:28:00-debug: Skip compress image, progress: 0%
2025-9-5 11:28:00-debug: Init all bundles start..., progress: 0%
2025-9-5 11:28:00-debug: 查询 Asset Bundle start, progress: 0%
2025-9-5 11:28:00-debug: Num of bundles: 3..., progress: 0%
2025-9-5 11:28:00-debug: // ---- build task 查询 Asset Bundle ----
2025-9-5 11:28:00-debug: Init bundle root assets start..., progress: 0%
2025-9-5 11:28:00-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-5 11:28:00-debug:   Number of other assets: 1912
2025-9-5 11:28:00-debug: Init bundle root assets success..., progress: 0%
2025-9-5 11:28:00-debug:   Number of all scenes: 8
2025-9-5 11:28:00-debug:   Number of all scripts: 244
2025-9-5 11:28:00-debug: // ---- build task 查询 Asset Bundle ---- (20ms)
2025-9-5 11:28:00-log: run build task 查询 Asset Bundle success in 20 ms√, progress: 5%
2025-9-5 11:28:00-debug: [Build Memory track]: 查询 Asset Bundle start:218.99MB, end 220.55MB, increase: 1.56MB
2025-9-5 11:28:00-debug: 查询 Asset Bundle start, progress: 5%
2025-9-5 11:28:00-debug: // ---- build task 查询 Asset Bundle ----
2025-9-5 11:28:00-debug: // ---- build task 查询 Asset Bundle ---- (2ms)
2025-9-5 11:28:00-log: run build task 查询 Asset Bundle success in 2 ms√, progress: 10%
2025-9-5 11:28:00-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-5 11:28:00-debug: [Build Memory track]: 查询 Asset Bundle start:220.58MB, end 219.96MB, increase: -633.96KB
2025-9-5 11:28:00-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-5 11:28:00-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-9-5 11:28:00-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-5 11:28:00-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-9-5 11:28:00-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:219.99MB, end 220.03MB, increase: 36.73KB
2025-9-5 11:28:00-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-5 11:28:00-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-9-5 11:28:00-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-9-5 11:28:00-debug: [Build Memory track]: 填充脚本数据到 settings.json start:220.06MB, end 220.08MB, increase: 25.90KB
2025-9-5 11:28:00-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-5 11:28:00-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-5 11:28:00-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-9-5 11:28:00-log: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 15%
2025-9-5 11:28:00-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:220.11MB, end 220.42MB, increase: 317.83KB
2025-9-5 11:29:14-debug: refresh db internal success
2025-9-5 11:29:14-debug: %cImport%c: E:\M2Game\Client\assets\editor\gizmos\EmitterGizmo.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:29:14-debug: %cImport%c: E:\M2Game\Client\assets\editor\gizmos\GizmoManager.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:29:14-debug: refresh db assets success
2025-9-5 11:29:14-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 11:29:14-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 11:29:14-debug: asset-db:refresh-all-database (140ms)
2025-9-5 11:29:14-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 11:30:08-debug: refresh db internal success
2025-9-5 11:30:08-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 11:30:08-debug: refresh db assets success
2025-9-5 11:30:08-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 11:30:08-debug: asset-db:refresh-all-database (146ms)
2025-9-5 11:31:45-debug: refresh db internal success
2025-9-5 11:31:46-debug: refresh db assets success
2025-9-5 11:31:46-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 11:31:46-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 11:31:46-debug: asset-db:refresh-all-database (136ms)
2025-9-5 11:31:46-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 11:31:46-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 11:32:40-debug: refresh db internal success
2025-9-5 11:32:40-debug: %cImport%c: E:\M2Game\Client\assets\editor\gizmos\EmitterGizmo.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:32:40-debug: refresh db assets success
2025-9-5 11:32:40-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 11:32:40-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 11:32:40-debug: asset-db:refresh-all-database (107ms)
2025-9-5 11:34:00-debug: refresh db internal success
2025-9-5 11:34:00-debug: %cImport%c: E:\M2Game\Client\assets\editor\gizmos\EmitterGizmo.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:34:00-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 11:34:00-debug: refresh db assets success
2025-9-5 11:34:00-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 11:34:00-debug: asset-db:refresh-all-database (108ms)
2025-9-5 11:34:46-debug: refresh db internal success
2025-9-5 11:34:46-debug: %cImport%c: E:\M2Game\Client\assets\editor\gizmos\EmitterGizmo.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:34:46-debug: %cImport%c: E:\M2Game\Client\assets\editor\gizmos\GizmoUtils.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:34:46-debug: refresh db assets success
2025-9-5 11:34:46-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 11:34:46-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 11:34:46-debug: asset-db:refresh-all-database (135ms)
2025-9-5 11:34:46-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 11:36:45-debug: refresh db internal success
2025-9-5 11:36:45-debug: %cImport%c: E:\M2Game\Client\assets\editor\gizmos\EmitterGizmo.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:36:45-debug: refresh db assets success
2025-9-5 11:36:45-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 11:36:45-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 11:36:45-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 11:36:45-debug: asset-db:refresh-all-database (104ms)
2025-9-5 11:36:45-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 11:36:52-debug: refresh db internal success
2025-9-5 11:36:52-debug: refresh db assets success
2025-9-5 11:36:52-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 11:36:52-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 11:36:52-debug: asset-db:refresh-all-database (100ms)
2025-9-5 11:36:54-debug: refresh db internal success
2025-9-5 11:36:54-debug: refresh db assets success
2025-9-5 11:36:54-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 11:36:54-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 11:36:54-debug: asset-db:refresh-all-database (104ms)
2025-9-5 11:38:35-debug: refresh db internal success
2025-9-5 11:38:35-debug: %cImport%c: E:\M2Game\Client\assets\editor\emitter\EmitterEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:38:35-debug: refresh db assets success
2025-9-5 11:38:35-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 11:38:35-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 11:38:35-debug: asset-db:refresh-all-database (141ms)
2025-9-5 11:38:35-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 11:38:35-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 11:39:47-debug: refresh db internal success
2025-9-5 11:39:47-debug: refresh db assets success
2025-9-5 11:39:47-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 11:39:47-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 11:39:47-debug: asset-db:refresh-all-database (124ms)
2025-9-5 11:40:14-debug: refresh db internal success
2025-9-5 11:40:14-debug: refresh db assets success
2025-9-5 11:40:14-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 11:40:14-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 11:40:14-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 11:40:14-debug: asset-db:refresh-all-database (124ms)
2025-9-5 11:40:14-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 11:40:15-debug: refresh db internal success
2025-9-5 11:40:15-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 11:40:15-debug: refresh db assets success
2025-9-5 11:40:15-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 11:40:15-debug: asset-db:refresh-all-database (149ms)
2025-9-5 11:40:15-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 11:40:15-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-5 11:40:24-debug: refresh db internal success
2025-9-5 11:40:24-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 11:40:24-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 11:40:24-debug: refresh db assets success
2025-9-5 11:40:24-debug: asset-db:refresh-all-database (99ms)
2025-9-5 11:40:24-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 11:40:24-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-5 11:45:48-debug: refresh db internal success
2025-9-5 11:45:48-debug: %cImport%c: E:\M2Game\Client\assets\editor\gizmos\GizmoManager.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:45:48-debug: %cImport%c: E:\M2Game\Client\assets\editor\gizmos\EmitterGizmo.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:45:48-debug: %cImport%c: E:\M2Game\Client\assets\editor\gizmos\GizmoUtils.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:45:48-debug: refresh db assets success
2025-9-5 11:45:48-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 11:45:48-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 11:45:48-debug: asset-db:refresh-all-database (134ms)
2025-9-5 11:45:48-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 11:45:48-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 11:47:55-debug: refresh db internal success
2025-9-5 11:47:55-debug: %cImport%c: E:\M2Game\Client\assets\editor\gizmos\GizmoUtils.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:47:55-debug: refresh db assets success
2025-9-5 11:47:55-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 11:47:55-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 11:47:55-debug: asset-db:refresh-all-database (131ms)
2025-9-5 11:47:55-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 11:47:55-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 11:48:35-debug: refresh db internal success
2025-9-5 11:48:35-debug: refresh db assets success
2025-9-5 11:48:35-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 11:48:35-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 11:48:35-debug: asset-db:refresh-all-database (128ms)
2025-9-5 11:48:35-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 11:48:35-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-5 11:51:51-debug: refresh db internal success
2025-9-5 11:51:51-debug: %cImport%c: E:\M2Game\Client\assets\editor\gizmos\EmitterGizmo.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:51:51-debug: %cImport%c: E:\M2Game\Client\assets\editor\gizmos\GizmoManager.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:51:51-debug: %cImport%c: E:\M2Game\Client\assets\editor\gizmos\GizmoUtils.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:51:51-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 11:51:51-debug: refresh db assets success
2025-9-5 11:51:51-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 11:51:51-debug: asset-db:refresh-all-database (149ms)
2025-9-5 11:51:51-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 11:52:12-debug: refresh db internal success
2025-9-5 11:52:12-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 11:52:12-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 11:52:12-debug: refresh db assets success
2025-9-5 11:52:12-debug: asset-db:refresh-all-database (102ms)
2025-9-5 11:52:57-debug: refresh db internal success
2025-9-5 11:52:58-debug: refresh db assets success
2025-9-5 11:52:58-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 11:52:58-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 11:52:58-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-5 11:52:58-debug: asset-db:refresh-all-database (109ms)
2025-9-5 11:52:58-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-5 11:53:00-debug: %cImport%c: E:\M2Game\Client\assets\scenes\BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:53:00-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (3ms)
2025-9-5 11:53:00-debug: refresh db internal success
2025-9-5 11:53:00-debug: refresh db assets success
2025-9-5 11:53:00-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 11:53:00-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 11:53:00-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 11:53:00-debug: asset-db:refresh-all-database (103ms)
2025-9-5 11:53:00-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 11:53:07-debug: refresh db internal success
2025-9-5 11:53:07-debug: refresh db assets success
2025-9-5 11:53:07-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 11:53:07-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 11:53:07-debug: asset-db:refresh-all-database (98ms)
2025-9-5 11:53:07-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 11:53:07-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 11:53:22-debug: refresh db internal success
2025-9-5 11:53:22-debug: %cImport%c: E:\M2Game\Client\assets\editor\gizmos\GizmoManager.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:53:22-debug: refresh db assets success
2025-9-5 11:53:22-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 11:53:22-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 11:53:22-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-5 11:53:22-debug: asset-db:refresh-all-database (106ms)
2025-9-5 11:53:22-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-5 11:53:48-debug: %cImport%c: E:\M2Game\Client\assets\scenes\BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:53:48-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (3ms)
2025-9-5 11:56:54-debug: refresh db internal success
2025-9-5 11:56:54-debug: %cImport%c: E:\M2Game\Client\assets\editor\gizmos\GizmoManager.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:56:54-debug: refresh db assets success
2025-9-5 11:56:54-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 11:56:54-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 11:56:54-debug: asset-db:refresh-all-database (131ms)
2025-9-5 11:56:54-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 11:56:54-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 11:57:35-debug: refresh db internal success
2025-9-5 11:57:35-debug: refresh db assets success
2025-9-5 11:57:35-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 11:57:35-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 11:57:35-debug: asset-db:refresh-all-database (124ms)
2025-9-5 11:57:35-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 11:57:35-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 11:58:21-debug: refresh db internal success
2025-9-5 11:58:21-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 11:58:21-debug: refresh db assets success
2025-9-5 11:58:21-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 11:58:21-debug: asset-db:refresh-all-database (103ms)
2025-9-5 11:58:21-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 11:58:21-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 11:58:27-debug: refresh db internal success
2025-9-5 11:58:27-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 11:58:27-debug: refresh db assets success
2025-9-5 11:58:27-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 11:58:27-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 11:58:27-debug: asset-db:refresh-all-database (101ms)
2025-9-5 11:58:27-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 11:58:49-debug: refresh db internal success
2025-9-5 11:58:49-debug: refresh db assets success
2025-9-5 11:58:49-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 11:58:49-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 11:58:49-debug: asset-db:refresh-all-database (125ms)
2025-9-5 11:58:49-debug: asset-db:worker-effect-data-processing (3ms)
2025-9-5 11:58:49-debug: asset-db-hook-engine-extends-afterRefresh (3ms)
2025-9-5 11:59:20-debug: refresh db internal success
2025-9-5 11:59:20-debug: refresh db assets success
2025-9-5 11:59:20-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 11:59:20-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 11:59:20-debug: asset-db:refresh-all-database (99ms)
2025-9-5 11:59:20-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 11:59:20-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 11:59:24-debug: refresh db internal success
2025-9-5 11:59:24-debug: refresh db assets success
2025-9-5 11:59:24-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 11:59:24-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 11:59:24-debug: asset-db:refresh-all-database (97ms)
2025-9-5 11:59:24-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-5 11:59:24-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-5 11:59:31-debug: %cImport%c: E:\M2Game\Client\assets\scenes\BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-5 11:59:31-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (3ms)
2025-9-5 12:04:06-debug: refresh db internal success
2025-9-5 12:04:06-debug: refresh db assets success
2025-9-5 12:04:06-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 12:04:06-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 12:04:06-debug: asset-db:refresh-all-database (125ms)
2025-9-5 12:05:44-debug: refresh db internal success
2025-9-5 12:05:44-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 12:05:44-debug: refresh db assets success
2025-9-5 12:05:44-debug: asset-db:refresh-all-database (114ms)
2025-9-5 12:05:44-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 12:05:44-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 12:05:50-debug: refresh db internal success
2025-9-5 12:05:50-debug: refresh db assets success
2025-9-5 12:05:50-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 12:05:50-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 12:05:50-debug: asset-db:refresh-all-database (101ms)
2025-9-5 12:06:53-debug: refresh db internal success
2025-9-5 12:06:53-debug: refresh db assets success
2025-9-5 12:06:53-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 12:06:53-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 12:06:53-debug: asset-db:refresh-all-database (102ms)
2025-9-5 12:06:53-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 12:06:53-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 12:07:08-debug: refresh db internal success
2025-9-5 12:07:08-debug: refresh db assets success
2025-9-5 12:07:08-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 12:07:08-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 12:07:08-debug: asset-db:refresh-all-database (100ms)
2025-9-5 12:07:08-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-5 12:07:45-debug: refresh db internal success
2025-9-5 12:07:45-debug: refresh db assets success
2025-9-5 12:07:45-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 12:07:45-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 12:07:45-debug: asset-db:refresh-all-database (109ms)
2025-9-5 12:07:45-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 12:07:45-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 12:07:48-debug: refresh db internal success
2025-9-5 12:07:48-debug: refresh db assets success
2025-9-5 12:07:48-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 12:07:48-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 12:07:48-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 12:07:48-debug: asset-db:refresh-all-database (96ms)
2025-9-5 12:07:48-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 12:07:50-debug: refresh db internal success
2025-9-5 12:07:50-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 12:07:50-debug: refresh db assets success
2025-9-5 12:07:50-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 12:07:50-debug: asset-db:refresh-all-database (95ms)
2025-9-5 12:07:50-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 12:07:50-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 12:07:54-debug: refresh db internal success
2025-9-5 12:07:54-debug: refresh db assets success
2025-9-5 12:07:54-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 12:07:54-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 12:07:54-debug: asset-db:refresh-all-database (96ms)
2025-9-5 12:08:00-debug: refresh db internal success
2025-9-5 12:08:00-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 12:08:00-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 12:08:00-debug: refresh db assets success
2025-9-5 12:08:00-debug: asset-db:refresh-all-database (99ms)
2025-9-5 12:08:00-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 12:09:46-debug: refresh db internal success
2025-9-5 12:09:46-debug: %cImport%c: E:\M2Game\Client\assets\editor\emitter\EmitterEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 12:09:46-debug: refresh db assets success
2025-9-5 12:09:46-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 12:09:46-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 12:09:46-debug: asset-db:refresh-all-database (128ms)
2025-9-5 12:09:46-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 12:09:46-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 12:10:28-debug: refresh db internal success
2025-9-5 12:10:28-debug: refresh db assets success
2025-9-5 12:10:28-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 12:10:28-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 12:10:28-debug: asset-db:refresh-all-database (126ms)
2025-9-5 12:10:28-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 12:10:28-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 12:12:32-debug: refresh db internal success
2025-9-5 12:12:32-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 12:12:32-debug: refresh db assets success
2025-9-5 12:12:32-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 12:12:32-debug: asset-db:refresh-all-database (101ms)
2025-9-5 12:12:32-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 12:13:33-debug: refresh db internal success
2025-9-5 12:13:33-debug: refresh db assets success
2025-9-5 12:13:33-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 12:13:33-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 12:13:33-debug: asset-db:refresh-all-database (144ms)
2025-9-5 12:13:33-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-5 12:13:33-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-5 12:14:58-debug: refresh db internal success
2025-9-5 12:14:59-debug: refresh db assets success
2025-9-5 12:14:59-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 12:14:59-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 12:14:59-debug: asset-db:refresh-all-database (122ms)
2025-9-5 12:14:59-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 12:15:04-debug: refresh db internal success
2025-9-5 12:15:04-debug: refresh db assets success
2025-9-5 12:15:04-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 12:15:04-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 12:15:04-debug: asset-db:refresh-all-database (99ms)
2025-9-5 12:34:06-debug: refresh db internal success
2025-9-5 12:34:06-debug: refresh db assets success
2025-9-5 12:34:06-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 12:34:06-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 12:34:06-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 12:34:06-debug: asset-db:refresh-all-database (102ms)
2025-9-5 12:34:06-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 12:34:34-debug: refresh db internal success
2025-9-5 12:34:34-debug: refresh db assets success
2025-9-5 12:34:34-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 12:34:34-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 12:34:34-debug: asset-db:refresh-all-database (98ms)
2025-9-5 12:34:34-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 12:34:34-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 12:34:37-debug: refresh db internal success
2025-9-5 12:34:37-debug: refresh db assets success
2025-9-5 12:34:37-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 12:34:37-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 12:34:37-debug: asset-db:refresh-all-database (98ms)
2025-9-5 12:34:37-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 12:34:37-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 12:36:30-debug: refresh db internal success
2025-9-5 12:36:30-debug: %cImport%c: E:\M2Game\Client\assets\editor\emitter\EmitterEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 12:36:30-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 12:36:30-debug: refresh db assets success
2025-9-5 12:36:30-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 12:36:30-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 12:36:30-debug: asset-db:refresh-all-database (106ms)
2025-9-5 12:36:30-debug: asset-db:worker-effect-data-processing (4ms)
2025-9-5 12:36:30-debug: asset-db-hook-engine-extends-afterRefresh (4ms)
2025-9-5 12:39:13-debug: refresh db internal success
2025-9-5 12:39:13-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 12:39:13-debug: refresh db assets success
2025-9-5 12:39:13-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 12:39:13-debug: asset-db:refresh-all-database (122ms)
2025-9-5 12:42:26-debug: refresh db internal success
2025-9-5 12:42:26-debug: %cImport%c: E:\M2Game\Client\assets\editor\gizmos\EmitterGizmo.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 12:42:26-debug: refresh db assets success
2025-9-5 12:42:26-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 12:42:26-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 12:42:26-debug: asset-db:refresh-all-database (125ms)
2025-9-5 12:42:26-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 12:42:26-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-5 12:42:43-debug: refresh db internal success
2025-9-5 12:42:43-debug: refresh db assets success
2025-9-5 12:42:43-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 12:42:43-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 12:42:43-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 12:42:43-debug: asset-db:refresh-all-database (102ms)
2025-9-5 12:42:43-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 12:42:46-debug: refresh db internal success
2025-9-5 12:42:46-debug: refresh db assets success
2025-9-5 12:42:46-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 12:42:46-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 12:42:46-debug: asset-db:refresh-all-database (104ms)
2025-9-5 12:42:46-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 12:42:46-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 12:43:03-log: 资源数据库已锁定，资源操作(bound _refreshAsset)将会延迟响应，请稍侯
2025-9-5 12:43:03-debug: refresh db internal success
2025-9-5 12:43:03-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\prefabs\emitter\TestNewEmitter.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-5 12:43:03-error: Importer exec failed: {asset[E:\M2Game\Client\assets\resources\Game\prefabs\emitter\TestNewEmitter.prefab](42ef9a7f-ace3-4133-83ab-6d149fa74bd9)}
2025-9-5 12:43:03-error: SyntaxError: E:\M2Game\Client\assets\resources\Game\prefabs\emitter\TestNewEmitter.prefab: Unexpected end of JSON input
    at JSON.parse (<anonymous>)
    at _readFile (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\fs-extra\node_modules\jsonfile\index.js:25:16)
    at import (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\modules\engine-extensions\extensions\engine-extends\dist\handler\assets\scene\prefab.ccc:1:943)
    at CustomImporter.import (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\asset-db\dist\worker\manager\asset-handler-manager.ccc:1:1243)
2025-9-5 12:43:03-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\prefabs\emitter
background: #aaff85; color: #000;
color: #000;
2025-9-5 12:43:03-debug: refresh db assets success
2025-9-5 12:43:03-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 12:43:03-debug: start refresh asset from E:\M2Game\Client\assets\resources\Game\prefabs\emitter\TestNewEmitter.prefab...
2025-9-5 12:43:03-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 12:43:03-debug: refresh asset E:\M2Game\Client\assets\resources\Game\prefabs\emitter success
2025-9-5 12:43:03-debug: asset-db:refresh-all-database (106ms)
2025-9-5 12:43:03-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 12:43:03-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 12:45:20-debug: refresh db internal success
2025-9-5 12:45:20-debug: %cImport%c: E:\M2Game\Client\assets\editor\gizmos\EmitterGizmo.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 12:45:20-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 12:45:20-debug: refresh db assets success
2025-9-5 12:45:20-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 12:45:20-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 12:45:20-debug: asset-db:refresh-all-database (134ms)
2025-9-5 12:45:20-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 12:45:52-debug: refresh db internal success
2025-9-5 12:45:52-debug: refresh db assets success
2025-9-5 12:45:52-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 12:45:52-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 12:45:52-debug: asset-db:refresh-all-database (128ms)
2025-9-5 12:45:52-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 12:45:52-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 12:46:29-debug: refresh db internal success
2025-9-5 12:46:29-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 12:46:29-debug: refresh db assets success
2025-9-5 12:46:29-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 12:46:29-debug: asset-db:refresh-all-database (133ms)
2025-9-5 12:46:30-debug: refresh db internal success
2025-9-5 12:46:30-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 12:46:30-debug: refresh db assets success
2025-9-5 12:46:30-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 12:46:30-debug: asset-db:refresh-all-database (126ms)
2025-9-5 12:51:31-debug: refresh db internal success
2025-9-5 12:51:31-debug: refresh db assets success
2025-9-5 12:51:31-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 12:51:31-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 12:51:31-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 12:51:31-debug: asset-db:refresh-all-database (127ms)
2025-9-5 12:51:31-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 12:51:34-debug: programming:execute-script (1ms)
2025-9-5 12:51:35-debug: start remove asset E:\M2Game\Client\assets\resources\Game\prefabs\emitter\TestNewEmitter.prefab...
2025-9-5 12:51:35-debug: start refresh asset from E:\M2Game\Client\assets\resources\Game\prefabs\emitter\TestNewEmitter.prefab...
2025-9-5 12:51:35-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\Game\prefabs\emitter\TestNewEmitter.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 12:51:35-debug: refresh asset E:\M2Game\Client\assets\resources\Game\prefabs\emitter success
2025-9-5 12:51:35-debug: remove asset E:\M2Game\Client\assets\resources\Game\prefabs\emitter\TestNewEmitter.prefab success
2025-9-5 12:51:35-debug: refresh db internal success
2025-9-5 12:51:35-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\prefabs\emitter
background: #aaff85; color: #000;
color: #000;
2025-9-5 12:51:35-debug: refresh db assets success
2025-9-5 12:51:35-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 12:51:35-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 12:51:35-debug: asset-db:refresh-all-database (159ms)
2025-9-5 12:51:35-debug: asset-db:worker-effect-data-processing (11ms)
2025-9-5 12:51:35-debug: asset-db-hook-engine-extends-afterRefresh (11ms)
2025-9-5 12:54:27-debug: refresh db internal success
2025-9-5 12:54:28-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 12:54:28-debug: refresh db assets success
2025-9-5 12:54:28-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 12:54:28-debug: asset-db:refresh-all-database (130ms)
2025-9-5 12:54:42-debug: refresh db internal success
2025-9-5 12:54:42-debug: refresh db assets success
2025-9-5 12:54:42-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 12:54:42-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 12:54:42-debug: asset-db:refresh-all-database (98ms)
2025-9-5 12:54:47-debug: refresh db internal success
2025-9-5 12:54:47-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 12:54:47-debug: refresh db assets success
2025-9-5 12:54:47-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 12:54:47-debug: asset-db:refresh-all-database (99ms)
2025-9-5 12:54:47-debug: asset-db:worker-effect-data-processing (-1ms)
2025-9-5 12:54:47-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 12:54:58-debug: refresh db internal success
2025-9-5 12:54:58-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\prefabs\emitter
background: #aaff85; color: #000;
color: #000;
2025-9-5 12:54:58-debug: refresh db assets success
2025-9-5 12:54:58-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 12:54:58-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 12:54:58-debug: asset-db:refresh-all-database (138ms)
2025-9-5 12:54:58-debug: asset-db:worker-effect-data-processing (26ms)
2025-9-5 12:54:58-debug: asset-db-hook-engine-extends-afterRefresh (26ms)
2025-9-5 12:55:19-debug: refresh db internal success
2025-9-5 12:55:19-debug: refresh db assets success
2025-9-5 12:55:19-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 12:55:19-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 12:55:19-debug: asset-db:refresh-all-database (120ms)
2025-9-5 12:55:19-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 12:55:19-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 12:55:29-debug: refresh db internal success
2025-9-5 12:55:29-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 12:55:29-debug: refresh db assets success
2025-9-5 12:55:29-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 12:55:29-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 12:55:29-debug: asset-db:refresh-all-database (128ms)
2025-9-5 12:55:29-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 12:56:51-debug: refresh db internal success
2025-9-5 12:56:51-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 12:56:51-debug: refresh db assets success
2025-9-5 12:56:51-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 12:56:51-debug: asset-db:refresh-all-database (126ms)
2025-9-5 12:56:51-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 12:57:03-debug: refresh db internal success
2025-9-5 12:57:03-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\prefabs\emitter
background: #aaff85; color: #000;
color: #000;
2025-9-5 12:57:03-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 12:57:03-debug: refresh db assets success
2025-9-5 12:57:03-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 12:57:03-debug: asset-db:refresh-all-database (117ms)
2025-9-5 12:57:03-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 12:57:03-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 12:57:22-debug: refresh db internal success
2025-9-5 12:57:23-debug: refresh db assets success
2025-9-5 12:57:23-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 12:57:23-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 12:57:23-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 12:57:23-debug: asset-db:refresh-all-database (139ms)
2025-9-5 12:57:23-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-5 12:57:28-debug: refresh db internal success
2025-9-5 12:57:28-debug: refresh db assets success
2025-9-5 12:57:28-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 12:57:28-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 12:57:28-debug: asset-db:refresh-all-database (98ms)
2025-9-5 12:57:28-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 12:57:28-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 12:57:42-log: 资源数据库已锁定，资源操作(bound _refreshAsset)将会延迟响应，请稍侯
2025-9-5 12:57:42-debug: refresh db internal success
2025-9-5 12:57:42-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\prefabs\emitter\test.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-5 12:57:42-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\prefabs\emitter
background: #aaff85; color: #000;
color: #000;
2025-9-5 12:57:42-debug: refresh db assets success
2025-9-5 12:57:42-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 12:57:42-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 12:57:42-debug: start refresh asset from E:\M2Game\Client\assets\resources\Game\prefabs\emitter\test.prefab...
2025-9-5 12:57:42-debug: refresh asset E:\M2Game\Client\assets\resources\Game\prefabs\emitter success
2025-9-5 12:57:42-debug: asset-db:refresh-all-database (109ms)
2025-9-5 12:57:42-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 12:57:42-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 12:57:58-debug: refresh db internal success
2025-9-5 12:57:58-debug: refresh db assets success
2025-9-5 12:57:58-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 12:57:58-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 12:57:58-debug: asset-db:refresh-all-database (118ms)
2025-9-5 12:58:55-debug: refresh db internal success
2025-9-5 12:58:55-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 12:58:55-debug: refresh db assets success
2025-9-5 12:58:55-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 12:58:55-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 12:58:55-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 12:58:55-debug: asset-db:refresh-all-database (131ms)
2025-9-5 12:58:55-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 12:59:00-debug: refresh db internal success
2025-9-5 12:59:00-debug: refresh db assets success
2025-9-5 12:59:00-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 12:59:00-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 12:59:00-debug: asset-db:refresh-all-database (100ms)
2025-9-5 12:59:00-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 12:59:00-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 12:59:04-debug: refresh db internal success
2025-9-5 12:59:04-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 12:59:04-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 12:59:04-debug: refresh db assets success
2025-9-5 12:59:04-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 12:59:04-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 12:59:04-debug: asset-db:refresh-all-database (99ms)
2025-9-5 12:59:11-debug: refresh db internal success
2025-9-5 12:59:11-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 12:59:11-debug: refresh db assets success
2025-9-5 12:59:11-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 12:59:11-debug: asset-db:refresh-all-database (101ms)
2025-9-5 12:59:11-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 12:59:20-log: 资源数据库已锁定，资源操作(bound _refreshAsset)将会延迟响应，请稍侯
2025-9-5 12:59:20-debug: refresh db internal success
2025-9-5 12:59:20-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\prefabs\emitter\test2.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-5 12:59:20-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\prefabs\emitter
background: #aaff85; color: #000;
color: #000;
2025-9-5 12:59:20-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 12:59:20-debug: start refresh asset from E:\M2Game\Client\assets\resources\Game\prefabs\emitter\test2.prefab...
2025-9-5 12:59:20-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 12:59:20-debug: refresh db assets success
2025-9-5 12:59:20-debug: asset-db:refresh-all-database (122ms)
2025-9-5 12:59:20-debug: refresh asset E:\M2Game\Client\assets\resources\Game\prefabs\emitter success
2025-9-5 12:59:20-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 12:59:20-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-5 13:01:48-debug: refresh db internal success
2025-9-5 13:01:48-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\BulletSystem.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 13:01:48-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 13:01:48-debug: refresh db assets success
2025-9-5 13:01:48-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 13:01:48-debug: asset-db:refresh-all-database (130ms)
2025-9-5 13:02:54-debug: refresh db internal success
2025-9-5 13:02:54-debug: %cImport%c: E:\M2Game\Client\assets\editor\emitter\EmitterEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 13:02:54-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 13:02:54-debug: refresh db assets success
2025-9-5 13:02:54-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 13:02:54-debug: asset-db:refresh-all-database (135ms)
2025-9-5 13:03:07-debug: refresh db internal success
2025-9-5 13:03:07-debug: refresh db assets success
2025-9-5 13:03:07-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 13:03:07-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 13:03:07-debug: asset-db:refresh-all-database (99ms)
2025-9-5 13:04:54-debug: refresh db internal success
2025-9-5 13:04:54-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\BulletSystem.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 13:04:54-debug: refresh db assets success
2025-9-5 13:04:54-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 13:04:54-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 13:04:54-debug: asset-db:refresh-all-database (132ms)
2025-9-5 13:05:42-debug: refresh db internal success
2025-9-5 13:05:42-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\BulletController.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 13:05:42-debug: refresh db assets success
2025-9-5 13:05:42-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 13:05:42-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 13:05:42-debug: asset-db:refresh-all-database (127ms)
2025-9-5 13:05:42-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 13:06:02-debug: refresh db internal success
2025-9-5 13:06:02-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 13:06:02-debug: refresh db assets success
2025-9-5 13:06:02-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 13:06:02-debug: asset-db:refresh-all-database (131ms)
2025-9-5 13:06:14-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\prefabs\Bullet_New.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-5 13:06:14-debug: asset-db:reimport-asset68ac1a9d-3829-40ab-9efb-62a7794c31ed (1ms)
2025-9-5 13:07:25-debug: refresh db internal success
2025-9-5 13:07:25-debug: refresh db assets success
2025-9-5 13:07:25-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 13:07:25-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 13:07:25-debug: asset-db:refresh-all-database (130ms)
2025-9-5 13:07:42-debug: refresh db internal success
2025-9-5 13:07:42-debug: refresh db assets success
2025-9-5 13:07:42-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 13:07:42-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 13:07:42-debug: asset-db:refresh-all-database (105ms)
2025-9-5 13:08:36-debug: refresh db internal success
2025-9-5 13:08:36-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\data\bullet\EmitterData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 13:08:36-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\data\bullet\ExpressionValue.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 13:08:36-debug: refresh db assets success
2025-9-5 13:08:36-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 13:08:36-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 13:08:36-debug: asset-db:refresh-all-database (134ms)
2025-9-5 13:08:36-debug: asset-db:worker-effect-data-processing (3ms)
2025-9-5 13:08:36-debug: asset-db-hook-engine-extends-afterRefresh (3ms)
2025-9-5 13:09:26-debug: refresh db internal success
2025-9-5 13:09:26-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\data\bullet\BulletData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 13:09:26-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\data\bullet\EmitterData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 13:09:26-debug: refresh db assets success
2025-9-5 13:09:26-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 13:09:26-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 13:09:26-debug: asset-db:refresh-all-database (113ms)
2025-9-5 13:09:26-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 13:09:26-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 13:09:30-debug: start remove asset E:\M2Game\Client\assets\resources\Game\prefabs\emitter\test2.prefab...
2025-9-5 13:09:30-debug: start refresh asset from E:\M2Game\Client\assets\resources\Game\prefabs\emitter\test2.prefab...
2025-9-5 13:09:31-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\Game\prefabs\emitter\test2.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 13:09:31-log: 资源数据库已锁定，资源操作(bound autoRefreshAssetLazy)将会延迟响应，请稍侯
2025-9-5 13:09:31-debug: remove asset E:\M2Game\Client\assets\resources\Game\prefabs\emitter\test2.prefab success
2025-9-5 13:09:31-debug: refresh asset E:\M2Game\Client\assets\resources\Game\prefabs\emitter success
2025-9-5 13:09:31-debug: refresh db internal success
2025-9-5 13:09:31-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\prefabs\emitter
background: #aaff85; color: #000;
color: #000;
2025-9-5 13:09:31-debug: refresh db assets success
2025-9-5 13:09:31-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 13:09:31-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 13:09:31-debug: asset-db:refresh-all-database (159ms)
2025-9-5 13:09:31-debug: asset-db:worker-effect-data-processing (38ms)
2025-9-5 13:09:31-debug: asset-db-hook-engine-extends-afterRefresh (40ms)
2025-9-5 13:09:33-debug: start remove asset E:\M2Game\Client\assets\resources\Game\prefabs\emitter\test.prefab...
2025-9-5 13:09:33-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\Game\prefabs\emitter\test.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 13:09:33-debug: start refresh asset from E:\M2Game\Client\assets\resources\Game\prefabs\emitter\test.prefab...
2025-9-5 13:09:33-log: 资源数据库已锁定，资源操作(bound autoRefreshAssetLazy)将会延迟响应，请稍侯
2025-9-5 13:09:33-debug: remove asset E:\M2Game\Client\assets\resources\Game\prefabs\emitter\test.prefab success
2025-9-5 13:09:33-debug: refresh asset E:\M2Game\Client\assets\resources\Game\prefabs\emitter success
2025-9-5 13:09:33-debug: refresh db internal success
2025-9-5 13:09:33-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\prefabs\emitter
background: #aaff85; color: #000;
color: #000;
2025-9-5 13:09:33-debug: refresh db assets success
2025-9-5 13:09:33-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 13:09:33-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 13:09:33-debug: asset-db:refresh-all-database (144ms)
2025-9-5 13:09:33-debug: asset-db:worker-effect-data-processing (29ms)
2025-9-5 13:09:33-debug: asset-db-hook-engine-extends-afterRefresh (32ms)
2025-9-5 13:09:42-debug: refresh db internal success
2025-9-5 13:09:42-log: 资源数据库已锁定，资源操作(bound _refreshAsset)将会延迟响应，请稍侯
2025-9-5 13:09:42-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\prefabs\emitter
background: #aaff85; color: #000;
color: #000;
2025-9-5 13:09:42-debug: refresh db assets success
2025-9-5 13:09:42-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 13:09:42-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 13:09:42-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\prefabs\emitter\test.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-5 13:09:42-debug: start refresh asset from E:\M2Game\Client\assets\resources\Game\prefabs\emitter\test.prefab...
2025-9-5 13:09:42-debug: asset-db:refresh-all-database (110ms)
2025-9-5 13:09:42-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 13:09:42-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 13:09:42-debug: refresh asset E:\M2Game\Client\assets\resources\Game\prefabs\emitter success
2025-9-5 13:09:42-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\prefabs\emitter
background: #aaff85; color: #000;
color: #000;
2025-9-5 13:10:19-debug: refresh db internal success
2025-9-5 13:10:19-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 13:10:19-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 13:10:19-debug: refresh db assets success
2025-9-5 13:10:19-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 13:10:19-debug: asset-db:refresh-all-database (129ms)
2025-9-5 13:13:56-debug: refresh db internal success
2025-9-5 13:13:56-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 13:13:56-debug: refresh db assets success
2025-9-5 13:13:56-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 13:13:56-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 13:13:56-debug: asset-db:refresh-all-database (139ms)
2025-9-5 13:13:56-debug: asset-db:worker-effect-data-processing (6ms)
2025-9-5 13:13:56-debug: asset-db-hook-engine-extends-afterRefresh (6ms)
2025-9-5 13:15:16-debug: refresh db internal success
2025-9-5 13:15:17-debug: refresh db assets success
2025-9-5 13:15:17-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 13:15:17-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 13:15:17-debug: asset-db:refresh-all-database (124ms)
2025-9-5 13:15:17-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 13:15:23-debug: refresh db internal success
2025-9-5 13:15:24-debug: refresh db assets success
2025-9-5 13:15:24-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 13:15:24-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 13:15:24-debug: asset-db:refresh-all-database (98ms)
2025-9-5 13:15:34-debug: refresh db internal success
2025-9-5 13:15:34-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 13:15:34-debug: refresh db assets success
2025-9-5 13:15:34-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 13:15:34-debug: asset-db:refresh-all-database (125ms)
2025-9-5 13:15:34-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 13:15:34-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 13:15:35-debug: refresh db internal success
2025-9-5 13:15:35-debug: refresh db assets success
2025-9-5 13:15:35-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 13:15:35-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 13:15:35-debug: asset-db:refresh-all-database (120ms)
2025-9-5 13:15:35-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 13:15:35-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 13:16:13-debug: refresh db internal success
2025-9-5 13:16:13-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 13:16:13-debug: refresh db assets success
2025-9-5 13:16:13-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 13:16:13-debug: asset-db:refresh-all-database (102ms)
2025-9-5 13:16:13-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 13:16:42-debug: refresh db internal success
2025-9-5 13:16:42-debug: refresh db assets success
2025-9-5 13:16:42-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 13:16:42-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 13:16:42-debug: asset-db:refresh-all-database (134ms)
2025-9-5 13:19:27-debug: refresh db internal success
2025-9-5 13:19:27-debug: refresh db assets success
2025-9-5 13:19:27-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 13:19:27-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 13:19:27-debug: asset-db:refresh-all-database (134ms)
2025-9-5 13:19:27-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 13:19:27-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 13:19:35-debug: refresh db internal success
2025-9-5 13:19:35-debug: refresh db assets success
2025-9-5 13:19:35-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 13:19:35-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 13:19:35-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 13:19:35-debug: asset-db:refresh-all-database (107ms)
2025-9-5 13:19:35-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 13:21:30-debug: refresh db internal success
2025-9-5 13:21:30-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 13:21:30-debug: refresh db assets success
2025-9-5 13:21:30-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 13:21:30-debug: asset-db:refresh-all-database (119ms)
2025-9-5 13:23:09-debug: refresh db internal success
2025-9-5 13:23:09-debug: refresh db assets success
2025-9-5 13:23:09-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 13:23:09-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 13:23:09-debug: asset-db:refresh-all-database (99ms)
2025-9-5 13:23:09-debug: asset-db:worker-effect-data-processing (-1ms)
2025-9-5 13:24:41-debug: refresh db internal success
2025-9-5 13:24:41-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 13:24:41-debug: refresh db assets success
2025-9-5 13:24:41-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 13:24:41-debug: asset-db:refresh-all-database (117ms)
2025-9-5 13:24:45-debug: refresh db internal success
2025-9-5 13:24:45-debug: refresh db assets success
2025-9-5 13:24:45-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 13:24:45-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 13:24:45-debug: asset-db:refresh-all-database (114ms)
2025-9-5 13:24:45-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 13:24:59-debug: refresh db internal success
2025-9-5 13:24:59-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 13:24:59-debug: refresh db assets success
2025-9-5 13:24:59-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 13:24:59-debug: asset-db:refresh-all-database (103ms)
2025-9-5 13:25:38-debug: refresh db internal success
2025-9-5 13:25:38-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 13:25:38-debug: refresh db assets success
2025-9-5 13:25:38-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 13:25:38-debug: asset-db:refresh-all-database (126ms)
2025-9-5 13:25:38-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 13:25:38-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 13:26:41-debug: refresh db internal success
2025-9-5 13:26:41-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 13:26:41-debug: refresh db assets success
2025-9-5 13:26:41-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 13:26:41-debug: asset-db:refresh-all-database (140ms)
2025-9-5 13:26:41-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 13:26:41-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 13:27:31-debug: refresh db internal success
2025-9-5 13:27:31-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 13:27:31-debug: refresh db assets success
2025-9-5 13:27:31-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 13:27:31-debug: asset-db:refresh-all-database (126ms)
2025-9-5 13:27:31-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 13:27:31-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 13:27:35-debug: refresh db internal success
2025-9-5 13:27:35-debug: refresh db assets success
2025-9-5 13:27:35-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 13:27:35-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 13:27:35-debug: asset-db:refresh-all-database (120ms)
2025-9-5 13:27:42-debug: start refresh asset from E:\M2Game\Client\assets\resources\Game\prefabs\emitter\EmitterExample_copy.prefab...
2025-9-5 13:27:42-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\prefabs\emitter\EmitterExample_copy.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-5 13:27:42-debug: refresh asset E:\M2Game\Client\assets\resources\Game\prefabs\emitter success
2025-9-5 13:27:42-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\prefabs\emitter
background: #aaff85; color: #000;
color: #000;
2025-9-5 13:28:30-debug: refresh db internal success
2025-9-5 13:28:30-debug: refresh db assets success
2025-9-5 13:28:30-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 13:28:30-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 13:28:30-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 13:28:30-debug: asset-db:refresh-all-database (124ms)
2025-9-5 13:28:30-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 13:28:43-debug: refresh db internal success
2025-9-5 13:28:43-debug: refresh db assets success
2025-9-5 13:28:43-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 13:28:43-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 13:28:43-debug: asset-db:refresh-all-database (100ms)
2025-9-5 13:34:57-debug: refresh db internal success
2025-9-5 13:34:57-debug: refresh db assets success
2025-9-5 13:34:57-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 13:34:57-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 13:34:57-debug: asset-db:refresh-all-database (104ms)
2025-9-5 13:34:57-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 14:27:41-debug: refresh db internal success
2025-9-5 14:27:41-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 14:27:41-debug: refresh db assets success
2025-9-5 14:27:41-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 14:27:41-debug: asset-db:refresh-all-database (101ms)
2025-9-5 14:27:48-debug: refresh db internal success
2025-9-5 14:27:48-debug: refresh db assets success
2025-9-5 14:27:48-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 14:27:48-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 14:27:48-debug: asset-db:refresh-all-database (108ms)
2025-9-5 14:27:48-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 14:27:48-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-5 14:27:51-debug: refresh db internal success
2025-9-5 14:27:51-debug: refresh db assets success
2025-9-5 14:27:51-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 14:27:51-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 14:27:51-debug: asset-db:refresh-all-database (105ms)
2025-9-5 14:32:39-debug: refresh db internal success
2025-9-5 14:32:39-debug: refresh db assets success
2025-9-5 14:32:39-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 14:32:39-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 14:32:39-debug: asset-db:refresh-all-database (120ms)
2025-9-5 14:33:55-debug: %cImport%c: E:\M2Game\Client\assets\scenes\BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-5 14:33:55-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (2ms)
2025-9-5 14:34:18-debug: refresh db internal success
2025-9-5 14:34:19-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 14:34:19-debug: refresh db assets success
2025-9-5 14:34:19-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 14:34:19-debug: asset-db:refresh-all-database (126ms)
2025-9-5 14:34:19-debug: asset-db:worker-effect-data-processing (3ms)
2025-9-5 14:34:19-debug: asset-db-hook-engine-extends-afterRefresh (3ms)
2025-9-5 14:37:26-debug: %cImport%c: E:\M2Game\Client\assets\scenes\BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-5 14:37:26-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (2ms)
2025-9-5 14:39:05-debug: refresh db internal success
2025-9-5 14:39:05-debug: refresh db assets success
2025-9-5 14:39:05-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 14:39:05-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 14:39:05-debug: asset-db:refresh-all-database (109ms)
2025-9-5 14:39:05-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-5 14:39:05-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-5 14:39:30-debug: refresh db internal success
2025-9-5 14:39:30-debug: refresh db assets success
2025-9-5 14:39:30-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 14:39:30-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 14:39:30-debug: asset-db:refresh-all-database (129ms)
2025-9-5 14:39:30-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 14:39:30-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 14:41:50-debug: refresh db internal success
2025-9-5 14:41:50-debug: refresh db assets success
2025-9-5 14:41:50-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 14:41:50-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 14:41:50-debug: asset-db:refresh-all-database (123ms)
2025-9-5 14:43:27-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-5 14:43:27-debug: asset-db:reimport-asset401efd7e-bd20-4537-a13a-f25e6238c2a9 (3ms)
2025-9-5 14:44:35-debug: refresh db internal success
2025-9-5 14:44:35-debug: refresh db assets success
2025-9-5 14:44:35-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 14:44:35-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 14:44:35-debug: asset-db:refresh-all-database (120ms)
2025-9-5 14:44:35-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 14:44:49-debug: refresh db internal success
2025-9-5 14:44:49-debug: refresh db assets success
2025-9-5 14:44:49-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 14:44:49-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 14:44:49-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 14:44:49-debug: asset-db:refresh-all-database (97ms)
2025-9-5 14:44:49-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 14:44:53-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\level\2.json
background: #aaff85; color: #000;
color: #000;
2025-9-5 14:44:53-debug: asset-db:reimport-asset3ad94693-2598-49ff-9854-c85f26cc2a8b (3ms)
2025-9-5 14:44:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\level\1.json
background: #aaff85; color: #000;
color: #000;
2025-9-5 14:44:55-debug: asset-db:reimport-assetd4b69b47-f21e-498c-8785-c8324a4213f3 (2ms)
2025-9-5 14:44:57-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\level\2.json
background: #aaff85; color: #000;
color: #000;
2025-9-5 14:44:57-debug: asset-db:reimport-asset3ad94693-2598-49ff-9854-c85f26cc2a8b (2ms)
2025-9-5 14:44:57-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\level\1.json
background: #aaff85; color: #000;
color: #000;
2025-9-5 14:44:57-debug: asset-db:reimport-assetd4b69b47-f21e-498c-8785-c8324a4213f3 (2ms)
2025-9-5 14:44:58-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\level\2.json
background: #aaff85; color: #000;
color: #000;
2025-9-5 14:44:58-debug: asset-db:reimport-asset3ad94693-2598-49ff-9854-c85f26cc2a8b (2ms)
2025-9-5 14:44:59-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\level\20001.json
background: #aaff85; color: #000;
color: #000;
2025-9-5 14:44:59-debug: asset-db:reimport-asset91f0671d-babc-45d1-a784-92f7d868c09f (2ms)
2025-9-5 14:45:00-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\level\3.json
background: #aaff85; color: #000;
color: #000;
2025-9-5 14:45:00-debug: asset-db:reimport-assetf5e34a8e-0631-42fa-a803-961da4598cdd (2ms)
2025-9-5 14:45:01-debug: refresh db internal success
2025-9-5 14:45:01-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 14:45:01-debug: refresh db assets success
2025-9-5 14:45:01-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 14:45:01-debug: asset-db:refresh-all-database (102ms)
2025-9-5 14:45:01-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 14:45:01-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 15:07:20-debug: refresh db internal success
2025-9-5 15:07:20-debug: refresh db assets success
2025-9-5 15:07:20-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 15:07:20-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 15:07:20-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 15:07:20-debug: asset-db:refresh-all-database (133ms)
2025-9-5 15:07:20-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 15:07:26-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\level\4.json
background: #aaff85; color: #000;
color: #000;
2025-9-5 15:07:26-debug: asset-db:reimport-asset63344f11-bb27-454f-88a7-397b8e522b06 (3ms)
2025-9-5 15:07:26-debug: refresh db internal success
2025-9-5 15:07:27-debug: refresh db assets success
2025-9-5 15:07:27-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 15:07:27-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 15:07:27-debug: asset-db:refresh-all-database (102ms)
2025-9-5 15:07:27-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 15:08:04-debug: refresh db internal success
2025-9-5 15:08:04-debug: refresh db assets success
2025-9-5 15:08:04-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 15:08:04-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 15:08:04-debug: asset-db:refresh-all-database (121ms)
2025-9-5 15:08:04-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 15:08:04-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 15:08:10-debug: refresh db internal success
2025-9-5 15:08:10-debug: refresh db assets success
2025-9-5 15:08:10-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 15:08:10-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 15:08:10-debug: asset-db:refresh-all-database (99ms)
2025-9-5 15:13:52-debug: refresh db internal success
2025-9-5 15:13:52-debug: refresh db assets success
2025-9-5 15:13:52-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 15:13:52-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 15:13:52-debug: asset-db:refresh-all-database (106ms)
2025-9-5 15:13:52-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 15:14:36-debug: refresh db internal success
2025-9-5 15:14:36-debug: refresh db assets success
2025-9-5 15:14:36-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 15:14:36-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 15:14:36-debug: asset-db:refresh-all-database (105ms)
2025-9-5 15:14:36-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 15:14:36-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 15:17:01-debug: refresh db internal success
2025-9-5 15:17:01-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\weapon\WeaponSlots.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 15:17:01-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\weapon\Weapon.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 15:17:01-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\weapon
background: #aaff85; color: #000;
color: #000;
2025-9-5 15:17:01-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\plane
background: #aaff85; color: #000;
color: #000;
2025-9-5 15:17:01-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 15:17:01-debug: refresh db assets success
2025-9-5 15:17:01-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 15:17:01-debug: asset-db:refresh-all-database (147ms)
2025-9-5 15:29:40-debug: refresh db internal success
2025-9-5 15:29:40-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\weapon\Weapon.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 15:29:40-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\weapon\WeaponSlots.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 15:29:40-debug: refresh db assets success
2025-9-5 15:29:40-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 15:29:40-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 15:29:40-debug: asset-db:refresh-all-database (124ms)
2025-9-5 15:30:50-debug: start refresh asset from E:\M2Game\Client\assets\resources\Game\prefabs\emitter\EmitterExample.prefab...
2025-9-5 15:30:50-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\prefabs\emitter\EmitterExample.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-5 15:30:50-debug: refresh asset E:\M2Game\Client\assets\resources\Game\prefabs\emitter success
2025-9-5 15:30:50-debug: refresh db internal success
2025-9-5 15:30:50-debug: refresh db assets success
2025-9-5 15:30:50-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 15:30:50-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 15:30:50-debug: asset-db:refresh-all-database (104ms)
2025-9-5 15:33:43-debug: refresh db internal success
2025-9-5 15:33:43-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\EventGroup.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 15:33:43-debug: refresh db assets success
2025-9-5 15:33:43-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 15:33:43-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 15:33:43-debug: asset-db:refresh-all-database (128ms)
2025-9-5 15:33:43-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 15:33:43-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 15:35:45-debug: refresh db internal success
2025-9-5 15:35:45-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\BulletSystem.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 15:35:45-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\EventGroup.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 15:35:45-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 15:35:45-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 15:35:45-debug: refresh db assets success
2025-9-5 15:35:45-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 15:35:45-debug: asset-db:refresh-all-database (133ms)
2025-9-5 15:35:45-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 15:35:50-debug: %cImport%c: E:\M2Game\Client\assets\scenes\BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-5 15:35:50-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (2ms)
2025-9-5 15:36:39-debug: refresh db internal success
2025-9-5 15:36:39-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\EventGroup.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 15:36:39-debug: refresh db assets success
2025-9-5 15:36:39-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 15:36:39-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 15:36:39-debug: asset-db:refresh-all-database (125ms)
2025-9-5 15:37:06-debug: %cImport%c: E:\M2Game\Client\assets\scenes\BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-5 15:37:06-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (2ms)
2025-9-5 15:37:28-debug: refresh db internal success
2025-9-5 15:37:28-debug: refresh db assets success
2025-9-5 15:37:28-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 15:37:28-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 15:37:28-debug: asset-db:refresh-all-database (130ms)
2025-9-5 15:37:28-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 15:37:48-debug: %cImport%c: E:\M2Game\Client\assets\scenes\BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-5 15:37:48-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (2ms)
2025-9-5 15:38:09-debug: %cImport%c: E:\M2Game\Client\assets\scenes\BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-5 15:38:09-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (3ms)
2025-9-5 15:39:09-debug: refresh db internal success
2025-9-5 15:39:09-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\EventGroup.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 15:39:09-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 15:39:09-debug: refresh db assets success
2025-9-5 15:39:09-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 15:39:09-debug: asset-db:refresh-all-database (137ms)
2025-9-5 15:39:09-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 15:39:09-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 15:39:47-debug: refresh db internal success
2025-9-5 15:39:47-debug: refresh db assets success
2025-9-5 15:39:47-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 15:39:47-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 15:39:47-debug: asset-db:refresh-all-database (126ms)
2025-9-5 15:39:47-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 15:41:50-debug: refresh db internal success
2025-9-5 15:41:50-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\actions\BulletEventActions.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 15:41:50-debug: refresh db assets success
2025-9-5 15:41:50-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 15:41:50-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 15:41:50-debug: asset-db:refresh-all-database (129ms)
2025-9-5 15:42:25-debug: refresh db internal success
2025-9-5 15:42:25-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 15:42:25-debug: refresh db assets success
2025-9-5 15:42:25-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 15:42:25-debug: asset-db:refresh-all-database (121ms)
2025-9-5 15:42:25-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 15:42:25-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 15:42:35-debug: refresh db internal success
2025-9-5 15:42:35-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 15:42:35-debug: refresh db assets success
2025-9-5 15:42:35-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 15:42:35-debug: asset-db:refresh-all-database (98ms)
2025-9-5 15:42:35-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 15:42:35-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 15:42:43-debug: refresh db internal success
2025-9-5 15:42:43-debug: refresh db assets success
2025-9-5 15:42:43-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 15:42:43-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 15:42:43-debug: asset-db:refresh-all-database (102ms)
2025-9-5 15:42:43-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 15:42:43-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 15:45:02-debug: refresh db internal success
2025-9-5 15:45:02-debug: refresh db assets success
2025-9-5 15:45:02-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 15:45:02-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 15:45:02-debug: asset-db:refresh-all-database (98ms)
2025-9-5 15:45:02-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 15:45:02-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 15:46:21-debug: refresh db internal success
2025-9-5 15:46:21-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\EventGroup.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 15:46:22-debug: refresh db assets success
2025-9-5 15:46:22-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 15:46:22-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 15:46:22-debug: asset-db:refresh-all-database (123ms)
2025-9-5 15:46:22-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-5 15:46:22-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-5 15:47:25-debug: refresh db internal success
2025-9-5 15:47:25-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\actions\BulletEventActions.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 15:47:25-debug: refresh db assets success
2025-9-5 15:47:25-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 15:47:25-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 15:47:25-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 15:47:25-debug: asset-db:refresh-all-database (123ms)
2025-9-5 15:47:25-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 15:52:51-debug: refresh db internal success
2025-9-5 15:52:51-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\EventGroup.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 15:52:51-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\actions\BulletEventActions.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 15:52:51-debug: refresh db assets success
2025-9-5 15:52:51-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 15:52:51-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 15:52:51-debug: asset-db:refresh-all-database (133ms)
2025-9-5 15:53:46-debug: refresh db internal success
2025-9-5 15:53:46-debug: refresh db assets success
2025-9-5 15:53:46-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 15:53:46-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 15:53:46-debug: asset-db:refresh-all-database (101ms)
2025-9-5 15:53:46-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-5 15:53:46-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-5 15:54:11-debug: refresh db internal success
2025-9-5 15:54:11-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\EventGroup.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 15:54:11-debug: refresh db assets success
2025-9-5 15:54:11-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 15:54:11-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 15:54:11-debug: asset-db:refresh-all-database (141ms)
2025-9-5 15:54:11-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 15:54:11-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 15:54:28-debug: refresh db internal success
2025-9-5 15:54:28-debug: refresh db assets success
2025-9-5 15:54:28-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 15:54:28-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 15:54:28-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 15:54:28-debug: asset-db:refresh-all-database (102ms)
2025-9-5 15:54:28-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 15:56:48-debug: refresh db internal success
2025-9-5 15:56:48-debug: refresh db assets success
2025-9-5 15:56:48-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 15:56:48-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 15:56:48-debug: asset-db:refresh-all-database (119ms)
2025-9-5 15:56:48-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 15:56:48-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 15:57:44-debug: refresh db internal success
2025-9-5 15:57:44-debug: refresh db assets success
2025-9-5 15:57:44-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 15:57:44-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 15:57:44-debug: asset-db:refresh-all-database (116ms)
2025-9-5 15:58:33-debug: refresh db internal success
2025-9-5 15:58:33-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\BulletSystem.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 15:58:33-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 15:58:33-debug: refresh db assets success
2025-9-5 15:58:33-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 15:58:33-debug: asset-db:refresh-all-database (102ms)
2025-9-5 15:59:48-debug: refresh db internal success
2025-9-5 15:59:48-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\BulletSystem.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 15:59:48-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\EventGroup.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 15:59:48-debug: refresh db assets success
2025-9-5 15:59:48-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 15:59:48-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 15:59:48-debug: asset-db:refresh-all-database (144ms)
2025-9-5 15:59:48-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 15:59:48-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 16:00:56-debug: refresh db internal success
2025-9-5 16:00:57-debug: refresh db assets success
2025-9-5 16:00:57-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 16:00:57-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 16:00:57-debug: asset-db:refresh-all-database (98ms)
2025-9-5 16:00:59-debug: refresh db internal success
2025-9-5 16:00:59-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 16:00:59-debug: refresh db assets success
2025-9-5 16:00:59-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 16:00:59-debug: asset-db:refresh-all-database (96ms)
2025-9-5 16:00:59-debug: asset-db:worker-effect-data-processing (-1ms)
2025-9-5 16:00:59-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 16:01:11-debug: refresh db internal success
2025-9-5 16:01:11-debug: refresh db assets success
2025-9-5 16:01:11-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 16:01:11-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 16:01:11-debug: asset-db:refresh-all-database (101ms)
2025-9-5 16:01:11-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-5 16:01:11-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-5 16:01:13-debug: refresh db internal success
2025-9-5 16:01:13-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 16:01:13-debug: refresh db assets success
2025-9-5 16:01:13-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 16:01:13-debug: asset-db:refresh-all-database (99ms)
2025-9-5 16:02:36-debug: refresh db internal success
2025-9-5 16:02:36-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 16:02:36-debug: refresh db assets success
2025-9-5 16:02:36-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 16:02:36-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 16:02:36-debug: asset-db:refresh-all-database (131ms)
2025-9-5 16:02:36-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 16:07:12-debug: refresh db internal success
2025-9-5 16:07:12-debug: refresh db assets success
2025-9-5 16:07:12-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 16:07:12-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 16:07:12-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 16:07:12-debug: asset-db:refresh-all-database (101ms)
2025-9-5 16:07:12-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 16:31:10-debug: refresh db internal success
2025-9-5 16:31:10-debug: refresh db assets success
2025-9-5 16:31:10-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 16:31:10-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 16:31:10-debug: asset-db:refresh-all-database (118ms)
2025-9-5 16:31:10-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 16:33:55-debug: refresh db internal success
2025-9-5 16:33:55-debug: refresh db assets success
2025-9-5 16:33:55-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 16:33:55-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 16:33:55-debug: asset-db:refresh-all-database (131ms)
2025-9-5 16:33:55-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 16:33:55-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 16:40:01-debug: refresh db internal success
2025-9-5 16:40:01-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 16:40:01-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\Plane.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 16:40:01-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\weapon\WeaponSlots.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 16:40:01-debug: refresh db assets success
2025-9-5 16:40:01-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 16:40:01-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 16:40:01-debug: asset-db:refresh-all-database (123ms)
2025-9-5 16:40:01-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 16:40:01-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 16:40:18-debug: refresh db internal success
2025-9-5 16:40:18-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 16:40:18-debug: refresh db assets success
2025-9-5 16:40:18-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 16:40:18-debug: asset-db:refresh-all-database (120ms)
2025-9-5 16:40:21-debug: refresh db internal success
2025-9-5 16:40:21-debug: refresh db assets success
2025-9-5 16:40:21-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 16:40:21-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 16:40:21-debug: asset-db:refresh-all-database (118ms)
2025-9-5 16:40:21-debug: asset-db:worker-effect-data-processing (-1ms)
2025-9-5 17:17:40-debug: refresh db internal success
2025-9-5 17:17:40-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\Plane.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 17:17:40-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\weapon\Weapon.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 17:17:40-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\weapon\WeaponSlots.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 17:17:40-debug: refresh db assets success
2025-9-5 17:17:40-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 17:17:40-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 17:17:40-debug: asset-db:refresh-all-database (146ms)
2025-9-5 17:17:40-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 17:17:40-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 17:18:34-debug: Query all assets info in project
2025-9-5 17:18:34-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-5 17:18:34-debug: Skip compress image, progress: 0%
2025-9-5 17:18:34-debug: Init all bundles start..., progress: 0%
2025-9-5 17:18:34-debug: // ---- build task 查询 Asset Bundle ----
2025-9-5 17:18:34-debug: Num of bundles: 3..., progress: 0%
2025-9-5 17:18:34-debug: 查询 Asset Bundle start, progress: 0%
2025-9-5 17:18:34-debug: Init bundle root assets start..., progress: 0%
2025-9-5 17:18:34-debug:   Number of all scenes: 8
2025-9-5 17:18:34-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-5 17:18:34-debug: Init bundle root assets success..., progress: 0%
2025-9-5 17:18:34-debug:   Number of all scripts: 246
2025-9-5 17:18:34-debug:   Number of other assets: 1914
2025-9-5 17:18:34-debug: // ---- build task 查询 Asset Bundle ---- (20ms)
2025-9-5 17:18:34-debug: [Build Memory track]: 查询 Asset Bundle start:214.73MB, end 214.29MB, increase: -444.11KB
2025-9-5 17:18:34-debug: 查询 Asset Bundle start, progress: 5%
2025-9-5 17:18:34-log: run build task 查询 Asset Bundle success in 20 ms√, progress: 5%
2025-9-5 17:18:34-debug: // ---- build task 查询 Asset Bundle ----
2025-9-5 17:18:34-log: run build task 查询 Asset Bundle success in 2 ms√, progress: 10%
2025-9-5 17:18:34-debug: [Build Memory track]: 查询 Asset Bundle start:214.32MB, end 214.57MB, increase: 255.36KB
2025-9-5 17:18:34-debug: // ---- build task 查询 Asset Bundle ---- (2ms)
2025-9-5 17:18:34-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-5 17:18:34-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-5 17:18:34-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-9-5 17:18:34-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-9-5 17:18:34-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:214.60MB, end 214.62MB, increase: 25.82KB
2025-9-5 17:18:34-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-5 17:18:34-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-5 17:18:34-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-9-5 17:18:34-debug: [Build Memory track]: 填充脚本数据到 settings.json start:214.65MB, end 214.68MB, increase: 25.79KB
2025-9-5 17:18:34-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-9-5 17:18:34-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-5 17:18:34-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-5 17:18:34-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (4ms)
2025-9-5 17:18:34-log: run build task 整理部分构建选项内数据到 settings.json success in 4 ms√, progress: 15%
2025-9-5 17:18:34-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:214.71MB, end 214.28MB, increase: -438.41KB
2025-9-5 17:27:19-debug: refresh db internal success
2025-9-5 17:27:19-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 17:27:19-debug: refresh db assets success
2025-9-5 17:27:19-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 17:27:19-debug: asset-db:refresh-all-database (121ms)
2025-9-5 17:40:49-debug: refresh db internal success
2025-9-5 17:40:49-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 17:40:49-debug: refresh db assets success
2025-9-5 17:40:49-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 17:40:49-debug: asset-db:refresh-all-database (121ms)
2025-9-5 17:40:49-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 17:40:49-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 17:47:54-debug: refresh db internal success
2025-9-5 17:47:54-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\move\Movable.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 17:47:54-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\weapon\Weapon.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 17:47:54-debug: refresh db assets success
2025-9-5 17:47:54-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 17:47:54-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 17:47:54-debug: asset-db:refresh-all-database (139ms)
2025-9-5 18:00:55-debug: Query all assets info in project
2025-9-5 18:00:55-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-5 18:00:55-debug: Skip compress image, progress: 0%
2025-9-5 18:00:55-debug: Init all bundles start..., progress: 0%
2025-9-5 18:00:55-debug: Num of bundles: 3..., progress: 0%
2025-9-5 18:00:55-debug: 查询 Asset Bundle start, progress: 0%
2025-9-5 18:00:55-debug: // ---- build task 查询 Asset Bundle ----
2025-9-5 18:00:55-debug: Init bundle root assets start..., progress: 0%
2025-9-5 18:00:55-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-5 18:00:55-debug:   Number of all scenes: 8
2025-9-5 18:00:55-debug:   Number of other assets: 1914
2025-9-5 18:00:55-debug: Init bundle root assets success..., progress: 0%
2025-9-5 18:00:55-debug:   Number of all scripts: 246
2025-9-5 18:00:55-debug: // ---- build task 查询 Asset Bundle ---- (22ms)
2025-9-5 18:00:55-log: run build task 查询 Asset Bundle success in 22 ms√, progress: 5%
2025-9-5 18:00:55-debug: [Build Memory track]: 查询 Asset Bundle start:223.92MB, end 224.91MB, increase: 1013.80KB
2025-9-5 18:00:55-debug: 查询 Asset Bundle start, progress: 5%
2025-9-5 18:00:55-debug: // ---- build task 查询 Asset Bundle ----
2025-9-5 18:00:55-debug: // ---- build task 查询 Asset Bundle ---- (1ms)
2025-9-5 18:00:55-debug: [Build Memory track]: 查询 Asset Bundle start:224.94MB, end 224.58MB, increase: -366.76KB
2025-9-5 18:00:55-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-5 18:00:55-log: run build task 查询 Asset Bundle success in 1 ms√, progress: 10%
2025-9-5 18:00:55-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-5 18:00:55-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:224.61MB, end 224.65MB, increase: 42.22KB
2025-9-5 18:00:55-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-9-5 18:00:55-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-5 18:00:55-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-5 18:00:55-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-9-5 18:00:55-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-5 18:00:55-debug: [Build Memory track]: 填充脚本数据到 settings.json start:224.68MB, end 224.73MB, increase: 48.98KB
2025-9-5 18:00:55-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-5 18:00:55-debug: refresh db internal success
2025-9-5 18:00:55-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (43ms)
2025-9-5 18:00:55-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:224.76MB, end 226.22MB, increase: 1.46MB
2025-9-5 18:00:55-log: run build task 整理部分构建选项内数据到 settings.json success in 43 ms√, progress: 15%
2025-9-5 18:00:55-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\weapon\WeaponSlots.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:00:55-debug: refresh db assets success
2025-9-5 18:00:55-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 18:00:55-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 18:00:55-debug: asset-db:refresh-all-database (186ms)
2025-9-5 18:00:55-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 18:00:55-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 18:02:45-log: 资源数据库已锁定，资源操作(bound _refreshAsset)将会延迟响应，请稍侯
2025-9-5 18:02:45-debug: refresh db internal success
2025-9-5 18:02:45-debug: %cImport%c: E:\M2Game\Client\assets\scenes
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:02:45-debug: %cImport%c: E:\M2Game\Client\assets\scenes\PlaneEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:02:45-debug: refresh db assets success
2025-9-5 18:02:45-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 18:02:45-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 18:02:45-debug: start refresh asset from E:\M2Game\Client\assets\scenes\PlaneEditor.scene...
2025-9-5 18:02:45-debug: asset-db:refresh-all-database (106ms)
2025-9-5 18:02:45-debug: refresh asset E:\M2Game\Client\assets\scenes success
2025-9-5 18:02:45-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-5 18:02:45-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-5 18:04:15-debug: refresh db internal success
2025-9-5 18:04:15-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\weapon\Weapon.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:04:15-debug: refresh db assets success
2025-9-5 18:04:15-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 18:04:15-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 18:04:15-debug: asset-db:refresh-all-database (128ms)
2025-9-5 18:04:38-debug: Query all assets info in project
2025-9-5 18:04:38-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-5 18:04:38-debug: Skip compress image, progress: 0%
2025-9-5 18:04:38-debug: Init all bundles start..., progress: 0%
2025-9-5 18:04:38-debug: Num of bundles: 3..., progress: 0%
2025-9-5 18:04:38-debug: 查询 Asset Bundle start, progress: 0%
2025-9-5 18:04:38-debug: // ---- build task 查询 Asset Bundle ----
2025-9-5 18:04:38-debug: Init bundle root assets start..., progress: 0%
2025-9-5 18:04:38-debug:   Number of all scripts: 246
2025-9-5 18:04:38-debug:   Number of all scenes: 9
2025-9-5 18:04:38-debug: Init bundle root assets success..., progress: 0%
2025-9-5 18:04:38-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-5 18:04:38-debug:   Number of other assets: 1914
2025-9-5 18:04:38-debug: // ---- build task 查询 Asset Bundle ---- (19ms)
2025-9-5 18:04:38-log: run build task 查询 Asset Bundle success in 19 ms√, progress: 5%
2025-9-5 18:04:38-debug: [Build Memory track]: 查询 Asset Bundle start:211.72MB, end 212.18MB, increase: 470.04KB
2025-9-5 18:04:38-debug: 查询 Asset Bundle start, progress: 5%
2025-9-5 18:04:38-debug: // ---- build task 查询 Asset Bundle ----
2025-9-5 18:04:38-debug: // ---- build task 查询 Asset Bundle ---- (1ms)
2025-9-5 18:04:38-log: run build task 查询 Asset Bundle success in 1 ms√, progress: 10%
2025-9-5 18:04:38-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-5 18:04:38-debug: [Build Memory track]: 查询 Asset Bundle start:212.20MB, end 212.45MB, increase: 256.06KB
2025-9-5 18:04:38-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-5 18:04:38-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-5 18:04:38-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-9-5 18:04:38-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-5 18:04:38-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:212.48MB, end 212.50MB, increase: 16.13KB
2025-9-5 18:04:38-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-5 18:04:38-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-9-5 18:04:38-debug: [Build Memory track]: 填充脚本数据到 settings.json start:212.53MB, end 212.54MB, increase: 16.68KB
2025-9-5 18:04:38-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-5 18:04:38-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 15%
2025-9-5 18:04:38-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:212.57MB, end 212.85MB, increase: 283.00KB
2025-9-5 18:04:51-debug: refresh db internal success
2025-9-5 18:04:51-debug: refresh db assets success
2025-9-5 18:04:51-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 18:04:51-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 18:04:51-debug: asset-db:refresh-all-database (103ms)
2025-9-5 18:05:32-debug: refresh db internal success
2025-9-5 18:05:32-debug: refresh db assets success
2025-9-5 18:05:32-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 18:05:32-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 18:05:32-debug: asset-db:refresh-all-database (99ms)
2025-9-5 18:05:32-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 18:05:32-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 18:15:24-debug: refresh db internal success
2025-9-5 18:15:24-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\PlaneWithWeapon.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:15:24-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\plane
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:15:24-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\weapon\WeaponSlots.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:15:24-debug: refresh db assets success
2025-9-5 18:15:24-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 18:15:24-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 18:15:24-debug: asset-db:refresh-all-database (146ms)
2025-9-5 18:15:24-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-5 18:15:24-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-5 18:18:37-debug: refresh db internal success
2025-9-5 18:18:37-debug: refresh db assets success
2025-9-5 18:18:37-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 18:18:37-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 18:18:37-debug: asset-db:refresh-all-database (119ms)
2025-9-5 18:18:43-debug: refresh db internal success
2025-9-5 18:18:43-debug: refresh db assets success
2025-9-5 18:18:43-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 18:18:43-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 18:18:43-debug: asset-db:refresh-all-database (138ms)
2025-9-5 18:18:43-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 18:18:43-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 18:21:39-debug: refresh db internal success
2025-9-5 18:21:39-debug: refresh db assets success
2025-9-5 18:21:39-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 18:21:39-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 18:21:39-debug: asset-db:refresh-all-database (123ms)
2025-9-5 18:21:39-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 18:21:39-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 18:28:51-debug: refresh db internal success
2025-9-5 18:28:52-debug: refresh db assets success
2025-9-5 18:28:52-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 18:28:52-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 18:28:52-debug: asset-db:refresh-all-database (124ms)
2025-9-5 18:28:52-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 18:32:15-debug: refresh db internal success
2025-9-5 18:32:15-debug: refresh db assets success
2025-9-5 18:32:15-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 18:32:15-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 18:32:15-debug: asset-db:refresh-all-database (127ms)
2025-9-5 18:32:22-debug: refresh db internal success
2025-9-5 18:32:22-debug: refresh db assets success
2025-9-5 18:32:22-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 18:32:22-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 18:32:22-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 18:32:22-debug: asset-db:refresh-all-database (120ms)
2025-9-5 18:32:22-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 18:44:48-debug: refresh db internal success
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\ui\gm
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\ui\main\dialogue
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\ui\main\mail
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\ui\main\fight
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\ui\main\friend
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\ui\main\plane
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\ui\main\pk
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\ui\main\dialogue
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\ui\main\fight
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\ui\main\friend
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\ui\main\mail
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\ui\main\BattleUI.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\ui\main\BottomUI.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\ui\main\BuidingUI.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\ui\main\pk
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\ui\main\BuildingInfoUI.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\ui\main\MapModeUI.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\ui\main\PlaneShowUI.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\ui\main\PopupUI.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\ui\main\ShopUI.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\ui\main\SkyIslandUI.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\ui\main\TopUI.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\ui\main\WheelSpinnerUI.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\ui\main\TalentUI.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\ui\main\dialogue\DialogueUI.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\ui\main\fight\RogueSelectIcon.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\ui\main\fight\RogueUI.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\ui\main\friend\FriendAddUI.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\ui\main\friend\FriendCellUI.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\ui\main\friend\FriendListUI.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\ui\main\friend\FriendStrangerUI.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\ui\main\mail\MailUI.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\ui\main\friend\FriendUI.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\ui\main\mail\MailCellUI.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\ui\main\pk\PKHistoryCellUI.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\ui\main\pk\PKHistoryUI.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\ui\main\pk\PKRewardIcon.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\ui\main\pk\PKUI.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\ui\gm\GmButtonUI.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\ui\gm\GmUI.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\ui\main\BattleUI.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\ui\main\BottomUI.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\ui\main\Building.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\ui\main\BuildingInfoUI.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\ui\main\ItemIcon.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\ui\main\MapModeUI.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\ui\main\PlaneShowUI.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\ui\main\PopupUI.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\ui\main\ShopUI.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\ui\main\SkyIslandUI.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\ui\main\TalentUI.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\ui\main\TopUI.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\ui\gm\img\gm_dw_mark.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\ui\main\dialogue\boy.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\ui\main\dialogue\DialogueUI.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\ui\main\dialogue\girl.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\ui\gm\img\gm_dw_mark.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\ui\main\fight\RogueAbilityIcon.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\ui\main\dialogue\boy.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\ui\main\fight\RogueSelectIcon.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\ui\main\dialogue\girl.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\ui\gm\img\gm_dw_mark.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\ui\main\dialogue\boy.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\ui\main\fight\RogueUI.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\ui\main\friend\FriendAddUI.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\ui\main\dialogue\girl.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\ui\main\friend\FriendCellUI.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\ui\main\friend\FriendListUI.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\ui\main\friend\FriendStrangerUI.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\ui\main\friend\FriendUI.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\ui\main\friend\icon.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\ui\main\friend\leidian.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\ui\main\mail\MailCellUI.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\ui\main\mail\MailUI.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\ui\main\friend\icon.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\ui\main\friend\leidian.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\ui\main\pk\PKHistoryCellUI.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\ui\main\pk\PKHistoryUI.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\ui\main\friend\icon.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\ui\main\friend\leidian.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\ui\main\pk\PKRewardIcon.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\ui\main\pk\PKUI.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\ui\main\plane\BagItem.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\ui\main\plane\PlaneCombineResultUI.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\ui\main\plane\PlaneEquipInfoUI.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\ui\main\plane\PlaneUI.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\main
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\main_shop
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\main_plane
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\main_skyisland
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\main\audio
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\main_talent
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\main\prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\main\script
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\main\texture
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\main_plane\script
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\main_plane\prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\main_plane\audio
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\main_plane\texture
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\main_shop\audio
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\main_shop\prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\main_shop\texture
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\main_skyisland\audio
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\main_shop\script
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\main_skyisland\prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\main_skyisland\texture
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\main_talent\audio
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\main_skyisland\script
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\main_talent\prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\main_talent\script
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\gm\script\data
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\main_talent\texture
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\main\prefab\ui
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\main\script\ui
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\main_plane\prefab\ui
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\main_plane\script\ui
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\main_plane\script\module
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\main_shop\script\data
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\main_shop\prefab\ui
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\main_shop\script\ui
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\main_skyisland\prefab\ui
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\main_skyisland\script\data
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\main_talent\prefab\ui
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\main_skyisland\script\ui
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\main_talent\script\data
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\main_talent\script\ui
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\common\script\ui\CommonEntiry.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\ui\main\ToastUI.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\main_plane\script\module\PlaneEvent.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\main_plane\script\module\PlaneTypes.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\main_plane\script\ui\PlaneCombineResultUI.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\main_plane\script\ui\PlaneEquipInfoUI.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\main_plane\script\ui\components
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\main_plane\script\ui\PlaneUI.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\main_plane\script\ui\components\back_pack
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\main_plane\script\ui\components\display
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\main_plane\script\ui\components\back_pack\BagGrid.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\main_plane\script\ui\components\back_pack\BagItem.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\main_plane\script\ui\components\back_pack\SortTypeDropdown.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\main_plane\script\ui\components\back_pack\Tabs.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\main_plane\script\ui\components\display\EquipDisplay.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\main_plane\script\ui\components\display\CombineDisplay.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\ui\main\ToastUI.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_plane
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_skyisland
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_talent
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_shop
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\audio
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\prefab
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\script
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_plane\audio
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_plane\prefab
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_shop\audio
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_plane\script
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_plane\texture
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_shop\prefab
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_shop\script
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_skyisland\audio
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_skyisland\prefab
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_shop\texture
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_skyisland\script
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_skyisland\texture
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_talent\audio
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_talent\prefab
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_talent\texture
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_talent\script
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\CommonEntry.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\script\event
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\gm\script\GmEntry.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\script\HomeEntry.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\prefab\ui
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\script\ui
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\script\type
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_plane\script\HomePlaneEntry.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_plane\prefab\ui
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_plane\script\module
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_plane\script\ui
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_shop\prefab\ui
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_shop\script\data
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_skyisland\prefab\ui
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_shop\script\ui
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_skyisland\script\data
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_skyisland\script\ui
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_talent\script\ui
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_talent\script\data
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_talent\prefab\ui
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\prefab\ui\dialogue
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\prefab\ui\fight
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\prefab\ui\friend
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\prefab\ui\pk
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\prefab\ui\mail
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\script\event\HomeUIEvent.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\script\type\BottomTab.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\script\ui\BuidingUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\script\ui\BuildingInfoUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\script\ui\BottomUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\script\ui\dialogue
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\script\ui\fight
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\script\ui\friend
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\script\ui\HomeUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\script\ui\pk
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\script\ui\MapModeUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\script\ui\mail
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\script\ui\PlaneShowUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\script\ui\PopupUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\script\ui\ShopUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\script\ui\SkyIslandUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\script\ui\TalentUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\script\ui\ToastUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\script\ui\TopUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\script\ui\WheelSpinnerUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_plane\script\module\PlaneTypes.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_plane\script\module\PlaneEvent.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_plane\script\ui\components
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_plane\script\ui\PlaneUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_plane\script\ui\PlaneCombineResultUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_plane\script\ui\PlaneEquipInfoUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\script\ui\dialogue\DialogueUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\script\ui\fight\RogueSelectIcon.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\script\ui\friend\FriendAddUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\script\ui\friend\FriendCellUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\script\ui\fight\RogueUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\script\ui\friend\FriendListUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\script\ui\friend\FriendStrangerUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\script\ui\friend\FriendUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\script\ui\mail\MailCellUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\script\ui\pk\PKHistoryCellUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\script\ui\mail\MailUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\script\ui\pk\PKHistoryUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\script\ui\pk\PKRewardIcon.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_plane\script\ui\components\back_pack
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\script\ui\pk\PKUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_plane\script\ui\components\display
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_plane\script\ui\components\back_pack\BagGrid.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_plane\script\ui\components\back_pack\BagItem.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_plane\script\ui\components\back_pack\SortTypeDropdown.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_plane\script\ui\components\back_pack\Tabs.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_plane\script\ui\components\display\CombineDisplay.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_plane\script\ui\components\display\EquipDisplay.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\prefab\Entry.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\gm\prefab\Entry.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_plane\prefab\Entry.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\gm\texture\gm_dw_mark.png
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\prefab\Entry.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\gm\prefab\ui\GmButtonUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\gm\prefab\ui\GmUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\prefab\ui\BottomUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\prefab\ui\Building.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\prefab\ui\BuildingInfoUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\gm\texture\gm_dw_mark.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\prefab\ui\HomeUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\prefab\ui\MapModeUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\prefab\ui\ItemIcon.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\gm\texture\gm_dw_mark.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\prefab\ui\PlaneShowUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\prefab\ui\PopupUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\prefab\ui\ShopUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\prefab\ui\SkyIslandUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\prefab\ui\TalentUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\prefab\ui\ToastUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\prefab\ui\TopUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_plane\prefab\ui\BagItem.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_plane\prefab\ui\PlaneCombineResultUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_plane\prefab\ui\PlaneEquipInfoUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_plane\prefab\ui\PlaneUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\prefab\ui\dialogue\boy.png
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\prefab\ui\dialogue\DialogueUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\prefab\ui\dialogue\girl.png
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\prefab\ui\fight\RogueAbilityIcon.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\prefab\ui\fight\RogueSelectIcon.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\prefab\ui\fight\RogueUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\prefab\ui\friend\FriendAddUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\prefab\ui\dialogue\boy.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\prefab\ui\friend\FriendCellUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\prefab\ui\dialogue\boy.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\prefab\ui\friend\FriendListUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\prefab\ui\dialogue\girl.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\prefab\ui\friend\FriendStrangerUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\prefab\ui\dialogue\girl.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\prefab\ui\friend\FriendUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\prefab\ui\friend\icon.png
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\prefab\ui\friend\leidian.png
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\prefab\ui\mail\MailCellUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\prefab\ui\mail\MailUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\prefab\ui\pk\PKHistoryCellUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\prefab\ui\pk\PKHistoryUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\prefab\ui\friend\icon.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\prefab\ui\friend\leidian.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\prefab\ui\pk\PKRewardIcon.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\prefab\ui\friend\icon.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\prefab\ui\friend\leidian.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\prefab\ui\pk\PKUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:49-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:49-debug: %cImport%c: E:\M2Game\Client\assets\bundles\gm
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:49-debug: %cImport%c: E:\M2Game\Client\assets\bundles
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:49-debug: %cImport%c: E:\M2Game\Client\assets\bundles\Bundle.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:49-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\MainUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ResUpdate
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:49-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:49-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\prefab
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:49-debug: %cImport%c: E:\M2Game\Client\assets\bundles\gm\prefab
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:49-debug: %cImport%c: E:\M2Game\Client\assets\bundles\gm\script
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:49-debug: %cImport%c: E:\M2Game\Client\assets\bundles\gm\texture
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:49-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\gm
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:49-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\main
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ResUpdate\ResUpdate.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\main
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\DevLoginUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\UIMgr.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:49-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:49-debug: %cImport%c: E:\M2Game\Client\assets\bundles\gm\prefab\ui
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:49-debug: %cImport%c: E:\M2Game\Client\assets\bundles\gm\script\ui
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:49-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\prefabs\emitter
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:49-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\gm\img
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:49-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\main\WheelSpinnerUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\plane
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:49-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\data\equip
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:49-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\data\gm
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:49-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\TopBlockInputUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:49-debug: %cImport%c: E:\M2Game\Client\assets\bundles\gm\script\ui\GmButtonUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:49-debug: %cImport%c: E:\M2Game\Client\assets\bundles\gm\script\ui\GmUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:49-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\prefabs\emitter\EmitterExample.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\Plane.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\skill
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\PlaneWithWeapon.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:49-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\data\equip\EquipCombine.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:49-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\data\gm\GM.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\skill\BuffComp.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\weapon\WeaponSlots.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:49-debug: refresh db assets success
2025-9-5 18:44:49-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 18:44:49-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 18:44:49-debug: asset-db:refresh-all-database (369ms)
2025-9-5 18:44:49-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 18:44:49-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-5 18:44:55-debug: %cImport%c: E:\M2Game\Client\assets\scenes\PlaneEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:44:55-debug: asset-db:reimport-assetd90c1d4b-98f0-42eb-8111-dacc86636312 (3ms)
2025-9-5 18:44:55-debug: refresh db internal success
2025-9-5 18:44:55-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 18:44:55-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 18:44:55-debug: refresh db assets success
2025-9-5 18:44:55-debug: asset-db:refresh-all-database (103ms)
2025-9-5 18:44:55-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 18:47:50-debug: refresh db internal success
2025-9-5 18:47:50-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:47:50-debug: refresh db assets success
2025-9-5 18:47:50-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 18:47:50-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 18:47:50-debug: asset-db:refresh-all-database (133ms)
2025-9-5 18:49:03-debug: refresh db internal success
2025-9-5 18:49:03-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:49:03-debug: refresh db assets success
2025-9-5 18:49:03-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 18:49:03-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 18:49:03-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 18:49:03-debug: asset-db:refresh-all-database (135ms)
2025-9-5 18:49:03-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 18:51:57-debug: refresh db internal success
2025-9-5 18:51:57-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:51:57-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\BulletController.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:51:57-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 18:51:57-debug: refresh db assets success
2025-9-5 18:51:57-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 18:51:57-debug: asset-db:refresh-all-database (138ms)
2025-9-5 18:51:57-debug: asset-db:worker-effect-data-processing (3ms)
2025-9-5 18:51:57-debug: asset-db-hook-engine-extends-afterRefresh (3ms)
2025-9-5 18:53:37-debug: refresh db internal success
2025-9-5 18:53:37-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\BulletController.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:53:37-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\move\Movable.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:53:37-debug: refresh db assets success
2025-9-5 18:53:37-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 18:53:37-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 18:53:37-debug: asset-db:refresh-all-database (137ms)
2025-9-5 18:53:37-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 18:53:37-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 18:54:26-debug: refresh db internal success
2025-9-5 18:54:26-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\move\Movable.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:54:26-debug: refresh db assets success
2025-9-5 18:54:26-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 18:54:26-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 18:54:26-debug: asset-db:refresh-all-database (129ms)
2025-9-5 18:54:26-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 18:54:47-debug: refresh db internal success
2025-9-5 18:54:47-debug: refresh db assets success
2025-9-5 18:54:47-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 18:54:47-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 18:54:47-debug: asset-db:refresh-all-database (122ms)
2025-9-5 18:54:47-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 18:54:47-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 18:55:45-debug: refresh db internal success
2025-9-5 18:55:45-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\move\Movable.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:55:45-debug: refresh db assets success
2025-9-5 18:55:45-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 18:55:45-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 18:55:45-debug: asset-db:refresh-all-database (151ms)
2025-9-5 18:55:45-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 18:55:45-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 18:56:44-debug: refresh db internal success
2025-9-5 18:56:44-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\move\Movable.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:56:44-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 18:56:44-debug: refresh db assets success
2025-9-5 18:56:44-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 18:56:44-debug: asset-db:refresh-all-database (143ms)
2025-9-5 18:56:44-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 18:57:43-debug: refresh db internal success
2025-9-5 18:57:43-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\move\Movable.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:57:43-debug: refresh db assets success
2025-9-5 18:57:43-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 18:57:43-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 18:57:43-debug: asset-db:refresh-all-database (138ms)
2025-9-5 18:57:43-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 18:59:18-debug: refresh db internal success
2025-9-5 18:59:18-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\move\Movable.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 18:59:18-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 18:59:18-debug: refresh db assets success
2025-9-5 18:59:18-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 18:59:18-debug: asset-db:refresh-all-database (141ms)
2025-9-5 18:59:18-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 19:07:00-debug: refresh db internal success
2025-9-5 19:07:00-debug: refresh db assets success
2025-9-5 19:07:00-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 19:07:00-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 19:07:00-debug: asset-db:refresh-all-database (129ms)
2025-9-5 19:07:00-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 19:07:00-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-5 19:09:30-debug: refresh db internal success
2025-9-5 19:09:30-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\BulletController.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 19:09:30-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 19:09:30-debug: refresh db assets success
2025-9-5 19:09:30-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 19:09:30-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 19:09:30-debug: asset-db:refresh-all-database (133ms)
2025-9-5 19:10:45-debug: refresh db internal success
2025-9-5 19:10:45-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 19:10:45-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 19:10:45-debug: refresh db assets success
2025-9-5 19:10:45-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 19:10:45-debug: asset-db:refresh-all-database (134ms)
2025-9-5 19:11:29-debug: refresh db internal success
2025-9-5 19:11:29-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 19:11:29-debug: refresh db assets success
2025-9-5 19:11:29-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 19:11:29-debug: asset-db:refresh-all-database (129ms)
2025-9-5 19:11:29-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 19:11:34-debug: refresh db internal success
2025-9-5 19:11:34-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 19:11:34-debug: refresh db assets success
2025-9-5 19:11:34-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 19:11:34-debug: asset-db:refresh-all-database (138ms)
2025-9-5 19:11:34-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 19:11:46-debug: refresh db internal success
2025-9-5 19:11:46-debug: refresh db assets success
2025-9-5 19:11:46-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 19:11:46-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 19:11:46-debug: asset-db:refresh-all-database (99ms)
2025-9-5 19:11:46-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 19:11:46-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 19:12:20-debug: refresh db internal success
2025-9-5 19:12:20-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\move\Movable.ts
background: #aaff85; color: #000;
color: #000;
2025-9-5 19:12:20-debug: refresh db assets success
2025-9-5 19:12:20-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 19:12:20-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 19:12:20-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-5 19:12:20-debug: asset-db:refresh-all-database (142ms)
2025-9-5 19:12:20-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-5 19:13:38-debug: refresh db internal success
2025-9-5 19:13:39-debug: refresh db assets success
2025-9-5 19:13:39-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 19:13:39-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 19:13:39-debug: asset-db:refresh-all-database (105ms)
2025-9-5 19:13:42-debug: refresh db internal success
2025-9-5 19:13:42-debug: refresh db assets success
2025-9-5 19:13:42-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-5 19:13:42-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-5 19:13:42-debug: asset-db:refresh-all-database (97ms)
2025-9-6 10:00:09-debug: refresh db internal success
2025-9-6 10:00:09-debug: %cImport%c: E:\M2Game\Client\assets\editor\emitter\EmitterEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-6 10:00:09-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-9-6 10:00:09-debug: refresh db assets success
2025-9-6 10:00:09-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-6 10:00:09-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-6 10:00:09-debug: asset-db:refresh-all-database (110ms)
2025-9-6 10:00:09-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-6 10:00:09-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-6 10:00:30-debug: refresh db internal success
2025-9-6 10:00:30-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-9-6 10:00:30-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-6 10:00:30-debug: refresh db assets success
2025-9-6 10:00:30-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-6 10:00:30-debug: asset-db:refresh-all-database (150ms)
2025-9-6 10:00:30-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-6 10:00:30-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-6 10:01:30-debug: refresh db internal success
2025-9-6 10:01:30-debug: refresh db assets success
2025-9-6 10:01:30-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-6 10:01:30-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-6 10:01:30-debug: asset-db:refresh-all-database (140ms)
2025-9-6 10:01:30-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-6 10:01:30-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-6 10:02:33-debug: %cImport%c: E:\M2Game\Client\assets\scenes\BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-6 10:02:33-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (3ms)
2025-9-6 10:02:37-debug: Query all assets info in project
2025-9-6 10:02:37-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-6 10:02:37-debug: Skip compress image, progress: 0%
2025-9-6 10:02:37-debug: Init all bundles start..., progress: 0%
2025-9-6 10:02:37-debug: // ---- build task 查询 Asset Bundle ----
2025-9-6 10:02:37-debug: 查询 Asset Bundle start, progress: 0%
2025-9-6 10:02:37-debug: Num of bundles: 7..., progress: 0%
2025-9-6 10:02:37-debug: Init bundle root assets start..., progress: 0%
2025-9-6 10:02:37-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-6 10:02:37-debug:   Number of other assets: 1918
2025-9-6 10:02:37-debug: Init bundle root assets success..., progress: 0%
2025-9-6 10:02:37-debug:   Number of all scripts: 252
2025-9-6 10:02:37-debug:   Number of all scenes: 9
2025-9-6 10:02:37-log: run build task 查询 Asset Bundle success in 22 ms√, progress: 5%
2025-9-6 10:02:37-debug: // ---- build task 查询 Asset Bundle ---- (22ms)
2025-9-6 10:02:37-debug: [Build Memory track]: 查询 Asset Bundle start:209.27MB, end 206.38MB, increase: -2953.72KB
2025-9-6 10:02:37-debug: 查询 Asset Bundle start, progress: 5%
2025-9-6 10:02:37-debug: // ---- build task 查询 Asset Bundle ----
2025-9-6 10:02:37-debug: // ---- build task 查询 Asset Bundle ---- (3ms)
2025-9-6 10:02:37-log: run build task 查询 Asset Bundle success in 3 ms√, progress: 10%
2025-9-6 10:02:37-debug: [Build Memory track]: 查询 Asset Bundle start:206.41MB, end 205.89MB, increase: -531.41KB
2025-9-6 10:02:37-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-6 10:02:37-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-6 10:02:37-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:205.92MB, end 205.94MB, increase: 16.39KB
2025-9-6 10:02:37-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-6 10:02:37-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-9-6 10:02:37-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-6 10:02:37-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-6 10:02:37-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-9-6 10:02:37-debug: [Build Memory track]: 填充脚本数据到 settings.json start:205.97MB, end 205.98MB, increase: 16.39KB
2025-9-6 10:02:37-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-6 10:02:37-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-9-6 10:02:37-log: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 15%
2025-9-6 10:02:37-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:206.01MB, end 206.30MB, increase: 295.35KB
2025-9-6 10:03:40-debug: refresh db internal success
2025-9-6 10:03:40-debug: refresh db assets success
2025-9-6 10:03:40-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-6 10:03:40-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-6 10:03:40-debug: asset-db:refresh-all-database (103ms)
2025-9-6 10:04:00-debug: refresh db internal success
2025-9-6 10:04:00-debug: refresh db assets success
2025-9-6 10:04:00-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-6 10:04:00-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-6 10:04:00-debug: asset-db:refresh-all-database (98ms)
2025-9-6 10:12:31-debug: refresh db internal success
2025-9-6 10:12:31-debug: refresh db assets success
2025-9-6 10:12:31-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-6 10:12:31-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-6 10:12:31-debug: asset-db:refresh-all-database (102ms)
2025-9-6 10:54:36-debug: refresh db internal success
2025-9-6 10:54:36-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\data\bullet\EventConditionType.ts
background: #aaff85; color: #000;
color: #000;
2025-9-6 10:54:36-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\data\bullet\EventGroupData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-6 10:54:36-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\PlaneWithWeapon.ts
background: #aaff85; color: #000;
color: #000;
2025-9-6 10:54:36-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\weapon\Weapon.ts
background: #aaff85; color: #000;
color: #000;
2025-9-6 10:54:36-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\weapon\WeaponSlots.ts
background: #aaff85; color: #000;
color: #000;
2025-9-6 10:54:36-debug: refresh db assets success
2025-9-6 10:54:36-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-6 10:54:36-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-6 10:54:36-debug: asset-db:refresh-all-database (167ms)
2025-9-6 10:54:36-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-6 10:55:03-debug: refresh db internal success
2025-9-6 10:55:03-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\data\bullet\EventGroupData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-6 10:55:03-debug: refresh db assets success
2025-9-6 10:55:03-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-6 10:55:03-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-6 10:55:03-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-6 10:55:03-debug: asset-db:refresh-all-database (122ms)
2025-9-6 10:55:03-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-6 10:55:13-debug: refresh db internal success
2025-9-6 10:55:13-debug: refresh db assets success
2025-9-6 10:55:13-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-6 10:55:13-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-6 10:55:13-debug: asset-db:refresh-all-database (97ms)
2025-9-6 11:11:29-debug: refresh db internal success
2025-9-6 11:11:29-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\EventGroup.ts
background: #aaff85; color: #000;
color: #000;
2025-9-6 11:11:29-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\data\bullet\EventConditionType.ts
background: #aaff85; color: #000;
color: #000;
2025-9-6 11:11:29-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\data\bullet\EventActionType.ts
background: #aaff85; color: #000;
color: #000;
2025-9-6 11:11:29-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\data\bullet\EventGroupData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-6 11:11:29-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\PlaneWithWeapon.ts
background: #aaff85; color: #000;
color: #000;
2025-9-6 11:11:29-debug: refresh db assets success
2025-9-6 11:11:29-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-6 11:11:29-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-6 11:11:29-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-6 11:11:29-debug: asset-db:refresh-all-database (155ms)
2025-9-6 11:11:29-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-6 11:11:59-debug: refresh db internal success
2025-9-6 11:11:59-debug: refresh db assets success
2025-9-6 11:11:59-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-6 11:11:59-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-6 11:11:59-debug: asset-db:refresh-all-database (124ms)
2025-9-6 11:11:59-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-6 11:11:59-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-6 11:12:01-debug: refresh db internal success
2025-9-6 11:12:01-debug: refresh db assets success
2025-9-6 11:12:01-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-6 11:12:01-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-6 11:12:01-debug: asset-db:refresh-all-database (138ms)
2025-9-6 11:12:01-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-6 11:12:01-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-6 11:12:20-debug: refresh db internal success
2025-9-6 11:12:20-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-9-6 11:12:20-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\data\bullet\EventActionType.ts
background: #aaff85; color: #000;
color: #000;
2025-9-6 11:12:20-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-6 11:12:20-debug: refresh db assets success
2025-9-6 11:12:20-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-6 11:12:20-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-6 11:12:20-debug: asset-db:refresh-all-database (132ms)
2025-9-6 11:12:20-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-6 11:15:12-debug: refresh db internal success
2025-9-6 11:15:12-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-6 11:15:12-debug: refresh db assets success
2025-9-6 11:15:12-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-6 11:15:12-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-6 11:15:12-debug: asset-db:refresh-all-database (151ms)
2025-9-6 11:15:12-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-6 11:15:32-debug: refresh db internal success
2025-9-6 11:15:32-debug: refresh db assets success
2025-9-6 11:15:32-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-6 11:15:32-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-6 11:15:32-debug: asset-db:refresh-all-database (142ms)
2025-9-6 11:15:32-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-6 11:15:32-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-6 11:16:01-debug: refresh db internal success
2025-9-6 11:16:01-debug: refresh db assets success
2025-9-6 11:16:01-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-6 11:16:01-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-6 11:16:01-debug: asset-db:refresh-all-database (99ms)
2025-9-6 11:16:01-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-6 11:16:27-debug: refresh db internal success
2025-9-6 11:16:27-debug: refresh db assets success
2025-9-6 11:16:27-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-6 11:16:27-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-6 11:16:27-debug: asset-db:refresh-all-database (99ms)
2025-9-6 11:18:29-debug: refresh db internal success
2025-9-6 11:18:29-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\BulletController.ts
background: #aaff85; color: #000;
color: #000;
2025-9-6 11:18:29-debug: refresh db assets success
2025-9-6 11:18:29-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-6 11:18:29-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-6 11:18:29-debug: asset-db:refresh-all-database (126ms)
2025-9-6 11:23:34-debug: refresh db internal success
2025-9-6 11:23:34-debug: %cImport%c: E:\M2Game\Client\assets\editor\level
background: #aaff85; color: #000;
color: #000;
2025-9-6 11:23:34-debug: %cImport%c: E:\M2Game\Client\assets\editor\emitter\EmitterEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-6 11:23:34-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-6 11:23:34-debug: refresh db assets success
2025-9-6 11:23:34-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-6 11:23:34-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-6 11:23:34-debug: asset-db:refresh-all-database (169ms)
2025-9-6 11:26:50-debug: refresh db internal success
2025-9-6 11:26:50-debug: %cImport%c: E:\M2Game\Client\assets\editor\emitter\EmitterEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-6 11:26:50-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\BulletSystem.ts
background: #aaff85; color: #000;
color: #000;
2025-9-6 11:26:50-debug: refresh db assets success
2025-9-6 11:26:50-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-6 11:26:50-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-6 11:26:50-debug: asset-db:refresh-all-database (142ms)
2025-9-6 11:26:50-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-6 11:26:50-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-6 11:27:27-debug: refresh db internal success
2025-9-6 11:27:27-debug: %cImport%c: E:\M2Game\Client\assets\editor\emitter\EmitterEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-6 11:27:27-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-6 11:27:27-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-6 11:27:27-debug: refresh db assets success
2025-9-6 11:27:27-debug: asset-db:refresh-all-database (139ms)
2025-9-6 11:27:27-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-6 11:30:51-debug: refresh db internal success
2025-9-6 11:30:51-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\prefabs\emitter
background: #aaff85; color: #000;
color: #000;
2025-9-6 11:30:51-log: 资源数据库已锁定，资源操作(bound _refreshAsset)将会延迟响应，请稍侯
2025-9-6 11:30:51-debug: refresh db assets success
2025-9-6 11:30:51-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-6 11:30:51-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-6 11:30:51-debug: start refresh asset from E:\M2Game\Client\assets\resources\Game\prefabs\emitter\Tesst.prefab...
2025-9-6 11:30:51-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\prefabs\emitter\Tesst.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-6 11:30:51-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-6 11:30:51-debug: asset-db:refresh-all-database (111ms)
2025-9-6 11:30:51-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-6 11:30:51-debug: refresh asset E:\M2Game\Client\assets\resources\Game\prefabs\emitter success
2025-9-6 11:30:51-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\prefabs\emitter
background: #aaff85; color: #000;
color: #000;
2025-9-6 11:31:46-debug: refresh db internal success
2025-9-6 11:31:46-debug: %cImport%c: E:\M2Game\Client\assets\bundles
background: #aaff85; color: #000;
color: #000;
2025-9-6 11:31:46-debug: refresh db assets success
2025-9-6 11:31:46-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-6 11:31:46-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-6 11:31:46-debug: asset-db:refresh-all-database (125ms)
2025-9-6 11:31:46-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-6 11:31:46-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-6 11:33:04-debug: refresh db internal success
2025-9-6 11:33:04-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\Game\prefabs\emitter\test.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-6 11:33:04-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\Game\prefabs\emitter\EmitterExample_copy.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-6 11:33:04-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\Game\prefabs\emitter\Tesst.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-6 11:33:04-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\prefabs\emitter\TestBullet1.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-6 11:33:04-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\prefabs\emitter
background: #aaff85; color: #000;
color: #000;
2025-9-6 11:33:04-debug: refresh db assets success
2025-9-6 11:33:04-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-6 11:33:04-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-6 11:33:04-debug: asset-db:refresh-all-database (139ms)
2025-9-6 11:33:04-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-6 11:34:54-debug: refresh db internal success
2025-9-6 11:34:54-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\BulletSystem.ts
background: #aaff85; color: #000;
color: #000;
2025-9-6 11:34:54-debug: refresh db assets success
2025-9-6 11:34:54-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-6 11:34:54-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-6 11:34:54-debug: asset-db:refresh-all-database (138ms)
2025-9-6 11:34:54-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-6 11:34:54-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-6 12:07:12-debug: refresh db internal success
2025-9-6 12:07:12-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\BulletSystem.ts
background: #aaff85; color: #000;
color: #000;
2025-9-6 12:07:12-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\move\Movable.ts
background: #aaff85; color: #000;
color: #000;
2025-9-6 12:07:12-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\data\bullet\BulletData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-6 12:07:12-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\data\bullet\EmitterData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-6 12:07:12-debug: refresh db assets success
2025-9-6 12:07:12-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-6 12:07:12-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-6 12:07:12-debug: asset-db:refresh-all-database (143ms)
2025-9-6 12:07:35-debug: refresh db internal success
2025-9-6 12:07:35-debug: refresh db assets success
2025-9-6 12:07:35-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-6 12:07:35-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-6 12:07:35-debug: asset-db:refresh-all-database (102ms)
2025-9-6 12:10:52-debug: refresh db internal success
2025-9-6 12:10:52-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\BulletController.ts
background: #aaff85; color: #000;
color: #000;
2025-9-6 12:10:52-debug: refresh db assets success
2025-9-6 12:10:52-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-6 12:10:52-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-6 12:10:52-debug: asset-db:refresh-all-database (131ms)
2025-9-6 12:11:26-debug: %cImport%c: E:\M2Game\Client\assets\scenes\BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-6 12:11:26-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (3ms)
2025-9-6 12:13:01-debug: start refresh asset from E:\M2Game\Client\assets\resources\Game\prefabs\emitter\EmitterExample.prefab...
2025-9-6 12:13:01-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\prefabs\emitter\EmitterExample.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-6 12:13:01-debug: refresh asset E:\M2Game\Client\assets\resources\Game\prefabs\emitter success
2025-9-6 12:13:02-debug: refresh db internal success
2025-9-6 12:13:02-debug: refresh db assets success
2025-9-6 12:13:02-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-6 12:13:02-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-6 12:13:02-debug: asset-db:refresh-all-database (109ms)
2025-9-6 12:13:02-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-6 12:13:02-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-6 12:14:07-debug: refresh db internal success
2025-9-6 12:14:07-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\BulletController.ts
background: #aaff85; color: #000;
color: #000;
2025-9-6 12:14:07-debug: refresh db assets success
2025-9-6 12:14:07-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-6 12:14:07-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-6 12:14:07-debug: asset-db:refresh-all-database (132ms)
2025-9-6 12:14:07-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-6 12:14:07-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-6 12:15:27-debug: refresh db internal success
2025-9-6 12:15:27-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\BulletController.ts
background: #aaff85; color: #000;
color: #000;
2025-9-6 12:15:27-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\move\Movable.ts
background: #aaff85; color: #000;
color: #000;
2025-9-6 12:15:27-debug: refresh db assets success
2025-9-6 12:15:27-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-6 12:15:27-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-6 12:15:27-debug: asset-db:refresh-all-database (134ms)
2025-9-6 12:15:27-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-6 12:15:27-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-6 12:15:30-debug: refresh db internal success
2025-9-6 12:15:30-debug: refresh db assets success
2025-9-6 12:15:30-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-6 12:15:30-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-6 12:15:30-debug: asset-db:refresh-all-database (120ms)
2025-9-6 12:15:30-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-6 12:16:20-debug: refresh db internal success
2025-9-6 12:16:20-debug: refresh db assets success
2025-9-6 12:16:20-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-6 12:16:20-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-6 12:16:20-debug: asset-db:refresh-all-database (129ms)
2025-9-6 12:16:20-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-6 12:16:20-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-6 12:18:40-debug: refresh db internal success
2025-9-6 12:18:40-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-9-6 12:18:40-debug: refresh db assets success
2025-9-6 12:18:40-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-6 12:18:40-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-6 12:18:40-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-6 12:18:40-debug: asset-db:refresh-all-database (144ms)
2025-9-6 12:18:40-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-6 12:19:36-debug: refresh db internal success
2025-9-6 12:19:36-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\BulletController.ts
background: #aaff85; color: #000;
color: #000;
2025-9-6 12:19:36-debug: refresh db assets success
2025-9-6 12:19:36-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-6 12:19:36-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-6 12:19:36-debug: asset-db:refresh-all-database (133ms)
2025-9-6 17:58:27-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\weapon\WeaponSlots.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-6 17:58:27-debug: refresh db internal success
2025-9-6 17:58:27-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\BulletController.ts
background: #aaff85; color: #000;
color: #000;
2025-9-6 17:58:27-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\EventGroup.ts
background: #aaff85; color: #000;
color: #000;
2025-9-6 17:58:27-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-9-6 17:58:27-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\PropertyContainer.ts
background: #aaff85; color: #000;
color: #000;
2025-9-6 17:58:27-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\actions\BulletEventActions.ts
background: #aaff85; color: #000;
color: #000;
2025-9-6 17:58:27-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\actions\EmitterEventActions.ts
background: #aaff85; color: #000;
color: #000;
2025-9-6 17:58:27-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\conditions\BulletEventConditions.ts
background: #aaff85; color: #000;
color: #000;
2025-9-6 17:58:27-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\conditions\EmitterEventConditions.ts
background: #aaff85; color: #000;
color: #000;
2025-9-6 17:58:27-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\data\bullet\BulletData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-6 17:58:27-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\data\bullet\EventActionType.ts
background: #aaff85; color: #000;
color: #000;
2025-9-6 17:58:27-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\Plane.ts
background: #aaff85; color: #000;
color: #000;
2025-9-6 17:58:27-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\PlaneWithWeapon.ts
background: #aaff85; color: #000;
color: #000;
2025-9-6 17:58:27-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\weapon
background: #aaff85; color: #000;
color: #000;
2025-9-6 17:58:27-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\weapon\Weapon.ts
background: #aaff85; color: #000;
color: #000;
2025-9-6 17:58:27-debug: refresh db assets success
2025-9-6 17:58:27-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-6 17:58:27-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-6 17:58:27-debug: asset-db:refresh-all-database (169ms)
2025-9-6 17:58:27-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-6 17:58:27-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-6 17:59:30-debug: refresh db internal success
2025-9-6 17:59:30-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-9-6 17:59:30-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-6 17:59:30-debug: refresh db assets success
2025-9-6 17:59:30-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-6 17:59:30-debug: asset-db:refresh-all-database (141ms)
2025-9-6 18:00:22-debug: refresh db internal success
2025-9-6 18:00:22-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\BulletController.ts
background: #aaff85; color: #000;
color: #000;
2025-9-6 18:00:22-debug: refresh db assets success
2025-9-6 18:00:22-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-6 18:00:22-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-6 18:00:22-debug: asset-db:refresh-all-database (142ms)
2025-9-6 18:00:22-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-6 18:00:22-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-8 10:02:04-debug: refresh db internal success
2025-9-8 10:02:04-debug: refresh db assets success
2025-9-8 10:02:04-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-8 10:02:04-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-8 10:02:04-debug: asset-db:refresh-all-database (184ms)
2025-9-8 10:02:04-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-8 10:08:40-debug: refresh db internal success
2025-9-8 10:08:40-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\Game\const\AttributeConst.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\Game\manager\PlaneManager.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\Game\data\AttributeData.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\Game\manager\PrefabManager.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\Game\data\MainPlaneData.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\Game\ui\base\AngleComp.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\boss\BossCollider.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\Plane.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\boss\BossBase.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\boss\BossEntity.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\boss\BossHurt.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\boss\BossUnit.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\boss\BossUnitBase.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\enemy\EnemyAnim.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\enemy\EnemyAttrBaseCom.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\enemy\EnemyAttrComponent.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\enemy\EnemyBase.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\enemy\EnemyAttrDoctorCom.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\enemy\EnemyAttrShieldCom.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\enemy\EnemyComponent.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\enemy\EnemyEffectComp.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\mainPlane\MainPlane.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\enemy\EnemyEntity.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\Game\prefabs\dj_zj_1_4.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\Game\prefabs\dj_zj_1_5.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\Game\prefabs\dj_zj_1_6.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\Game\prefabs\dj_zj_1_7.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\Game\prefabs\dj_zj_1_8.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\Game\prefabs\dj_zj_1_9.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\Game\prefabs\EnemyPlane.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\Game\prefabs\MainPlane.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\Game\prefabs\effect\Hurt0.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\const
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\prefabs\boss
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\prefabs\enemy
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\prefabs\mainPlane
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\scripts\core\base\PlaneManager.ts
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\data\MainPlaneFightData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\const\AttributeConst.ts
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\manager\GamePlaneManager.ts
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\data\plane
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\Plane.ts
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\FightEntity.ts
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\data\plane\PlaneCacheInfo.ts
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\data\base\AttributeData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\data\plane\PlaneData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\boss\BossPlane.ts
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\mainPlane\MainPlaneFight.ts
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\prefab\Plane.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\prefabs\boss\BossPlane.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\prefabs\effect\Boom.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\prefabs\effect\Hurt.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\prefabs\enemy\dj_zj_1_4.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\prefabs\enemy\dj_zj_1_5.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\prefabs\enemy\dj_zj_1_6.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\prefabs\enemy\dj_zj_1_7.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\prefabs\enemy\dj_zj_1_8.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\prefabs\enemy\dj_zj_1_9.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\prefabs\enemy\EnemyPlane.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\prefabs\mainPlane\MainPlaneFight.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\scenes
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\scripts
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\scripts\MyApp.ts
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\scenes\Main.scene
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ResUpdate
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\prefab
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\prefabs
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\collider-system
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\scripts\core\base
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\const
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\data
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\manager
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\GameIns.ts
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\scenes
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ResUpdate\ResUpdate.ts
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\res
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\data
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\script\ui
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\prefabs\effect
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\collider-system\FBoxCollider.ts
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\collider-system\FColliderManager.ts
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\const\GameConst.ts
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\const\GameResourceList.ts
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\manager\BossManager.ts
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\manager\EnemyManager.ts
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\manager\BattleManager.ts
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\manager\BulletManager.ts
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\manager\GameDataManager.ts
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\manager\GameRuleManager.ts
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\manager\SceneManager.ts
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\manager\MainPlaneManager.ts
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\manager\WaveManager.ts
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\scenes\GameMain.ts
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\base
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\bullet
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\bulletDanmu
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\map
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\plane
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\res\PlaneRes.ts
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\data\base
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\data\DataManager.ts
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\script\ui\HomeUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\base\Controller.ts
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\base\PfFrameAnim.ts
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\base\NodeMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\base\TrackComponent.ts
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\bullet\Bullet.ts
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\bullet\CircleZoomFly.ts
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\bullet\BulletFly.ts
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\bulletDanmu\AimCircleScreen.ts
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\bulletDanmu\AimSingleLineScreen.ts
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\bulletDanmu\BaseScreen.ts
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\bulletDanmu\CircleScreen.ts
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\map\GameMapRun.ts
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\mainPlane
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\boss
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\skill
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\enemy
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\enemy\EnemyPlane.ts
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\enemy\EnemyShootComponent.ts
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\enemy\EnemyPlaneRole.ts
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\skill\BuffComp.ts
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\skill\SkillComp.ts
background: #aaff85; color: #000;
color: #000;
2025-9-8 10:08:40-debug: refresh db assets success
2025-9-8 10:08:40-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-8 10:08:40-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-8 10:08:40-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-8 10:08:40-debug: asset-db:refresh-all-database (284ms)
2025-9-8 10:08:40-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-8 11:05:40-debug: refresh db internal success
2025-9-8 11:05:40-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\BulletController.ts
background: #aaff85; color: #000;
color: #000;
2025-9-8 11:05:40-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\actions\EmitterEventActions.ts
background: #aaff85; color: #000;
color: #000;
2025-9-8 11:05:40-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\conditions\EmitterEventConditions.ts
background: #aaff85; color: #000;
color: #000;
2025-9-8 11:05:40-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\data\bullet\EventConditionType.ts
background: #aaff85; color: #000;
color: #000;
2025-9-8 11:05:40-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\data\bullet\EventActionType.ts
background: #aaff85; color: #000;
color: #000;
2025-9-8 11:05:40-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-8 11:05:40-debug: refresh db assets success
2025-9-8 11:05:40-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-8 11:05:40-debug: asset-db:refresh-all-database (169ms)
2025-9-8 11:13:07-debug: refresh db internal success
2025-9-8 11:13:07-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\EventGroup.ts
background: #aaff85; color: #000;
color: #000;
2025-9-8 11:13:07-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\conditions\EmitterEventConditions.ts
background: #aaff85; color: #000;
color: #000;
2025-9-8 11:13:07-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\data\bullet\EventConditionType.ts
background: #aaff85; color: #000;
color: #000;
2025-9-8 11:13:07-debug: refresh db assets success
2025-9-8 11:13:07-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-8 11:13:07-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-8 11:13:07-debug: asset-db:refresh-all-database (134ms)
2025-9-8 11:13:13-debug: refresh db internal success
2025-9-8 11:13:13-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-8 11:13:13-debug: refresh db assets success
2025-9-8 11:13:13-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-8 11:13:13-debug: asset-db:refresh-all-database (107ms)
2025-9-8 11:13:13-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-8 11:13:13-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-8 11:15:58-debug: refresh db internal success
2025-9-8 11:15:58-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-8 11:15:58-debug: refresh db assets success
2025-9-8 11:15:58-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-8 11:15:58-debug: asset-db:refresh-all-database (125ms)
2025-9-8 11:16:13-debug: refresh db internal success
2025-9-8 11:16:13-debug: refresh db assets success
2025-9-8 11:16:13-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-8 11:16:13-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-8 11:16:13-debug: asset-db:refresh-all-database (100ms)
2025-9-8 11:16:13-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-8 11:16:13-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-8 11:34:54-debug: refresh db internal success
2025-9-8 11:34:54-debug: refresh db assets success
2025-9-8 11:34:54-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-8 11:34:54-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-8 11:34:54-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-8 11:34:54-debug: asset-db:refresh-all-database (119ms)
2025-9-8 11:34:54-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-8 11:53:08-debug: refresh db internal success
2025-9-8 11:53:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\PlaneWithWeapon.ts
background: #aaff85; color: #000;
color: #000;
2025-9-8 11:53:08-debug: refresh db assets success
2025-9-8 11:53:08-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-8 11:53:08-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-8 11:53:08-debug: asset-db:refresh-all-database (102ms)
2025-9-8 11:54:24-debug: refresh db internal success
2025-9-8 11:54:24-debug: refresh db assets success
2025-9-8 11:54:24-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-8 11:54:24-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-8 11:54:24-debug: asset-db:refresh-all-database (165ms)
2025-9-8 11:54:38-debug: start remove asset E:\M2Game\Client\assets\resources\Game\levels...
2025-9-8 11:54:38-debug: start refresh asset from E:\M2Game\Client\assets\resources\Game\levels...
2025-9-8 11:54:38-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\Game\levels
background: #ffb8b8; color: #000;
color: #000;
2025-9-8 11:54:38-debug: refresh asset E:\M2Game\Client\assets\resources\Game success
2025-9-8 11:54:38-debug: remove asset E:\M2Game\Client\assets\resources\Game\levels success
2025-9-8 11:54:38-debug: refresh db internal success
2025-9-8 11:54:38-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game
background: #aaff85; color: #000;
color: #000;
2025-9-8 11:54:38-debug: refresh db assets success
2025-9-8 11:54:38-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-8 11:54:38-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-8 11:54:38-debug: asset-db:refresh-all-database (125ms)
2025-9-8 11:54:38-debug: asset-db:worker-effect-data-processing (7ms)
2025-9-8 11:54:38-debug: asset-db-hook-engine-extends-afterRefresh (7ms)
2025-9-8 11:55:06-debug: refresh db internal success
2025-9-8 11:55:06-debug: refresh db assets success
2025-9-8 11:55:06-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-8 11:55:06-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-8 11:55:06-debug: asset-db:refresh-all-database (136ms)
2025-9-8 11:55:06-debug: asset-db:worker-effect-data-processing (7ms)
2025-9-8 11:55:06-debug: asset-db-hook-engine-extends-afterRefresh (8ms)
2025-9-8 11:55:08-debug: refresh db internal success
2025-9-8 11:55:08-debug: refresh db assets success
2025-9-8 11:55:08-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-8 11:55:08-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-8 11:55:08-debug: asset-db:refresh-all-database (124ms)
2025-9-8 11:55:08-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-8 11:55:08-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-8 11:55:24-debug: refresh db internal success
2025-9-8 11:55:24-debug: refresh db assets success
2025-9-8 11:55:24-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-8 11:55:24-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-8 11:55:24-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-8 11:55:24-debug: asset-db:refresh-all-database (98ms)
2025-9-8 11:55:24-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
