import { _decorator, misc, instantiate, Node, Component, Prefab, Color, Vec3, Quat, assetManager } from 'cc';
import { EDITOR } from 'cc/env';
import { BulletProperty, BulletController } from './BulletController';
import { EmitterData } from '../data/bullet/EmitterData';
import { BulletData } from '../data/bullet/BulletData';
import { ObjectPool } from './ObjectPool';
import { BulletSystem } from './BulletSystem';
import { EventGroup, EventGroupContext } from "./EventGroup";
import { Property, PropertyContainerComponent } from './PropertyContainer';

const { ccclass, executeInEditMode, property, disallowMultiple, menu  } = _decorator;
const { degreesToRadians, radiansToDegrees } = misc;

export enum eEmitterStatus {
    None, Prewarm, Emitting, LoopEndReached, Completed
}

// 用枚举定义属性
export enum eEmitterProp {
    IsActive = 1, IsOnlyInScreen, IsPreWarm, IsLoop, 
    InitialDelay, PreWarmDuration, EmitBulletID, EmitDuration, EmitInterval, EmitPower, LoopInterval,
    PerEmitCount, PerEmitInterval, PerEmitOffsetX, 
    Angle, Count, Arc, Radius,
    TotalElapsedTime, 
}

@ccclass('Emitter')
// @inspector('editor/inspector/components/emitter')
@menu('子弹系统/发射器')
@executeInEditMode(true)
@disallowMultiple(true)
export class Emitter extends PropertyContainerComponent<eEmitterProp> {

    static kBulletNameInEditor:string = "_bullet_";

    @property({displayName: "子弹ID"})
    public bulletID: number = 0;

    @property({type: EmitterData, displayName: "发射器属性"})
    readonly emitterData: EmitterData = new EmitterData();

    @property({type: BulletData, displayName: "子弹属性"})
    readonly bulletData: BulletData = new BulletData();

    // 以下属性缓存为了性能优化(减少this.getProperty<T>的调用)
    public isActive!: Property<boolean>;
    public isOnlyInScreen!: Property<boolean>;
    public isPreWarm!: Property<boolean>;
    public isLoop!: Property<boolean>;
    public initialDelay!: Property<number>;
    public preWarmDuration!: Property<number>;
    public emitBulletID!: Property<number>;
    public emitDuration!: Property<number>;
    public emitInterval!: Property<number>;
    public emitPower!: Property<number>;
    public loopInterval!: Property<number>;
    public perEmitCount!: Property<number>;
    public perEmitInterval!: Property<number>;
    public perEmitOffsetX!: Property<number>;
    public angle!: Property<number>;
    public count!: Property<number>;
    public arc!: Property<number>;
    public radius!: Property<number>;
    public totalElapsedTime!: Property<number>;
    // 以下用于事件组修改子弹的属性，（不直接修改bulletData)
    public bulletProp!: BulletProperty;

    // 发射器自己的事件组
    public eventGroups: EventGroup[] = [];

    // 私有变量
    protected _status: eEmitterStatus = eEmitterStatus.None;
    protected _statusElapsedTime: number = 0;
    protected _isEmitting: boolean = false;
    protected _nextEmitTime: number = 0;
    protected _bulletPrefab: Prefab|null = null;
    protected _prewarmEffectPrefab: Prefab|null = null;
    protected _emitEffectPrefab: Prefab|null = null;

    // Per-emit timing tracking
    protected _perEmitBulletQueue: Array<{index: number, perEmitIndex: number, targetTime: number}> = [];

    get isEmitting(): boolean { return this._isEmitting; }
    get status(): eEmitterStatus { return this._status; }
    get statusElapsedTime(): number { return this._statusElapsedTime; }

    protected onLoad() : void {
        this.createProperties();
        this.createEventGroups();
    }

    protected onEnable(): void {
        BulletSystem.onCreateEmitter(this);
    }

    protected onDisable(): void {
        BulletSystem.onDestroyEmitter(this);
    }

    public onLostFocusInEditor(): void {
        this.updatePropertiesInEditor();
        this.createEventGroups();
    }

    public resetInEditor() {
        this.resetProperties();
        this.changeStatus(eEmitterStatus.None);
        this._isEmitting = false;
        this.totalElapsedTime.value = 0;
    }

    public updatePropertiesInEditor() {
        if (!this.emitterData) return;
        
        this.isActive.value = true;
        this.emitBulletID.value = this.bulletID;
        this.isOnlyInScreen.value = this.emitterData.isOnlyInScreen;
        this.isPreWarm.value = this.emitterData.isPreWarm;
        this.isLoop.value = this.emitterData.isLoop;

        this.initialDelay.value = this.emitterData.initialDelay.eval();
        this.preWarmDuration.value = this.emitterData.preWarmDuration.eval();
        this.emitDuration.value = this.emitterData.emitDuration.eval();
        this.emitInterval.value = this.emitterData.emitInterval.eval();
        this.emitPower.value = this.emitterData.emitPower.eval();
        this.loopInterval.value = this.emitterData.loopInterval.eval();
        this.perEmitCount.value = this.emitterData.perEmitCount.eval();
        this.perEmitInterval.value = this.emitterData.perEmitInterval.eval();
        this.perEmitOffsetX.value = this.emitterData.perEmitOffsetX.eval();
        this.angle.value = this.emitterData.angle.eval();
        this.count.value = this.emitterData.count.eval();
        this.arc.value = this.emitterData.arc.eval();
        this.radius.value = this.emitterData.radius.eval();

        this.notifyAll(true);
    }

    protected createProperties() {
        this.clear();
        
        this.isActive = this.addProperty(eEmitterProp.IsActive, true);
        this.totalElapsedTime = this.addProperty(eEmitterProp.TotalElapsedTime, 0);
        this.emitBulletID = this.addProperty(eEmitterProp.EmitBulletID, this.bulletID);
        this.isOnlyInScreen = this.addProperty(eEmitterProp.IsOnlyInScreen, true);
        this.isPreWarm = this.addProperty(eEmitterProp.IsPreWarm, true);
        this.isLoop = this.addProperty(eEmitterProp.IsLoop, true);

        this.initialDelay = this.addProperty(eEmitterProp.InitialDelay, 0);
        this.preWarmDuration = this.addProperty(eEmitterProp.PreWarmDuration, 0);
        this.emitDuration = this.addProperty(eEmitterProp.EmitDuration, 0);
        this.emitInterval = this.addProperty(eEmitterProp.EmitInterval, 0);
        this.emitPower = this.addProperty(eEmitterProp.EmitPower, 1);
        this.loopInterval = this.addProperty(eEmitterProp.LoopInterval, 0);
        this.perEmitCount = this.addProperty(eEmitterProp.PerEmitCount, 1);
        this.perEmitInterval = this.addProperty(eEmitterProp.PerEmitInterval, 0);
        this.perEmitOffsetX = this.addProperty(eEmitterProp.PerEmitOffsetX, 0);
        this.angle = this.addProperty(eEmitterProp.Angle, 0);
        this.count = this.addProperty(eEmitterProp.Count, 1);
        this.arc = this.addProperty(eEmitterProp.Arc, 0);
        this.radius = this.addProperty(eEmitterProp.Radius, 0);

        // 子弹相关属性
        this.bulletProp = new BulletProperty();

        this.emitBulletID.on((value) => {
            // TODO: reload bullet prefab
            this._bulletPrefab = null;
        });
        this.isActive.on((value) => {
            if (value) {
                
            } else {
                
            }
        });
    }

    protected createEventGroups() {
        if (!this.emitterData || this.emitterData.eventGroupData.length <= 0) return;

        this.eventGroups = [];
        let ctx = new EventGroupContext();
        ctx.emitter = this;
        for (const eventGroup of this.emitterData.eventGroupData) {
            BulletSystem.createEmitterEventGroup(ctx, eventGroup);
        }
    }

    // reset properties from emitterData
    protected resetProperties() {
        if (!this.emitterData) return;
        
        this.totalElapsedTime.value = 0;
        this.isActive.value = true;
        this.emitBulletID.value = this.bulletID;
        this.isOnlyInScreen.value = this.emitterData.isOnlyInScreen;
        this.isPreWarm.value = this.emitterData.isPreWarm;
        this.isLoop.value = this.emitterData.isLoop;

        this.initialDelay.value = this.emitterData.initialDelay.eval();
        this.preWarmDuration.value = this.emitterData.preWarmDuration.eval();
        this.emitDuration.value = this.emitterData.emitDuration.eval();
        this.emitInterval.value = this.emitterData.emitInterval.eval();
        this.emitPower.value = this.emitterData.emitPower.eval();
        this.loopInterval.value = this.emitterData.loopInterval.eval();
        this.perEmitCount.value = this.emitterData.perEmitCount.eval();
        this.perEmitInterval.value = this.emitterData.perEmitInterval.eval();
        this.perEmitOffsetX.value = this.emitterData.perEmitOffsetX.eval();
        this.angle.value = this.emitterData.angle.eval();
        this.count.value = this.emitterData.count.eval();
        this.arc.value = this.emitterData.arc.eval();
        this.radius.value = this.emitterData.radius.eval();

        this.bulletProp.resetFromData(this.bulletData);

        this.notifyAll(true);
    }

    /**
     * public apis
     */
    changeStatus(status: eEmitterStatus) {
        this._status = status;
        this._statusElapsedTime = 0;
        this._nextEmitTime = 0;
        // Clear per-emit queue when changing status
        this._perEmitBulletQueue = [];
        if (status === eEmitterStatus.Emitting || status === eEmitterStatus.Prewarm) {
            if (this.eventGroups.length > 0) {
                this.eventGroups.forEach(group => group.start());
            }
        }
        else {
            if (this.eventGroups.length > 0) {
                this.eventGroups.forEach(group => group.stop());
            }
        }
    }

    protected scheduleNextEmit() {
        // Schedule the next emit after emitInterval
        this._nextEmitTime = this._statusElapsedTime + this.emitInterval.value;
    }

    protected startEmitting() {
        this._isEmitting = true;
        // 下一次update时触发发射
        // 或者在这里调用 this.tryEmit() && this.scheduleNextEmit(); 立即触发发射
    }
    
    protected stopEmitting() {
        this._isEmitting = false;
        // Clear the per-emit bullet queue
        this._perEmitBulletQueue = [];
    }

    protected canEmit(): boolean {
        // 检查是否可以触发发射
        // Override this method in subclasses to add custom trigger conditions
        return true;
    }

    protected emit(): void {
        if (this.perEmitInterval.value > 0) {
            // Generate bullets in time-sorted order directly
            for (let j = 0; j < this.perEmitCount.value; j++) {
                const targetTime = this._statusElapsedTime + (this.perEmitInterval.value * j);
                for (let i = 0; i < this.count.value; i++) {
                    this._perEmitBulletQueue.push({
                        index: i,
                        perEmitIndex: j,
                        targetTime: targetTime
                    });
                }
            }
        }
        else {
            // Immediate emission - no timing needed
            for (let i = 0; i < this.count.value; i++) {
                for (let j = 0; j < this.perEmitCount.value; j++) {
                    this.emitSingle(i, j);
                }
            }
        }
    }

    protected processPerEmitQueue(): void {
        // Process bullets that should be emitted based on current time
        while (this._perEmitBulletQueue.length > 0) {
            const nextBullet = this._perEmitBulletQueue[0];

            // Check if it's time to emit this bullet
            if (this._statusElapsedTime >= nextBullet.targetTime) {
                // Remove from queue and emit
                this._perEmitBulletQueue.shift();
                this.emitSingle(nextBullet.index, nextBullet.perEmitIndex);
            } else {
                // No more bullets ready to emit yet
                break;
            }
        }
    }

    protected tryEmit(): boolean {
        if (this.canEmit()) {
            this.emit();
            return true;
        }
        return false;
    }

    protected emitSingle(index:number, perEmitIndex: number) {
        const direction = this.getSpawnDirection(index);
        const position = this.getSpawnPosition(index, perEmitIndex);
        this.createBullet(direction, position);
    }

    /**
     * Calculate the direction for a bullet at the given index
     * @param index The index of the bullet (0 to count-1)
     * @returns Direction vector {x, y}
     */
    getSpawnDirection(index: number): { x: number, y: number } {
        // 计算发射方向
        const angleOffset = this.count.value > 1 ? (this.arc.value / (this.count.value - 1)) * index - this.arc.value / 2 : 0;
        const radian = degreesToRadians(this.angle.value + angleOffset);
        
        return {
            x: Math.cos(radian),
            y: Math.sin(radian)
        };
    }

    /**
     * Get the spawn position for a bullet at the given index
     * odd number to the right, even number to the left
     * @param index The index of the bullet (0 to count-1)
     * @returns Position offset from emitter center
     */
    getSpawnPosition(index: number, perEmitIndex: number): { x: number, y: number } {
        // add perEmitOffsetX by perEmitIndex, with the rules:
        // by the following order:0, 1; 2, 0, 1; 2, 0, 1, 3;
        const getEmitOffsetX = (perEmitIndex: number, perEmitCount: number, perEmitOffsetX: number) => {
            if (perEmitCount <= 0) return 0;
            const step = perEmitOffsetX / perEmitCount;
            const start = -(perEmitOffsetX / 2) + step / 2;

            let posIndex: number;

            if (perEmitCount % 2 === 1) {
                // odd count
                const center = Math.floor(perEmitCount / 2);
                if (perEmitIndex === 0) {
                    posIndex = center;
                } else {
                    const shift = Math.floor((perEmitIndex + 1) / 2);
                    if (perEmitIndex % 2 === 1) {
                        posIndex = center - shift; // left
                    } else {
                        posIndex = center + shift; // right
                    }
                }
            } else {
                // even count
                const rightOfCenter = perEmitCount / 2;
                const leftOfCenter = rightOfCenter - 1;
                const shift = Math.floor(perEmitIndex / 2);
                if (perEmitIndex % 2 === 0) {
                    posIndex = leftOfCenter - shift; // left side
                } else {
                    posIndex = rightOfCenter + shift; // right side
                }
            }

            return start + posIndex * step;
        }

        const perEmitOffsetX = getEmitOffsetX(perEmitIndex, this.perEmitCount.value, this.perEmitOffsetX.value);

        if (this.radius.value <= 0) {
            return { x: perEmitOffsetX, y: 0 };
        }
        
        const direction = this.getSpawnDirection(index);
        return {
            x: direction.x * this.radius.value + perEmitOffsetX,
            y: direction.y * this.radius.value
        };
    }

    createBullet(direction: { x: number, y: number }, position: { x: number, y: number }): void {
        if (!this._bulletPrefab) {
            if (EDITOR) {
                this.createBulletInEditor(direction, position);
            }
            else {
                console.warn("Emitter: No bullet prefab assigned");
            }
            return;
        }
        
        const bullet = this.instantiateBullet();
        if (!bullet) return;

        // Set bullet position relative to emitter
        const emitterPos = this.node.getWorldPosition();
        bullet.node.setWorldPosition(
            emitterPos.x + position.x,
            emitterPos.y + position.y,
            emitterPos.z
        );

        BulletSystem.onCreateBullet(this, bullet);
        bullet.prop.speedAngle.value = radiansToDegrees(Math.atan2(direction.y, direction.x));
        bullet.prop.speed.value *= this.emitPower.value;
        // 为什么需要在这里resetEventGroups?
        // 因为EventGroups的条件初始化依赖上面先初始化子弹的属性
        bullet.resetEventGroups();
    }

    protected async createBulletInEditor(direction: { x: number, y: number }, position: { x: number, y: number }) {
        // use a default bullet prefab
        const prefabPath = 'db://assets/resources/Game/prefabs/Bullet_New.prefab';
        // @ts-ignore
        Editor.Message.request('asset-db', 'query-uuid', prefabPath)
            .then((uuid: string) => {
                assetManager.loadAny({uuid: uuid}, (err, prefab) => {
                    if (err) {
                        console.error(err);
                        return;
                    }
                    this._bulletPrefab = prefab;
                    const bullet = this.instantiateBullet();
                    if (!bullet) return;

                    // Set bullet position relative to emitter
                    const emitterPos = this.node.getWorldPosition();
                    bullet.node.setWorldPosition(
                        emitterPos.x + position.x,
                        emitterPos.y + position.y,
                        emitterPos.z
                    );

                    BulletSystem.onCreateBullet(this, bullet);
                    bullet.prop.speedAngle.value = radiansToDegrees(Math.atan2(direction.y, direction.x));
                    bullet.prop.speed.value *= this.emitPower.value;
                    bullet.resetEventGroups();
                });
            });
    }

    protected instantiateBullet(): BulletController | null {
        const bulletNode = ObjectPool.getNode(BulletSystem.bulletParent, this._bulletPrefab!);
        if (!bulletNode) {
            console.error("Emitter: Failed to instantiate bullet prefab");
            return null;
        }

        // Get the bullet component
        const bullet = bulletNode.getComponent(BulletController);
        if (!bullet) {
            console.error("Emitter: Bullet prefab does not have Bullet component");
            bulletNode.destroy();
            return null;
        }

        if (EDITOR) {
            bulletNode.name = Emitter.kBulletNameInEditor;
        }

        return bullet;
    }

    playEffect(prefab: Prefab, position: Vec3, rotation: Quat, duration: number) {
        if (!prefab) return;

        const effectNode = ObjectPool.getNode(this.node, prefab);
        if (!effectNode) return;

        effectNode.setWorldPosition(position);
        effectNode.setWorldRotation(rotation);
        // Play the effect and destroy it after duration
        // effectNode.getComponent(ParticleSystem)?.play();
        this.scheduleOnce(() => {
            ObjectPool.returnNode(effectNode);
        }, duration);
    }

    /**
     * Return true if this.node is in screen
     */
    protected isInScreen() : boolean {
        // TODO: Get mainCamera.containsNode(this.node)
        return true;
    }

    public tick(deltaTime: number): void {
        if (!this.isActive || !this.isActive.value) {
            return;
        }

        switch (this._status)
        {
            case eEmitterStatus.None:
                this.updateStatusNone();
                break;
            case eEmitterStatus.Prewarm:
                this.updateStatusPrewarm();
                break;
            case eEmitterStatus.Emitting:
                this.updateStatusEmitting();
                break;
            case eEmitterStatus.LoopEndReached:
                this.updateStatusLoopEndReached();
                break;
            case eEmitterStatus.Completed:
                this.updateStatusCompleted();
                break;
            default:
                break;
        }

        this._statusElapsedTime += deltaTime;
        this.totalElapsedTime.value += deltaTime;

        this.notifyAll();
    }

    protected updateStatusNone() {
        if (this._statusElapsedTime >= this.initialDelay.value) {
            this.changeStatus(eEmitterStatus.Prewarm);
        }
    }

    protected updateStatusPrewarm() {
        if (!this.isPreWarm.value)
            this.changeStatus(eEmitterStatus.Emitting);
        else {
            if (this._statusElapsedTime >= this.preWarmDuration.value) {
                this.changeStatus(eEmitterStatus.Emitting);
            }
        }
    }

    protected updateStatusEmitting() {
        if (this._statusElapsedTime > this.emitDuration.value) {
            this.stopEmitting();
            if (this.isLoop)
                this.changeStatus(eEmitterStatus.LoopEndReached);
            else
                this.changeStatus(eEmitterStatus.Completed);
            return;
        }
        
        // Start emitting if not already started
        if (!this._isEmitting) {
            this.startEmitting();
        }
        else if (this._statusElapsedTime >= this._nextEmitTime) {
            this.tryEmit();
            if (this.perEmitInterval.value <= 0) {
                this.scheduleNextEmit();
            }
            else {
                // 开始这一波
                this._nextEmitTime = this._statusElapsedTime + 10000000;
            }
        }
        
        let wasEmitting = this._perEmitBulletQueue.length > 0;
        // Process per-emit bullet queue based on precise timing
        this.processPerEmitQueue();
        if (wasEmitting && this._perEmitBulletQueue.length <= 0) {
            this.scheduleNextEmit();
        }
    }

    protected updateStatusLoopEndReached() {
        if (this._statusElapsedTime >= this.loopInterval.value) {
            this.changeStatus(eEmitterStatus.Prewarm);
        }
    }

    protected updateStatusCompleted() {
        // Do nothing or cleanup if needed
        this.isActive.value = false;
        this.isActive.notify();
    }
}
