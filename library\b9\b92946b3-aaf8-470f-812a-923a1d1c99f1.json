[{"__type__": "cc.Prefab", "_name": "Emitter<PERSON><PERSON><PERSON>", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "Emitter<PERSON><PERSON><PERSON>", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [], "_active": true, "_components": [{"__id__": 2}, {"__id__": 4}], "_prefab": {"__id__": 64}, "_lpos": {"__type__": "cc.Vec3", "x": 7.6, "y": -358.722, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 3}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d36WBdI5xGP58zLtnCr2Vq"}, {"__type__": "2564dArcRFKZKoo3odCQrHw", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 5}, "bulletID": 0, "emitterData": {"__id__": 6}, "bulletData": {"__id__": 42}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c9TmdPYGZEYYcUkBAgjFpq"}, {"__type__": "EmitterData", "isOnlyInScreen": true, "isPreWarm": false, "isLoop": true, "initialDelay": {"__id__": 7}, "preWarmDuration": {"__id__": 9}, "preWarmEffect": null, "emitDuration": {"__id__": 11}, "emitInterval": {"__id__": 13}, "emitPower": {"__id__": 15}, "loopInterval": {"__id__": 17}, "perEmitCount": {"__id__": 19}, "perEmitInterval": {"__id__": 21}, "perEmitOffsetX": {"__id__": 23}, "angle": {"__id__": 25}, "count": {"__id__": 27}, "arc": {"__id__": 29}, "radius": {"__id__": 31}, "emitEffect": null, "eventGroupData": [{"__id__": 33}]}, {"__type__": "ExpressionValue", "value": 0, "isExpression": false, "expression": "0", "serializedProgram": {"__id__": 8}}, {"__type__": "SerializableRPNProgram", "code": [], "consts": [], "varNames": [], "propPaths": [], "strictVars": true}, {"__type__": "ExpressionValue", "value": 0, "isExpression": false, "expression": "0", "serializedProgram": {"__id__": 10}}, {"__type__": "SerializableRPNProgram", "code": [], "consts": [], "varNames": [], "propPaths": [], "strictVars": true}, {"__type__": "ExpressionValue", "value": 60000, "isExpression": false, "expression": "60000", "serializedProgram": {"__id__": 12}}, {"__type__": "SerializableRPNProgram", "code": [], "consts": [], "varNames": [], "propPaths": [], "strictVars": true}, {"__type__": "ExpressionValue", "value": 500, "isExpression": true, "expression": "rand(100, 500)", "serializedProgram": {"__id__": 14}}, {"__type__": "SerializableRPNProgram", "code": [0, 0, 0, 1, 19, 11, 2], "consts": [100, 500], "varNames": [], "propPaths": [], "strictVars": true}, {"__type__": "ExpressionValue", "value": 1, "isExpression": false, "expression": "1", "serializedProgram": {"__id__": 16}}, {"__type__": "SerializableRPNProgram", "code": [], "consts": [], "varNames": [], "propPaths": [], "strictVars": true}, {"__type__": "ExpressionValue", "value": 0, "isExpression": false, "expression": "0", "serializedProgram": {"__id__": 18}}, {"__type__": "SerializableRPNProgram", "code": [], "consts": [], "varNames": [], "propPaths": [], "strictVars": true}, {"__type__": "ExpressionValue", "value": 1, "isExpression": true, "expression": "rand(1, 5)", "serializedProgram": {"__id__": 20}}, {"__type__": "SerializableRPNProgram", "code": [0, 0, 0, 1, 19, 11, 2], "consts": [1, 5], "varNames": [], "propPaths": [], "strictVars": true}, {"__type__": "ExpressionValue", "value": 0, "isExpression": true, "expression": "rand(100, 500)", "serializedProgram": {"__id__": 22}}, {"__type__": "SerializableRPNProgram", "code": [0, 0, 0, 1, 19, 11, 2], "consts": [100, 500], "varNames": [], "propPaths": [], "strictVars": true}, {"__type__": "ExpressionValue", "value": 0, "isExpression": false, "expression": "0", "serializedProgram": {"__id__": 24}}, {"__type__": "SerializableRPNProgram", "code": [], "consts": [], "varNames": [], "propPaths": [], "strictVars": true}, {"__type__": "ExpressionValue", "value": 270, "isExpression": false, "expression": "270", "serializedProgram": {"__id__": 26}}, {"__type__": "SerializableRPNProgram", "code": [], "consts": [], "varNames": [], "propPaths": [], "strictVars": true}, {"__type__": "ExpressionValue", "value": 3, "isExpression": true, "expression": "rand(1,5)", "serializedProgram": {"__id__": 28}}, {"__type__": "SerializableRPNProgram", "code": [0, 0, 0, 1, 19, 11, 2], "consts": [1, 5], "varNames": [], "propPaths": [], "strictVars": true}, {"__type__": "ExpressionValue", "value": 30, "isExpression": false, "expression": "30", "serializedProgram": {"__id__": 30}}, {"__type__": "SerializableRPNProgram", "code": [], "consts": [], "varNames": [], "propPaths": [], "strictVars": true}, {"__type__": "ExpressionValue", "value": 1, "isExpression": false, "expression": "1", "serializedProgram": {"__id__": 32}}, {"__type__": "SerializableRPNProgram", "code": [], "consts": [], "varNames": [], "propPaths": [], "strictVars": true}, {"__type__": "EventGroupData", "name": "", "triggerCount": 1, "conditions": [{"__id__": 34}], "actions": [{"__id__": 37}]}, {"__type__": "EventConditionData", "op": 0, "type": 5, "compareOp": 4, "targetValue": {"__id__": 35}}, {"__type__": "ExpressionValue", "value": 3000, "isExpression": false, "expression": "3000", "serializedProgram": {"__id__": 36}}, {"__type__": "SerializableRPNProgram", "code": [], "consts": [], "varNames": [], "propPaths": [], "strictVars": true}, {"__type__": "EventActionData", "type": 13, "duration": {"__id__": 38}, "targetValue": {"__id__": 40}, "targetValueType": 1, "easing": 2}, {"__type__": "ExpressionValue", "value": 10000, "isExpression": false, "expression": "10000", "serializedProgram": {"__id__": 39}}, {"__type__": "SerializableRPNProgram", "code": [], "consts": [], "varNames": [], "propPaths": [], "strictVars": true}, {"__type__": "ExpressionValue", "value": 0, "isExpression": false, "expression": "0", "serializedProgram": {"__id__": 41}}, {"__type__": "SerializableRPNProgram", "code": [], "consts": [], "varNames": [], "propPaths": [], "strictVars": true}, {"__type__": "BulletData", "isFacingMoveDir": true, "isTrackingTarget": false, "isDestroyOutScreen": false, "isDestructive": false, "isDestructiveOnHit": false, "scale": {"__id__": 43}, "duration": {"__id__": 45}, "delayDestroy": {"__id__": 47}, "speed": {"__id__": 49}, "acceleration": {"__id__": 51}, "accelerationAngle": {"__id__": 53}, "eventGroupData": [{"__id__": 55}]}, {"__type__": "ExpressionValue", "value": 1, "isExpression": false, "expression": "1", "serializedProgram": {"__id__": 44}}, {"__type__": "SerializableRPNProgram", "code": [], "consts": [], "varNames": [], "propPaths": [], "strictVars": true}, {"__type__": "ExpressionValue", "value": 10000, "isExpression": false, "expression": "10000", "serializedProgram": {"__id__": 46}}, {"__type__": "SerializableRPNProgram", "code": [], "consts": [], "varNames": [], "propPaths": [], "strictVars": true}, {"__type__": "ExpressionValue", "value": 0, "isExpression": false, "expression": "0", "serializedProgram": {"__id__": 48}}, {"__type__": "SerializableRPNProgram", "code": [], "consts": [], "varNames": [], "propPaths": [], "strictVars": true}, {"__type__": "ExpressionValue", "value": 1000, "isExpression": false, "expression": "1000", "serializedProgram": {"__id__": 50}}, {"__type__": "SerializableRPNProgram", "code": [], "consts": [], "varNames": [], "propPaths": [], "strictVars": true}, {"__type__": "ExpressionValue", "value": 100, "isExpression": false, "expression": "100", "serializedProgram": {"__id__": 52}}, {"__type__": "SerializableRPNProgram", "code": [], "consts": [], "varNames": [], "propPaths": [], "strictVars": true}, {"__type__": "ExpressionValue", "value": 0, "isExpression": false, "expression": "0", "serializedProgram": {"__id__": 54}}, {"__type__": "SerializableRPNProgram", "code": [], "consts": [], "varNames": [], "propPaths": [], "strictVars": true}, {"__type__": "EventGroupData", "name": "修改速度角", "triggerCount": 1, "conditions": [{"__id__": 56}], "actions": [{"__id__": 59}]}, {"__type__": "EventConditionData", "op": 0, "type": 101, "compareOp": 4, "targetValue": {"__id__": 57}}, {"__type__": "ExpressionValue", "value": 1000, "isExpression": false, "expression": "1000", "serializedProgram": {"__id__": 58}}, {"__type__": "SerializableRPNProgram", "code": [], "consts": [], "varNames": [], "propPaths": [], "strictVars": true}, {"__type__": "EventActionData", "type": 108, "duration": {"__id__": 60}, "targetValue": {"__id__": 62}, "targetValueType": 1, "easing": 0}, {"__type__": "ExpressionValue", "value": 2000, "isExpression": false, "expression": "2000", "serializedProgram": {"__id__": 61}}, {"__type__": "SerializableRPNProgram", "code": [], "consts": [], "varNames": [], "propPaths": [], "strictVars": true}, {"__type__": "ExpressionValue", "value": 0, "isExpression": false, "expression": "0", "serializedProgram": {"__id__": 63}}, {"__type__": "SerializableRPNProgram", "code": [], "consts": [], "varNames": [], "propPaths": [], "strictVars": true}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "49VutYVl1GrbQe4mCj3Sww", "targetOverrides": null}]