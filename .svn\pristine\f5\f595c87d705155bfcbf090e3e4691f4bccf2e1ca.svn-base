import { _decorator, error, v2, Vec2, Prefab, Enum } from "cc";
const { ccclass, property } = _decorator;

export enum eEmitterCondition {
    Emitter_Active = 1,         // 发射器是否启用
    Emitter_InitialDelay,       // 发射器当前的初始延迟
    Emitter_Prewarm,            // 发射器是否启用预热
    Emitter_PrewarmDuration,    // 发射器预热的持续时间
    Emitter_Duration,           // 发射器配置的持续时间
    Emitter_ElapsedTime,        // 发射器已运行的时间
    Emitter_Loop,               // 发射器是否循环
    Emitter_LoopInterval,       // 发射器循环的间隔时间

    Emitter_EmitInterval,       // 发射器开火间隔

    Emitter_PerEmitCount,       // 发射器开火次数
    Emitter_PerEmitInterval,    // 发射器单次开火间隔
    Emitter_PerEmitOffsetX,     // 发射器单次开火偏移

    Emitter_Angle,              // 发射器弹道角度
    Emitter_Count,              // 发射器弹道数量

    Bullet_Duration,
    Bullet_Speed,
    Bullet_Acceleration,
    Bullet_AccelerationAngle,
    Bullet_FacingMoveDir,
    Bullet_TrackingTarget,
    Bullet_Destructive,
    Bullet_DestructiveOnHit,

    // 思考: 这几个是不是直接判断子弹id得了
    Bullet_Sprite,          
    Bullet_Scale,
    Bullet_ColorR,
    Bullet_ColorG,
    Bullet_ColorB,
    Bullet_DefaultFacing,   // 子弹外观初始朝向

    Level_Duration,         // 已持续时间
    Level_Distance,         // 已飞行距离
    Level_InfLevel,         // 无尽模式当前关卡等级
    Level_ChallengeLevel,   // 闯关模式当前等级

    Player_ActLevel,        // 玩家账号等级
    Player_PosX,            // 玩家当前坐标X
    Player_PosY,            // 玩家当前坐标Y
    Player_LifePercent,     // 玩家当前生命百分比
    Player_GainBuff,        // 玩家获得buff

    Unit_Life,              // 单位当前生命值
    Unit_LifePercent,       // 单位当前生命百分比
    Unit_ElapsedTime,       // 单位当前持续时间
    Unit_PosX,              // 单位当前坐标X
    Unit_PosY,              // 单位当前坐标Y
    Unit_Speed,             // 单位当前速度
    Unit_SpeedAngle,        // 单位当前速度角度
    Unit_Acceleration,      // 单位当前加速度
    Unit_AccelerationAngle, // 单位当前加速度角度
    Unit_DistanceToPlayer,  // 单位与玩家的距离
    Unit_AngleToPlayer,     // 单位与玩家的角度
}

export enum eBulletCondition {
    Bullet_Duration = 100,
    Bullet_ElapsedTime,
    Bullet_PosX,
    Bullet_PosY,
    Bullet_Damage,
    Bullet_Speed,
    Bullet_SpeedAngle,
    Bullet_Acceleration,
    Bullet_AccelerationAngle,
    Bullet_Scale,
    Bullet_ColorR,
    Bullet_ColorG,
    Bullet_ColorB,
    Bullet_FacingMoveDir,
    Bullet_Destructive,
    Bullet_DestructiveOnHit,
}

export type eEventConditionType = eEmitterCondition | eBulletCondition;

// 以下枚举值用于编辑器显示，实际运行时不会用到
export enum eEmitterConditionCn {
    发射器是否启用 = eEmitterCondition.Emitter_Active,
    发射器当前的初始延迟 = eEmitterCondition.Emitter_InitialDelay,
    发射器是否预热 = eEmitterCondition.Emitter_Prewarm,
    发射器预热的持续时间 = eEmitterCondition.Emitter_PrewarmDuration,
    发射器的持续时间 = eEmitterCondition.Emitter_Duration,
    发射器已运行的时间 = eEmitterCondition.Emitter_ElapsedTime,
    发射器是否循环 = eEmitterCondition.Emitter_Loop,
    发射器循环的间隔时间 = eEmitterCondition.Emitter_LoopInterval,
    发射器开火间隔 = eEmitterCondition.Emitter_EmitInterval,
    发射器单次开火次数 = eEmitterCondition.Emitter_PerEmitCount,
    发射器单次开火间隔 = eEmitterCondition.Emitter_PerEmitInterval,
    发射器单次开火偏移 = eEmitterCondition.Emitter_PerEmitOffsetX,
    发射器弹道角度 = eEmitterCondition.Emitter_Angle,
    发射器弹道数量 = eEmitterCondition.Emitter_Count,

    子弹持续时间 = eEmitterCondition.Bullet_Duration,
    子弹速度 = eEmitterCondition.Bullet_Speed,
    子弹加速度 = eEmitterCondition.Bullet_Acceleration,
    子弹加速度角度 = eEmitterCondition.Bullet_AccelerationAngle,
    子弹面向移动方向 = eEmitterCondition.Bullet_FacingMoveDir,
    子弹追踪目标 = eEmitterCondition.Bullet_TrackingTarget,
    子弹可破坏 = eEmitterCondition.Bullet_Destructive,
    子弹命中时破坏 = eEmitterCondition.Bullet_DestructiveOnHit,
    子弹外观图片 = eEmitterCondition.Bullet_Sprite,
    子弹缩放 = eEmitterCondition.Bullet_Scale,
    子弹颜色R = eEmitterCondition.Bullet_ColorR,
    子弹颜色G = eEmitterCondition.Bullet_ColorG,
    子弹颜色B = eEmitterCondition.Bullet_ColorB,
    子弹外观初始朝向 = eEmitterCondition.Bullet_DefaultFacing,

    关卡已持续时间 = eEmitterCondition.Level_Duration,
    关卡已飞行距离 = eEmitterCondition.Level_Distance,
    无尽模式当前等级 = eEmitterCondition.Level_InfLevel,
    闯关模式当前等级 = eEmitterCondition.Level_ChallengeLevel,

    玩家账号等级 = eEmitterCondition.Player_ActLevel,
    玩家当前坐标X = eEmitterCondition.Player_PosX,
    玩家当前坐标Y = eEmitterCondition.Player_PosY,
    玩家当前生命百分比 = eEmitterCondition.Player_LifePercent,
    玩家获得buff = eEmitterCondition.Player_GainBuff,

    单位当前生命值 = eEmitterCondition.Unit_Life,
    单位当前生命百分比 = eEmitterCondition.Unit_LifePercent,
    单位当前持续时间 = eEmitterCondition.Unit_ElapsedTime,
    单位当前坐标X = eEmitterCondition.Unit_PosX,
    单位当前坐标Y = eEmitterCondition.Unit_PosY,
    单位当前速度 = eEmitterCondition.Unit_Speed,
    单位当前速度角度 = eEmitterCondition.Unit_SpeedAngle,
    单位当前加速度 = eEmitterCondition.Unit_Acceleration,
    单位当前加速度角度 = eEmitterCondition.Unit_AccelerationAngle,
    单位与玩家的距离 = eEmitterCondition.Unit_DistanceToPlayer,
    单位与玩家的角度 = eEmitterCondition.Unit_AngleToPlayer,
}

export enum eBulletConditionCn {
    子弹持续时间 = eBulletCondition.Bullet_Duration,
    子弹已运行时间 = eBulletCondition.Bullet_ElapsedTime,
    子弹坐标X = eBulletCondition.Bullet_PosX,
    子弹坐标Y = eBulletCondition.Bullet_PosY,
    子弹伤害 = eBulletCondition.Bullet_Damage,
    子弹速度 = eBulletCondition.Bullet_Speed,
    子弹速度角度 = eBulletCondition.Bullet_SpeedAngle,
    子弹加速度 = eBulletCondition.Bullet_Acceleration,
    子弹加速度角度 = eBulletCondition.Bullet_AccelerationAngle,
    子弹缩放 = eBulletCondition.Bullet_Scale,
    子弹颜色R = eBulletCondition.Bullet_ColorR,
    子弹颜色G = eBulletCondition.Bullet_ColorG,
    子弹颜色B = eBulletCondition.Bullet_ColorB,
    子弹面向移动方向 = eBulletCondition.Bullet_FacingMoveDir,
    子弹可破坏 = eBulletCondition.Bullet_Destructive,
    子弹命中时破坏 = eBulletCondition.Bullet_DestructiveOnHit,
}