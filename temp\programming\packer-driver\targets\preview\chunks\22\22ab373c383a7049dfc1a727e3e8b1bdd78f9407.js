System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, FunctionRegistry, RPNProgram, RPNCalculator, _crd, OpCode, NUM_RE, OPS;

  function compileRPNInternal(tokens, registry, opts) {
    var _opts$unknownIdentifi, _opts$strictVars;

    var code = [];
    var consts = [];
    var varIndex = new Map();
    var varNames = [];

    var pushConst = v => {
      consts.push(v);
      code.push(OpCode.PUSH_CONST, consts.length - 1);
    };

    var pushVar = name => {
      var idx = varIndex.get(name);

      if (idx === undefined) {
        idx = varNames.length;
        varIndex.set(name, idx);
        varNames.push(name);
      }

      code.push(OpCode.PUSH_VAR, idx);
    };

    var emitBinary = op => code.push(op);

    var unknownIsVar = (_opts$unknownIdentifi = opts == null ? void 0 : opts.unknownIdentifierIsVar) != null ? _opts$unknownIdentifi : true;
    var strictVars = (_opts$strictVars = opts == null ? void 0 : opts.strictVars) != null ? _opts$strictVars : true;

    for (var raw of tokens) {
      var t = raw.trim();
      if (!t) continue; // constants

      if (NUM_RE.test(t)) {
        pushConst(parseFloat(t));
        continue;
      }

      if (/^true$/i.test(t)) {
        pushConst(true);
        continue;
      }

      if (/^false$/i.test(t)) {
        pushConst(false);
        continue;
      } // Operators


      switch (t) {
        case '+':
          emitBinary(OpCode.ADD);
          continue;

        case '-':
          emitBinary(OpCode.SUB);
          continue;

        case '*':
          emitBinary(OpCode.MUL);
          continue;

        case '/':
          emitBinary(OpCode.DIV);
          continue;

        case '%':
          emitBinary(OpCode.MOD);
          continue;

        case '^':
          emitBinary(OpCode.POW);
          continue;

        case 'neg':
          code.push(OpCode.NEG);
          continue;
        // unary neg

        case '<':
          emitBinary(OpCode.LT);
          continue;

        case '<=':
          emitBinary(OpCode.LE);
          continue;

        case '>':
          emitBinary(OpCode.GT);
          continue;

        case '>=':
          emitBinary(OpCode.GE);
          continue;

        case '==':
          emitBinary(OpCode.EQ);
          continue;

        case '!=':
          emitBinary(OpCode.NEQ);
          continue;

        case '&&':
          emitBinary(OpCode.AND);
          continue;

        case '||':
          emitBinary(OpCode.OR);
          continue;

        case '!':
          code.push(OpCode.NOT);
          continue;
      } // Function token produced by infix parser: format FUNC:name:arity


      if (t.startsWith('FUNC:')) {
        var parts = t.split(':'); // parts[0] == 'FUNC'

        var _name2 = parts[1];
        var arity = parseInt(parts[2], 10);
        var meta = registry.getByNameAndArity(_name2, arity);
        if (!meta) throw new Error("Function '" + _name2 + "' with arity " + arity + " is not registered.");
        code.push(OpCode.CALL, meta.id, arity);
        continue;
      } // identifier -> variable or error


      if (unknownIsVar) {
        pushVar(t);
        continue;
      }

      throw new Error("Unknown token '" + t + "'.");
    }

    return new RPNProgram(code, consts, varNames, registry, strictVars);
  } // ---------------- Infix -> RPN (Shunting-yard) ----------------


  function tokenize(expr) {
    var out = [];
    var re = /\s*([0-9]*\.?[0-9]+|[A-Za-z_][A-Za-z0-9_]*|<=|>=|==|!=|&&|\|\||[+\-*/%^(),!<>])\s*/g;
    var m;

    while ((m = re.exec(expr)) !== null) {
      var s = m[1];
      if (/^[0-9]*\.?[0-9]+$/.test(s)) out.push({
        type: 'number',
        value: s
      });else if (/^true|false$/i.test(s)) out.push({
        type: 'bool',
        value: s
      });else if (s === '(') out.push({
        type: 'lparen',
        value: s
      });else if (s === ')') out.push({
        type: 'rparen',
        value: s
      });else if (s === ',') out.push({
        type: 'comma',
        value: s
      });else if (/^[+\-*/%^!<>]=?$/.test(s) || s === '&&' || s === '||') out.push({
        type: 'op',
        value: s
      });else out.push({
        type: 'ident',
        value: s
      });
    }

    return out;
  } // Operator properties


  function infixToRPN(expr, registry) {
    var tokens = tokenize(expr);
    var output = [];
    var opStack = []; // helper: when an identifier is followed by '(', it's a function name

    for (var i = 0; i < tokens.length; i++) {
      var t = tokens[i];

      if (t.type === 'number' || t.type === 'bool') {
        output.push(t.value);
        continue;
      }

      if (t.type === 'ident') {
        // If next token is lparen -> function
        var next = tokens[i + 1];

        if (next && next.type === 'lparen') {
          // push function onto opStack; track argument count (start at 1 for first arg, will increment on commas)
          opStack.push({
            type: 'func',
            value: t.value,
            argCount: 1
          });
        } else {
          // variable
          output.push(t.value);
        }

        continue;
      }

      if (t.type === 'comma') {
        // pop operators to output until left paren
        while (opStack.length > 0 && opStack[opStack.length - 1].type !== 'lparen') {
          var op = opStack.pop();
          if (op.type === 'op') output.push(op.value);else if (op.type === 'func') {
            var _op$argCount;

            // shouldn't reach here on comma, but handle defensively
            output.push("FUNC:" + op.value + ":" + ((_op$argCount = op.argCount) != null ? _op$argCount : 0));
          }
        } // increment argCount of function on stack (function must be below the left paren)
        // find nearest func below the lparen


        for (var j = opStack.length - 1; j >= 0; j--) {
          if (opStack[j].type === 'lparen') {
            // Look for function below the lparen
            if (j > 0 && opStack[j - 1].type === 'func') {
              var _opStack$argCount;

              opStack[j - 1].argCount = ((_opStack$argCount = opStack[j - 1].argCount) != null ? _opStack$argCount : 1) + 1;
            }

            break;
          }
        }

        continue;
      }

      if (t.type === 'op') {
        var rawOp = t.value; // detect unary minus: if '-' and (start or after lparen or after comma or after another operator)

        var prev = tokens[i - 1];
        var isUnary = rawOp === '-' && (i === 0 || prev && (prev.type === 'op' || prev.type === 'lparen' || prev.type === 'comma'));
        var opKey = isUnary && rawOp === '-' ? 'neg' : rawOp;
        if (!(opKey in OPS)) throw new Error("Unknown operator '" + rawOp + "'");
        var o1 = OPS[opKey];

        while (opStack.length > 0) {
          var top = opStack[opStack.length - 1];
          if (top.type !== 'op') break;
          var o2 = OPS[top.value];
          if (!o2) break;

          if (o1.assoc === 'left' && o1.prec <= o2.prec || o1.assoc === 'right' && o1.prec < o2.prec) {
            output.push(opStack.pop().value);
          } else break;
        }

        opStack.push({
          type: 'op',
          value: opKey
        });
        continue;
      }

      if (t.type === 'lparen') {
        opStack.push({
          type: 'lparen',
          value: '('
        }); // if the token before lparen is a function, we already pushed the function earlier.

        continue;
      }

      if (t.type === 'rparen') {
        // pop until left paren
        while (opStack.length > 0 && opStack[opStack.length - 1].type !== 'lparen') {
          var _op = opStack.pop();

          if (_op.type === 'op') output.push(_op.value);else if (_op.type === 'func') {
            var _op$argCount2;

            // shouldn't normally be here
            output.push("FUNC:" + _op.value + ":" + ((_op$argCount2 = _op.argCount) != null ? _op$argCount2 : 0));
          }
        }

        if (opStack.length === 0) throw new Error('Mismatched parentheses'); // pop the left paren

        opStack.pop(); // if top is a function, pop it to output with correct arity

        if (opStack.length > 0 && opStack[opStack.length - 1].type === 'func') {
          var fn = opStack.pop(); // determine if there was any argument: check token before current index (i)

          var before = tokens[i - 1];
          var finalArity = void 0;

          if (before && before.type === 'lparen') {
            // empty function call like f()
            finalArity = 0;
          } else {
            var _fn$argCount;

            // function has arguments, use the argCount we've been tracking
            finalArity = (_fn$argCount = fn.argCount) != null ? _fn$argCount : 1;
          }

          output.push("FUNC:" + fn.value + ":" + finalArity);
        }

        continue;
      }
    } // pop remaining ops


    while (opStack.length > 0) {
      var _op2 = opStack.pop();

      if (_op2.type === 'lparen' || _op2.type === 'rparen') throw new Error('Mismatched parentheses');
      if (_op2.type === 'op') output.push(_op2.value);else if (_op2.type === 'func') {
        var _op2$argCount;

        // function with no parentheses? shouldn't happen
        output.push("FUNC:" + _op2.value + ":" + ((_op2$argCount = _op2.argCount) != null ? _op2$argCount : 0));
      }
    }

    return output;
  } // ---------------- Public RPNCalculator integrating infix parser ----------------


  _export({
    FunctionRegistry: void 0,
    RPNProgram: void 0,
    RPNCalculator: void 0
  });

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "281eayVE2tIiLE9RxQjgFdL", "RPN", undefined); // rpn_calculator_infix.ts
      // Complete TypeScript: Infix -> RPN -> Bytecode VM
      // Usage example at bottom.
      // -------- Function registry (supporting multiple arities per name) --------


      _export("FunctionRegistry", FunctionRegistry = class FunctionRegistry {
        constructor() {
          // name -> (arity -> FnMeta)
          this.byName = new Map();
          this.byId = [];
        }

        register(name, arity, impl) {
          if (!Number.isInteger(arity) || arity < 0) throw new Error('arity must be >= 0');
          var map = this.byName.get(name);

          if (!map) {
            map = new Map();
            this.byName.set(name, map);
          }

          var existing = map.get(arity);

          if (existing) {
            existing.impl = impl;
            return existing;
          }

          var meta = {
            id: this.byId.length,
            name,
            arity,
            impl
          };
          map.set(arity, meta);
          this.byId.push(meta);
          return meta;
        }

        getByNameAndArity(name, arity) {
          var map = this.byName.get(name);
          return map ? map.get(arity) : undefined;
        } // convenience: get any meta for a name when only one arity exists


        getSingleByName(name) {
          var map = this.byName.get(name);
          if (!map) return undefined;
          if (map.size === 1) return Array.from(map.values())[0];
          return undefined;
        }

        getById(id) {
          return this.byId[id];
        }

      }); // ---------------- Bytecode VM ----------------


      OpCode = /*#__PURE__*/function (OpCode) {
        OpCode[OpCode["PUSH_CONST"] = 0] = "PUSH_CONST";
        OpCode[OpCode["PUSH_VAR"] = 1] = "PUSH_VAR";
        OpCode[OpCode["ADD"] = 2] = "ADD";
        OpCode[OpCode["SUB"] = 3] = "SUB";
        OpCode[OpCode["MUL"] = 4] = "MUL";
        OpCode[OpCode["DIV"] = 5] = "DIV";
        OpCode[OpCode["MOD"] = 6] = "MOD";
        OpCode[OpCode["POW"] = 7] = "POW";
        OpCode[OpCode["NEG"] = 8] = "NEG";
        OpCode[OpCode["LT"] = 9] = "LT";
        OpCode[OpCode["LE"] = 10] = "LE";
        OpCode[OpCode["GT"] = 11] = "GT";
        OpCode[OpCode["GE"] = 12] = "GE";
        OpCode[OpCode["EQ"] = 13] = "EQ";
        OpCode[OpCode["NEQ"] = 14] = "NEQ";
        OpCode[OpCode["AND"] = 15] = "AND";
        OpCode[OpCode["OR"] = 16] = "OR";
        OpCode[OpCode["NOT"] = 17] = "NOT";
        OpCode[OpCode["CALL"] = 18] = "CALL";
        return OpCode;
      }(OpCode || {});

      _export("RPNProgram", RPNProgram = class RPNProgram {
        constructor(code, consts, varNames, registry, strictVars) {
          if (strictVars === void 0) {
            strictVars = true;
          }

          this.code = void 0;
          this.consts = void 0;
          this.varNames = void 0;
          this.registry = void 0;
          this.strictVars = void 0;
          this.code = code;
          this.consts = consts;
          this.varNames = varNames;
          this.registry = registry;
          this.strictVars = strictVars;
        }

        evaluate(ctx) {
          var varVals = new Array(this.varNames.length);

          for (var i = 0; i < this.varNames.length; i++) {
            var _name = this.varNames[i];
            var v = ctx[_name];

            if (v === undefined) {
              if (this.strictVars) throw new Error("Missing variable '" + _name + "' in context.");
              varVals[i] = 0;
            } else {
              varVals[i] = v;
            }
          }

          var stack = [];
          var code = this.code;
          var ip = 0;

          while (ip < code.length) {
            switch (code[ip++]) {
              case OpCode.PUSH_CONST:
                {
                  var idx = code[ip++];
                  stack.push(this.consts[idx]);
                  break;
                }

              case OpCode.PUSH_VAR:
                {
                  var _idx = code[ip++];
                  stack.push(varVals[_idx]);
                  break;
                }

              case OpCode.ADD:
                {
                  var b = Number(stack.pop());
                  var a = Number(stack.pop());
                  stack.push(a + b);
                  break;
                }

              case OpCode.SUB:
                {
                  var _b = Number(stack.pop());

                  var _a = Number(stack.pop());

                  stack.push(_a - _b);
                  break;
                }

              case OpCode.MUL:
                {
                  var _b2 = Number(stack.pop());

                  var _a2 = Number(stack.pop());

                  stack.push(_a2 * _b2);
                  break;
                }

              case OpCode.DIV:
                {
                  var _b3 = Number(stack.pop());

                  var _a3 = Number(stack.pop());

                  stack.push(_a3 / _b3);
                  break;
                }

              case OpCode.MOD:
                {
                  var _b4 = Number(stack.pop());

                  var _a4 = Number(stack.pop());

                  stack.push(_a4 % _b4);
                  break;
                }

              case OpCode.POW:
                {
                  var _b5 = Number(stack.pop());

                  var _a5 = Number(stack.pop());

                  stack.push(Math.pow(_a5, _b5));
                  break;
                }

              case OpCode.NEG:
                {
                  var _a6 = Number(stack.pop());

                  stack.push(-_a6);
                  break;
                }

              case OpCode.LT:
                {
                  var _b6 = Number(stack.pop());

                  var _a7 = Number(stack.pop());

                  stack.push(_a7 < _b6);
                  break;
                }

              case OpCode.LE:
                {
                  var _b7 = Number(stack.pop());

                  var _a8 = Number(stack.pop());

                  stack.push(_a8 <= _b7);
                  break;
                }

              case OpCode.GT:
                {
                  var _b8 = Number(stack.pop());

                  var _a9 = Number(stack.pop());

                  stack.push(_a9 > _b8);
                  break;
                }

              case OpCode.GE:
                {
                  var _b9 = Number(stack.pop());

                  var _a10 = Number(stack.pop());

                  stack.push(_a10 >= _b9);
                  break;
                }

              case OpCode.EQ:
                {
                  var _b10 = stack.pop();

                  var _a11 = stack.pop();

                  stack.push(_a11 === _b10);
                  break;
                }

              case OpCode.NEQ:
                {
                  var _b11 = stack.pop();

                  var _a12 = stack.pop();

                  stack.push(_a12 !== _b11);
                  break;
                }

              case OpCode.AND:
                {
                  var _b12 = Boolean(stack.pop());

                  var _a13 = Boolean(stack.pop());

                  stack.push(_a13 && _b12);
                  break;
                }

              case OpCode.OR:
                {
                  var _b13 = Boolean(stack.pop());

                  var _a14 = Boolean(stack.pop());

                  stack.push(_a14 || _b13);
                  break;
                }

              case OpCode.NOT:
                {
                  var _a15 = Boolean(stack.pop());

                  stack.push(!_a15);
                  break;
                }

              case OpCode.CALL:
                {
                  var fnId = code[ip++];
                  var arity = code[ip++];

                  var _args = new Array(arity);

                  for (var _i = arity - 1; _i >= 0; _i--) _args[_i] = stack.pop();

                  var meta = this.registry.getById(fnId);
                  var out = meta.impl(_args);
                  stack.push(out);
                  break;
                }

              default:
                throw new Error("Unknown opcode " + code[ip - 1]);
            }
          }

          if (stack.length !== 1) throw new Error("Invalid program: stack has " + stack.length + " values.");
          return stack[0];
        }

        getVariables() {
          return this.varNames;
        }

      }); // ---------------- Compiler from RPN tokens to bytecode ----------------


      NUM_RE = /^(?:[-+]?\d+(?:\.\d+)?)$/i;
      OPS = {
        // unary '!' handled separately; unary '-' will be 'neg'
        '^': {
          prec: 7,
          assoc: 'right'
        },
        'neg': {
          prec: 6,
          assoc: 'right',
          unary: true
        },
        '!': {
          prec: 6,
          assoc: 'right',
          unary: true
        },
        '*': {
          prec: 5,
          assoc: 'left'
        },
        '/': {
          prec: 5,
          assoc: 'left'
        },
        '%': {
          prec: 5,
          assoc: 'left'
        },
        '+': {
          prec: 4,
          assoc: 'left'
        },
        '-': {
          prec: 4,
          assoc: 'left'
        },
        '<': {
          prec: 3,
          assoc: 'left'
        },
        '<=': {
          prec: 3,
          assoc: 'left'
        },
        '>': {
          prec: 3,
          assoc: 'left'
        },
        '>=': {
          prec: 3,
          assoc: 'left'
        },
        '==': {
          prec: 2,
          assoc: 'left'
        },
        '!=': {
          prec: 2,
          assoc: 'left'
        },
        '&&': {
          prec: 1,
          assoc: 'left'
        },
        '||': {
          prec: 0,
          assoc: 'left'
        }
      };

      _export("RPNCalculator", RPNCalculator = class RPNCalculator {
        constructor(registerBuiltins) {
          if (registerBuiltins === void 0) {
            registerBuiltins = true;
          }

          this.registry = new FunctionRegistry();
          if (registerBuiltins) this.registerBuiltins();
        }

        compile(expr, opts) {
          var tokens;
          if (Array.isArray(expr)) tokens = expr;else {
            tokens = infixToRPN(expr, this.registry);
          }
          return compileRPNInternal(tokens, this.registry, opts);
        }

        register(name, arity, impl) {
          this.registry.register(name, arity, impl);
        }

        registerBuiltins() {
          var r = this.registry; // unary

          r.register('abs', 1, _ref => {
            var [a] = _ref;
            return Math.abs(Number(a));
          });
          r.register('floor', 1, _ref2 => {
            var [a] = _ref2;
            return Math.floor(Number(a));
          });
          r.register('ceil', 1, _ref3 => {
            var [a] = _ref3;
            return Math.ceil(Number(a));
          });
          r.register('round', 1, _ref4 => {
            var [a] = _ref4;
            return Math.round(Number(a));
          });
          r.register('sin', 1, _ref5 => {
            var [a] = _ref5;
            return Math.sin(Number(a));
          });
          r.register('cos', 1, _ref6 => {
            var [a] = _ref6;
            return Math.cos(Number(a));
          });
          r.register('tan', 1, _ref7 => {
            var [a] = _ref7;
            return Math.tan(Number(a));
          }); // min/max 2-arity

          r.register('min', 2, _ref8 => {
            var [a, b] = _ref8;
            return Math.min(Number(a), Number(b));
          });
          r.register('max', 2, _ref9 => {
            var [a, b] = _ref9;
            return Math.max(Number(a), Number(b));
          }); // clamp(x, lo, hi)

          r.register('clamp', 3, _ref10 => {
            var [x, lo, hi] = _ref10;
            return Math.max(Number(lo), Math.min(Number(hi), Number(x)));
          }); // rand: support rand() and rand(a,b)

          r.register('rand', 0, () => Math.random());
          r.register('rand', 2, _ref11 => {
            var [a, b] = _ref11;
            var A = Number(a),
                B = Number(b);
            return A + Math.random() * (B - A);
          }); // select ternary: sel(cond, a, b)

          r.register('sel', 3, _ref12 => {
            var [cond, a, b] = _ref12;
            return cond ? a : b;
          });
          r.register('bool', 1, _ref13 => {
            var [x] = _ref13;
            return Boolean(x);
          }); // lerp

          r.register('lerp', 3, _ref14 => {
            var [a, b, t] = _ref14;
            return Number(a) + (Number(b) - Number(a)) * Number(t);
          });
        }

      }); // ---------------- Example usage ----------------

      /*
      const calc = new RPNCalculator();
      
      const p1 = calc.compile('(a + b) * 2');
      console.log('vars:', p1.getVariables()); // [ 'a', 'b' ]
      console.log('eval1', p1.evaluate({ a: 3, b: 4 })); // 14
      
      const p2 = calc.compile('sin(x) + cos(y)');
      console.log('eval2', p2.evaluate({ x: Math.PI/2, y: 0 })); // 2
      
      const p3 = calc.compile('rand(0, 100) + min(3,5) * max(7,9)');
      console.log('eval3', p3.evaluate({})); // rand + 3*9
      
      // custom function
      const calc2 = new RPNCalculator();
      calc2.register('sum3', 3, ([a,b,c]) => Number(a)+Number(b)+Number(c));
      const p4 = calc2.compile('sum3(1,2,3) * 2');
      console.log('eval4', p4.evaluate({})); // 12
      
      // unary minus
      const p5 = calc.compile('-a * 5');
      console.log('eval5', p5.evaluate({ a: 2 })); // -10
      */
      // Export default if you like


      _export("default", RPNCalculator);

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=22ab373c383a7049dfc1a727e3e8b1bdd78f9407.js.map