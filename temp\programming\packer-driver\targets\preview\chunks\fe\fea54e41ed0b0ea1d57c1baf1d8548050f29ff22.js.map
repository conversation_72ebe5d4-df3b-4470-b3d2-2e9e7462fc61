{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/data/bullet/EventConditionType.ts"], "names": ["_decorator", "ccclass", "property", "eEmitterCondition", "eBulletCondition", "eEmitterConditionCn", "eBulletConditionCn"], "mappings": ";;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBF,U;;mCAElBG,iB,0BAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;eAAAA,iB;;;kCA4DAC,gB,0BAAAA,gB;AAAAA,QAAAA,gB,CAAAA,gB;AAAAA,QAAAA,gB,CAAAA,gB;AAAAA,QAAAA,gB,CAAAA,gB;AAAAA,QAAAA,gB,CAAAA,gB;AAAAA,QAAAA,gB,CAAAA,gB;AAAAA,QAAAA,gB,CAAAA,gB;AAAAA,QAAAA,gB,CAAAA,gB;AAAAA,QAAAA,gB,CAAAA,gB;AAAAA,QAAAA,gB,CAAAA,gB;AAAAA,QAAAA,gB,CAAAA,gB;AAAAA,QAAAA,gB,CAAAA,gB;AAAAA,QAAAA,gB,CAAAA,gB;AAAAA,QAAAA,gB,CAAAA,gB;AAAAA,QAAAA,gB,CAAAA,gB;AAAAA,QAAAA,gB,CAAAA,gB;AAAAA,QAAAA,gB,CAAAA,gB;eAAAA,gB;;;AAqBZ;qCACYC,mB,0BAAAA,mB;AAAAA,QAAAA,mB,CAAAA,mB;AAAAA,QAAAA,mB,CAAAA,mB;AAAAA,QAAAA,mB,CAAAA,mB;AAAAA,QAAAA,mB,CAAAA,mB;AAAAA,QAAAA,mB,CAAAA,mB;AAAAA,QAAAA,mB,CAAAA,mB;AAAAA,QAAAA,mB,CAAAA,mB;AAAAA,QAAAA,mB,CAAAA,mB;AAAAA,QAAAA,mB,CAAAA,mB;AAAAA,QAAAA,mB,CAAAA,mB;AAAAA,QAAAA,mB,CAAAA,mB;AAAAA,QAAAA,mB,CAAAA,mB;AAAAA,QAAAA,mB,CAAAA,mB;AAAAA,QAAAA,mB,CAAAA,mB;AAAAA,QAAAA,mB,CAAAA,mB;AAAAA,QAAAA,mB,CAAAA,mB;AAAAA,QAAAA,mB,CAAAA,mB;AAAAA,QAAAA,mB,CAAAA,mB;AAAAA,QAAAA,mB,CAAAA,mB;AAAAA,QAAAA,mB,CAAAA,mB;AAAAA,QAAAA,mB,CAAAA,mB;AAAAA,QAAAA,mB,CAAAA,mB;AAAAA,QAAAA,mB,CAAAA,mB;AAAAA,QAAAA,mB,CAAAA,mB;AAAAA,QAAAA,mB,CAAAA,mB;AAAAA,QAAAA,mB,CAAAA,mB;AAAAA,QAAAA,mB,CAAAA,mB;AAAAA,QAAAA,mB,CAAAA,mB;AAAAA,QAAAA,mB,CAAAA,mB;AAAAA,QAAAA,mB,CAAAA,mB;AAAAA,QAAAA,mB,CAAAA,mB;AAAAA,QAAAA,mB,CAAAA,mB;AAAAA,QAAAA,mB,CAAAA,mB;AAAAA,QAAAA,mB,CAAAA,mB;AAAAA,QAAAA,mB,CAAAA,mB;AAAAA,QAAAA,mB,CAAAA,mB;AAAAA,QAAAA,mB,CAAAA,mB;AAAAA,QAAAA,mB,CAAAA,mB;AAAAA,QAAAA,mB,CAAAA,mB;AAAAA,QAAAA,mB,CAAAA,mB;AAAAA,QAAAA,mB,CAAAA,mB;AAAAA,QAAAA,mB,CAAAA,mB;AAAAA,QAAAA,mB,CAAAA,mB;AAAAA,QAAAA,mB,CAAAA,mB;AAAAA,QAAAA,mB,CAAAA,mB;AAAAA,QAAAA,mB,CAAAA,mB;AAAAA,QAAAA,mB,CAAAA,mB;AAAAA,QAAAA,mB,CAAAA,mB;eAAAA,mB;;;oCAuDAC,kB,0BAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;eAAAA,kB", "sourcesContent": ["import { _decorator, error, v2, Vec2, Prefab, Enum } from \"cc\";\r\nconst { ccclass, property } = _decorator;\r\n\r\nexport enum eEmitterCondition {\r\n    Emitter_Active = 1,         // 发射器是否启用\r\n    Emitter_InitialDelay,       // 发射器当前的初始延迟\r\n    Emitter_Prewarm,            // 发射器是否启用预热\r\n    Emitter_PrewarmDuration,    // 发射器预热的持续时间\r\n    Emitter_Duration,           // 发射器配置的持续时间\r\n    Emitter_ElapsedTime,        // 发射器已运行的时间\r\n    Emitter_Loop,               // 发射器是否循环\r\n    Emitter_LoopInterval,       // 发射器循环的间隔时间\r\n\r\n    Emitter_EmitInterval,       // 发射器开火间隔\r\n\r\n    Emitter_PerEmitCount,       // 发射器开火次数\r\n    Emitter_PerEmitInterval,    // 发射器单次开火间隔\r\n    Emitter_PerEmitOffsetX,     // 发射器单次开火偏移\r\n\r\n    Emitter_Angle,              // 发射器弹道角度\r\n    Emitter_Count,              // 发射器弹道数量\r\n\r\n    Bullet_Duration,\r\n    Bullet_Speed,\r\n    Bullet_Acceleration,\r\n    Bullet_AccelerationAngle,\r\n    Bullet_FacingMoveDir,\r\n    Bullet_TrackingTarget,\r\n    Bullet_Destructive,\r\n    Bullet_DestructiveOnHit,\r\n\r\n    // 思考: 这几个是不是直接判断子弹id得了\r\n    Bullet_Sprite,          \r\n    Bullet_Scale,\r\n    Bullet_ColorR,\r\n    Bullet_ColorG,\r\n    Bullet_ColorB,\r\n    Bullet_DefaultFacing,   // 子弹外观初始朝向\r\n\r\n    Level_Duration,         // 已持续时间\r\n    Level_Distance,         // 已飞行距离\r\n    Level_InfLevel,         // 无尽模式当前关卡等级\r\n    Level_ChallengeLevel,   // 闯关模式当前等级\r\n\r\n    Player_ActLevel,        // 玩家账号等级\r\n    Player_PosX,            // 玩家当前坐标X\r\n    Player_PosY,            // 玩家当前坐标Y\r\n    Player_LifePercent,     // 玩家当前生命百分比\r\n    Player_GainBuff,        // 玩家获得buff\r\n\r\n    Unit_Life,              // 单位当前生命值\r\n    Unit_LifePercent,       // 单位当前生命百分比\r\n    Unit_ElapsedTime,       // 单位当前持续时间\r\n    Unit_PosX,              // 单位当前坐标X\r\n    Unit_PosY,              // 单位当前坐标Y\r\n    Unit_Speed,             // 单位当前速度\r\n    Unit_SpeedAngle,        // 单位当前速度角度\r\n    Unit_Acceleration,      // 单位当前加速度\r\n    Unit_AccelerationAngle, // 单位当前加速度角度\r\n    Unit_DistanceToPlayer,  // 单位与玩家的距离\r\n    Unit_AngleToPlayer,     // 单位与玩家的角度\r\n}\r\n\r\nexport enum eBulletCondition {\r\n    Bullet_Duration = 100,\r\n    Bullet_ElapsedTime,\r\n    Bullet_PosX,\r\n    Bullet_PosY,\r\n    Bullet_Damage,\r\n    Bullet_Speed,\r\n    Bullet_SpeedAngle,\r\n    Bullet_Acceleration,\r\n    Bullet_AccelerationAngle,\r\n    Bullet_Scale,\r\n    Bullet_ColorR,\r\n    Bullet_ColorG,\r\n    Bullet_ColorB,\r\n    Bullet_FacingMoveDir,\r\n    Bullet_Destructive,\r\n    Bullet_DestructiveOnHit,\r\n}\r\n\r\nexport type eEventConditionType = eEmitterCondition | eBulletCondition;\r\n\r\n// 以下枚举值用于编辑器显示，实际运行时不会用到\r\nexport enum eEmitterConditionCn {\r\n    发射器是否启用 = eEmitterCondition.Emitter_Active,\r\n    发射器当前的初始延迟 = eEmitterCondition.Emitter_InitialDelay,\r\n    发射器是否预热 = eEmitterCondition.Emitter_Prewarm,\r\n    发射器预热的持续时间 = eEmitterCondition.Emitter_PrewarmDuration,\r\n    发射器的持续时间 = eEmitterCondition.Emitter_Duration,\r\n    发射器已运行的时间 = eEmitterCondition.Emitter_ElapsedTime,\r\n    发射器是否循环 = eEmitterCondition.Emitter_Loop,\r\n    发射器循环的间隔时间 = eEmitterCondition.Emitter_LoopInterval,\r\n    发射器开火间隔 = eEmitterCondition.Emitter_EmitInterval,\r\n    发射器单次开火次数 = eEmitterCondition.Emitter_PerEmitCount,\r\n    发射器单次开火间隔 = eEmitterCondition.Emitter_PerEmitInterval,\r\n    发射器单次开火偏移 = eEmitterCondition.Emitter_PerEmitOffsetX,\r\n    发射器弹道角度 = eEmitterCondition.Emitter_Angle,\r\n    发射器弹道数量 = eEmitterCondition.Emitter_Count,\r\n\r\n    子弹持续时间 = eEmitterCondition.Bullet_Duration,\r\n    子弹速度 = eEmitterCondition.Bullet_Speed,\r\n    子弹加速度 = eEmitterCondition.Bullet_Acceleration,\r\n    子弹加速度角度 = eEmitterCondition.Bullet_AccelerationAngle,\r\n    子弹面向移动方向 = eEmitterCondition.Bullet_FacingMoveDir,\r\n    子弹追踪目标 = eEmitterCondition.Bullet_TrackingTarget,\r\n    子弹可破坏 = eEmitterCondition.Bullet_Destructive,\r\n    子弹命中时破坏 = eEmitterCondition.Bullet_DestructiveOnHit,\r\n    子弹外观图片 = eEmitterCondition.Bullet_Sprite,\r\n    子弹缩放 = eEmitterCondition.Bullet_Scale,\r\n    子弹颜色R = eEmitterCondition.Bullet_ColorR,\r\n    子弹颜色G = eEmitterCondition.Bullet_ColorG,\r\n    子弹颜色B = eEmitterCondition.Bullet_ColorB,\r\n    子弹外观初始朝向 = eEmitterCondition.Bullet_DefaultFacing,\r\n\r\n    关卡已持续时间 = eEmitterCondition.Level_Duration,\r\n    关卡已飞行距离 = eEmitterCondition.Level_Distance,\r\n    无尽模式当前等级 = eEmitterCondition.Level_InfLevel,\r\n    闯关模式当前等级 = eEmitterCondition.Level_ChallengeLevel,\r\n\r\n    玩家账号等级 = eEmitterCondition.Player_ActLevel,\r\n    玩家当前坐标X = eEmitterCondition.Player_PosX,\r\n    玩家当前坐标Y = eEmitterCondition.Player_PosY,\r\n    玩家当前生命百分比 = eEmitterCondition.Player_LifePercent,\r\n    玩家获得buff = eEmitterCondition.Player_GainBuff,\r\n\r\n    单位当前生命值 = eEmitterCondition.Unit_Life,\r\n    单位当前生命百分比 = eEmitterCondition.Unit_LifePercent,\r\n    单位当前持续时间 = eEmitterCondition.Unit_ElapsedTime,\r\n    单位当前坐标X = eEmitterCondition.Unit_PosX,\r\n    单位当前坐标Y = eEmitterCondition.Unit_PosY,\r\n    单位当前速度 = eEmitterCondition.Unit_Speed,\r\n    单位当前速度角度 = eEmitterCondition.Unit_SpeedAngle,\r\n    单位当前加速度 = eEmitterCondition.Unit_Acceleration,\r\n    单位当前加速度角度 = eEmitterCondition.Unit_AccelerationAngle,\r\n    单位与玩家的距离 = eEmitterCondition.Unit_DistanceToPlayer,\r\n    单位与玩家的角度 = eEmitterCondition.Unit_AngleToPlayer,\r\n}\r\n\r\nexport enum eBulletConditionCn {\r\n    子弹持续时间 = eBulletCondition.Bullet_Duration,\r\n    子弹已运行时间 = eBulletCondition.Bullet_ElapsedTime,\r\n    子弹坐标X = eBulletCondition.Bullet_PosX,\r\n    子弹坐标Y = eBulletCondition.Bullet_PosY,\r\n    子弹伤害 = eBulletCondition.Bullet_Damage,\r\n    子弹速度 = eBulletCondition.Bullet_Speed,\r\n    子弹速度角度 = eBulletCondition.Bullet_SpeedAngle,\r\n    子弹加速度 = eBulletCondition.Bullet_Acceleration,\r\n    子弹加速度角度 = eBulletCondition.Bullet_AccelerationAngle,\r\n    子弹缩放 = eBulletCondition.Bullet_Scale,\r\n    子弹颜色R = eBulletCondition.Bullet_ColorR,\r\n    子弹颜色G = eBulletCondition.Bullet_ColorG,\r\n    子弹颜色B = eBulletCondition.Bullet_ColorB,\r\n    子弹面向移动方向 = eBulletCondition.Bullet_FacingMoveDir,\r\n    子弹可破坏 = eBulletCondition.Bullet_Destructive,\r\n    子弹命中时破坏 = eBulletCondition.Bullet_DestructiveOnHit,\r\n}"]}