// Debug the stack bug in RPN expression
const fs = require('fs');

// Compile TypeScript to JavaScript for testing
const { execSync } = require('child_process');
try {
  execSync('npx tsc assets/scripts/Game/utils/RPN.ts --target es2020 --module commonjs --outDir temp', { stdio: 'inherit' });
} catch (error) {
  console.error('Failed to compile TypeScript:', error.message);
  process.exit(1);
}

const { RPNCalculator } = require('./temp/RPN.js');

console.log('=== Debugging Stack Bug ===\n');

const calc = new RPNCalculator();

// The problematic expression
const expr = 'sel(b==5, -a * 5, a * 5)';
console.log(`Problematic expression: ${expr}`);

// Let's trace the tokenization and RPN conversion process
console.log('\n1. Tokenization:');

// Manually tokenize to see the tokens
function tokenize(expr) {
  const out = [];
  const re = /\s*([0-9]*\.?[0-9]+|[A-Za-z_][A-Za-z0-9_.]*|<=|>=|==|!=|&&|\|\||[+\-*/%^(),!<>])\s*/g;
  let m;
  while ((m = re.exec(expr)) !== null) {
    const s = m[1];
    if (/^[0-9]*\.?[0-9]+$/.test(s)) out.push({type:'number', value:s});
    else if (/^true|false$/i.test(s)) out.push({type:'bool', value:s});
    else if (s === '(') out.push({type:'lparen', value:s});
    else if (s === ')') out.push({type:'rparen', value:s});
    else if (s === ',') out.push({type:'comma', value:s});
    else if (/^[+\-*/%^!<>]=?$/.test(s) || s === '&&' || s === '||') out.push({type:'op', value:s});
    else out.push({type:'ident', value:s});
  }
  return out;
}

const tokens = tokenize(expr);
tokens.forEach((token, i) => {
  console.log(`  ${i}: ${token.type} = "${token.value}"`);
});

console.log('\n2. Manual RPN conversion simulation:');

// Simulate the RPN conversion process
const output = [];
const opStack = [];

for (let i = 0; i < tokens.length; i++) {
  const t = tokens[i];
  console.log(`\nProcessing token ${i}: ${t.type} = "${t.value}"`);
  console.log(`  Stack before: [${opStack.map(op => `${op.type}:${op.value}`).join(', ')}]`);
  console.log(`  Output before: [${output.join(', ')}]`);
  
  if (t.type === 'number' || t.type === 'bool') {
    output.push(t.value);
    console.log(`    -> Added to output: ${t.value}`);
    continue;
  }

  if (t.type === 'ident') {
    const next = tokens[i+1];
    if (next && next.type === 'lparen') {
      // Function detected
      opStack.push({type:'func', value: t.value, argCount: 1});
      console.log(`    -> Function detected: ${t.value}, argCount=1`);
    } else {
      // Variable
      output.push(t.value);
      console.log(`    -> Variable added to output: ${t.value}`);
    }
    continue;
  }

  if (t.type === 'comma') {
    // Pop operators until lparen
    while (opStack.length > 0 && opStack[opStack.length-1].type !== 'lparen') {
      const op = opStack.pop();
      if (op.type === 'op') {
        output.push(op.value);
        console.log(`    -> Popped operator to output: ${op.value}`);
      }
    }
    // Increment argCount of function
    for (let j = opStack.length - 1; j >= 0; j--) {
      if (opStack[j].type === 'lparen') {
        if (j > 0 && opStack[j-1].type === 'func') {
          opStack[j-1].argCount = (opStack[j-1].argCount || 1) + 1;
          console.log(`    -> Incremented ${opStack[j-1].value} argCount to ${opStack[j-1].argCount}`);
        }
        break;
      }
    }
    continue;
  }

  if (t.type === 'op') {
    const rawOp = t.value;
    // Detect unary minus
    const prev = tokens[i-1];
    const isUnary = rawOp === '-' && (
      i === 0 ||
      (prev && (prev.type === 'op' || prev.type === 'lparen' || prev.type === 'comma'))
    );

    const opKey = (isUnary && rawOp === '-') ? 'neg' : rawOp;
    console.log(`    -> Operator: ${rawOp} ${isUnary ? '(unary)' : ''} -> ${opKey}`);

    // Pop operators with higher or equal precedence
    const OPS = {
      '^': { prec: 7, assoc: 'right' },
      'neg': { prec: 6, assoc: 'right', unary: true },
      '!': { prec: 6, assoc: 'right', unary: true },
      '*': { prec: 5, assoc: 'left' },
      '/': { prec: 5, assoc: 'left' },
      '%': { prec: 5, assoc: 'left' },
      '+': { prec: 4, assoc: 'left' },
      '-': { prec: 4, assoc: 'left' },
      '<': { prec: 3, assoc: 'left' },
      '<=': { prec: 3, assoc: 'left' },
      '>': { prec: 3, assoc: 'left' },
      '>=': { prec: 3, assoc: 'left' },
      '==': { prec: 2, assoc: 'left' },
      '!=': { prec: 2, assoc: 'left' },
      '&&': { prec: 1, assoc: 'left' },
      '||': { prec: 0, assoc: 'left' },
    };

    const o1 = OPS[opKey];
    while (opStack.length > 0) {
      const top = opStack[opStack.length - 1];
      if (top.type !== 'op') break;
      const o2 = OPS[top.value];
      if (!o2) break;
      if ((o1.assoc === 'left' && o1.prec <= o2.prec) || (o1.assoc === 'right' && o1.prec < o2.prec)) {
        const popped = opStack.pop();
        output.push(popped.value);
        console.log(`    -> Popped higher precedence operator: ${popped.value}`);
      } else break;
    }
    opStack.push({type:'op', value: opKey});
    console.log(`    -> Pushed operator to stack: ${opKey}`);
    continue;
  }

  if (t.type === 'lparen') {
    opStack.push({type:'lparen', value:'('});
    console.log(`    -> Pushed lparen to stack`);
    continue;
  }

  if (t.type === 'rparen') {
    // Pop until lparen
    console.log(`    -> Processing rparen, popping until lparen...`);
    while (opStack.length > 0 && opStack[opStack.length-1].type !== 'lparen') {
      const op = opStack.pop();
      if (op.type === 'op') {
        output.push(op.value);
        console.log(`      -> Popped operator to output: ${op.value}`);
      }
    }
    
    if (opStack.length === 0) {
      console.log(`    -> ERROR: Mismatched parentheses`);
      break;
    }
    
    // Pop the lparen
    opStack.pop();
    console.log(`    -> Popped lparen`);
    
    // Check for function
    if (opStack.length > 0 && opStack[opStack.length-1].type === 'func') {
      const fn = opStack.pop();
      
      // Check if empty function call
      const before = tokens[i-1];
      let finalArity;
      if (before && before.type === 'lparen') {
        finalArity = 0;
        console.log(`    -> Empty function call detected`);
      } else {
        finalArity = fn.argCount || 1;
      }
      
      const funcToken = `FUNC:${fn.value}:${finalArity}`;
      output.push(funcToken);
      console.log(`    -> Function call complete: ${funcToken}`);
    }
    continue;
  }

  console.log(`  Stack after: [${opStack.map(op => `${op.type}:${op.value}`).join(', ')}]`);
  console.log(`  Output after: [${output.join(', ')}]`);
}

// Pop remaining operators
console.log('\n3. Popping remaining operators:');
while (opStack.length > 0) {
  const op = opStack.pop();
  if (op.type === 'op') {
    output.push(op.value);
    console.log(`  -> Final pop: ${op.value}`);
  }
}

console.log(`\nFinal RPN output: [${output.join(', ')}]`);

// Now let's test the actual compilation
console.log('\n4. Testing actual compilation:');
try {
  const program = calc.compile(expr);
  console.log('✓ Compilation successful');
  
  const context = { a: 2, b: 5 };
  console.log(`Context: ${JSON.stringify(context)}`);
  
  const result = program.evaluate(context);
  console.log(`✓ Evaluation successful: ${result}`);
  
} catch (error) {
  console.log(`✗ Error: ${error.message}`);
  
  // Let's try to debug by testing simpler expressions
  console.log('\n5. Testing simpler expressions:');
  
  const simpleTests = [
    'b==5',
    '-a * 5', 
    'a * 5',
    'sel(true, 1, 2)',
    'sel(b==5, 1, 2)'
  ];
  
  simpleTests.forEach(testExpr => {
    try {
      const prog = calc.compile(testExpr);
      const result = prog.evaluate({ a: 2, b: 5 });
      console.log(`  ✓ "${testExpr}" = ${result}`);
    } catch (err) {
      console.log(`  ✗ "${testExpr}" failed: ${err.message}`);
    }
  });
}

console.log('\n✓ Debug analysis completed!');

// Cleanup
try {
  execSync('Remove-Item -Recurse -Force temp', { stdio: 'inherit' });
} catch (e) {
  // Ignore cleanup errors
}
