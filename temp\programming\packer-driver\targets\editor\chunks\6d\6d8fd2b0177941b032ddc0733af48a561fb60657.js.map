{"version": 3, "sources": ["cce:/internal/x/prerequisite-imports"], "names": ["requests", "request", "_err"], "mappings": ";;;;;;AACA;AAEA,YAAM,CAAC,YAAY;AACf,cAAMA,QAAQ,GAAG,CAAC,uCAAD,EAA4J,uCAA5J,EAA4T,uCAA5T,EAAge,uCAAhe,EAAioB,uCAAjoB,EAA4xB,uCAA5xB,EAAg7B,uCAAh7B,EAAm/B,uCAAn/B,EAAykC,uCAAzkC,EAAwqC,uCAAxqC,EAAmwC,wCAAnwC,EAA01C,wCAA11C,EAA47C,wCAA57C,EAAyhD,wCAAzhD,EAAonD,wCAApnD,EAAstD,wCAAttD,EAAszD,wCAAtzD,EAAi5D,wCAAj5D,EAAq/D,wCAAr/D,EAA0kE,wCAA1kE,EAAmqE,wCAAnqE,EAAwvE,wCAAxvE,EAA41E,wCAA51E,EAA27E,wCAA37E,EAAqhF,wCAArhF,EAAknF,wCAAlnF,EAAqsF,wCAArsF,EAAkyF,wCAAlyF,EAA84F,wCAA94F,EAA8/F,wCAA9/F,EAAwmG,wCAAxmG,EAAktG,wCAAltG,EAA4zG,wCAA5zG,EAA85G,wCAA95G,EAAogH,wCAApgH,EAAklH,wCAAllH,EAAsqH,wCAAtqH,EAAovH,wCAApvH,EAAs0H,wCAAt0H,EAAg6H,wCAAh6H,EAAu/H,wCAAv/H,EAA2kI,wCAA3kI,EAAgqI,wCAAhqI,EAA0vI,wCAA1vI,EAA40I,wCAA50I,EAAi6I,wCAAj6I,EAAw/I,wCAAx/I,EAA2kJ,wCAA3kJ,EAA6pJ,wCAA7pJ,EAAovJ,wCAApvJ,EAAw0J,wCAAx0J,EAA25J,wCAA35J,EAA4+J,wCAA5+J,EAAskK,wCAAtkK,EAAqqK,wCAArqK,EAAswK,wCAAtwK,EAA+1K,wCAA/1K,EAA67K,wCAA77K,EAA4hL,wCAA5hL,EAA2nL,wCAA3nL,EAA8tL,wCAA9tL,EAAyzL,wCAAzzL,EAAo5L,wCAAp5L,EAA2+L,wCAA3+L,EAAykM,wCAAzkM,EAAmqM,wCAAnqM,EAA8vM,wCAA9vM,EAAi1M,wCAAj1M,EAA86M,wCAA96M,EAA8gN,wCAA9gN,EAA8mN,wCAA9mN,EAAotN,wCAAptN,EAAszN,wCAAtzN,EAA+4N,wCAA/4N,EAA6/N,wCAA7/N,EAA2mO,wCAA3mO,EAAkuO,wCAAluO,EAA60O,wCAA70O,EAAg8O,wCAAh8O,EAAijP,wCAAjjP,EAAkoP,wCAAloP,EAAitP,wCAAjtP,EAA+xP,wCAA/xP,EAA82P,wCAA92P,EAA27P,wCAA37P,EAA8gQ,wCAA9gQ,EAAomQ,wCAApmQ,EAAurQ,wCAAvrQ,EAA2wQ,wCAA3wQ,EAA+1Q,wCAA/1Q,EAA86Q,wCAA96Q,EAAogR,wCAApgR,EAAulR,wCAAvlR,EAA8pR,wCAA9pR,EAA4uR,wCAA5uR,EAA6zR,wCAA7zR,EAA64R,wCAA74R,EAAs9R,wCAAt9R,EAAgiS,wCAAhiS,EAAymS,wCAAzmS,EAAksS,yCAAlsS,EAAuxS,yCAAvxS,EAAs2S,yCAAt2S,EAAs7S,yCAAt7S,EAAygT,yCAAzgT,EAA6lT,yCAA7lT,EAAgrT,yCAAhrT,EAA0wT,yCAA1wT,EAA+1T,yCAA/1T,EAAk8T,yCAAl8T,EAAsiU,yCAAtiU,EAAmoU,yCAAnoU,EAA4uU,yCAA5uU,EAAs1U,yCAAt1U,EAAy7U,yCAAz7U,EAAuhV,yCAAvhV,EAAwnV,yCAAxnV,EAAmtV,yCAAntV,EAAqzV,yCAArzV,EAAu5V,yCAAv5V,EAAq/V,yCAAr/V,EAA+kW,yCAA/kW,EAAgqW,yCAAhqW,EAAgvW,yCAAhvW,EAAw0W,yCAAx0W,EAAu5W,yCAAv5W,EAA6+W,yCAA7+W,EAA6jX,yCAA7jX,EAA6oX,yCAA7oX,EAA+tX,yCAA/tX,EAAwzX,yCAAxzX,EAA04X,yCAA14X,EAA09X,yCAA19X,EAA0iY,yCAA1iY,EAAkoY,yCAAloY,EAA2tY,yCAA3tY,EAAwzY,yCAAxzY,EAAw5Y,yCAAx5Y,EAAo/Y,yCAAp/Y,EAAilZ,yCAAjlZ,EAAkqZ,yCAAlqZ,EAAwvZ,yCAAxvZ,EAA+0Z,yCAA/0Z,EAAo6Z,yCAAp6Z,EAA2/Z,yCAA3/Z,EAAila,yCAAjla,EAA0qa,yCAA1qa,EAAowa,yCAApwa,EAA41a,yCAA51a,EAAq7a,yCAAr7a,EAAghb,yCAAhhb,EAA2mb,yCAA3mb,EAAqsb,yCAArsb,EAA2xb,yCAA3xb,EAAi3b,yCAAj3b,EAAs8b,yCAAt8b,EAAgic,yCAAhic,EAA+mc,yCAA/mc,EAA6rc,yCAA7rc,EAA8wc,yCAA9wc,EAAm2c,yCAAn2c,EAAq7c,yCAAr7c,EAAygd,yCAAzgd,EAAyld,yCAAzld,EAAgrd,yCAAhrd,EAAkwd,yCAAlwd,EAAu1d,yCAAv1d,EAA+6d,yCAA/6d,EAAsge,yCAAtge,EAAwle,yCAAxle,EAA6qe,yCAA7qe,EAAkwe,yCAAlwe,EAA21e,yCAA31e,EAA27e,yCAA37e,EAA+hf,yCAA/hf,EAA0nf,yCAA1nf,EAAutf,yCAAvtf,EAAwzf,yCAAxzf,EAAm5f,yCAAn5f,EAAy+f,yCAAz+f,EAA+jgB,yCAA/jgB,EAA0pgB,yCAA1pgB,EAA6ugB,yCAA7ugB,EAAi0gB,yCAAj0gB,EAAw5gB,yCAAx5gB,EAA4+gB,yCAA5+gB,EAAikhB,yCAAjkhB,EAAsphB,yCAAtphB,EAA0uhB,yCAA1uhB,EAAg0hB,yCAAh0hB,EAA05hB,yCAA15hB,EAAm/hB,yCAAn/hB,EAA8kiB,yCAA9kiB,EAA6qiB,yCAA7qiB,EAAixiB,yCAAjxiB,EAAg3iB,yCAAh3iB,EAAm9iB,yCAAn9iB,EAA4ijB,yCAA5ijB,EAAsojB,yCAAtojB,EAA8tjB,yCAA9tjB,EAA4yjB,yCAA5yjB,EAAu3jB,yCAAv3jB,EAAo8jB,yCAAp8jB,EAA+gkB,yCAA/gkB,EAAwmkB,yCAAxmkB,EAAsskB,yCAAtskB,EAAmykB,yCAAnykB,EAAi4kB,yCAAj4kB,EAA69kB,yCAA79kB,EAA8hlB,yCAA9hlB,EAAymlB,yCAAzmlB,EAA4qlB,yCAA5qlB,EAA8ulB,yCAA9ulB,EAAyzlB,yCAAzzlB,EAA04lB,yCAA14lB,EAA+9lB,yCAA/9lB,EAAojmB,yCAApjmB,EAAoomB,yCAApomB,EAAotmB,yCAAptmB,EAAsymB,yCAAtymB,EAAy3mB,yCAAz3mB,EAAk8mB,yCAAl8mB,EAAqhnB,yCAArhnB,EAAsmnB,yCAAtmnB,EAA0rnB,yCAA1rnB,EAAiynB,yCAAjynB,EAAq5nB,yCAAr5nB,EAAqgoB,yCAArgoB,EAAgnoB,yCAAhnoB,EAA6soB,yCAA7soB,EAA6xoB,yCAA7xoB,EAAi4oB,yCAAj4oB,EAA0+oB,yCAA1+oB,EAAilpB,yCAAjlpB,EAAyrpB,yCAAzrpB,EAAkxpB,yCAAlxpB,EAA41pB,yCAA51pB,EAAq6pB,yCAAr6pB,EAA0+pB,yCAA1+pB,EAAsjqB,yCAAtjqB,CAAjB;;AACA,aAAK,MAAMC,OAAX,IAAsBD,QAAtB,EAAgC;AAC5B,cAAI;AACA,kBAAMC,OAAO,EAAb;AACH,WAFD,CAEE,OAAOC,IAAP,EAAa,CACX;AACH;AACJ;AACJ,OATK,GAAN", "sourcesContent": ["\n// Auto generated represents the prerequisite imports of project modules.\n\nawait (async () => {\n    const requests = [() => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts\"), () => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts\"), () => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts\"), () => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts\"), () => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts\"), () => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/Bundle.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/const/AttributeConst.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/data/DataManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/data/bag/Bag.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/data/base/AttributeData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/data/base/BaseInfo.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/data/equip/Equip.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/data/equip/EquipCombine.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/data/equip/EquipSlots.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/data/fight/Rogue.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/data/game_level/GameLevel.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/data/gm/GM.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/data/mail/Mail.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/data/pk/PK.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/data/plane/PlaneCacheInfo.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/data/plane/PlaneData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/event/DataEvent.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/event/EventManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/Plane.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/TopBlockInputUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/components/SelectList/uiSelect.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/components/SelectList/uiSelectItem.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/components/button/ButtonPlus.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/components/button/DragButton.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/components/dropdown/DropDown.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/components/list/List.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/components/list/ListItem.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/gm/script/GmEntry.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/gm/script/ui/GmButtonUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/gm/script/ui/GmUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/home/<USER>/HomeEntry.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/home/<USER>/event/HomeUIEvent.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/home/<USER>/type/BottomTab.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/home/<USER>/ui/BottomUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/home/<USER>/ui/BuidingUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/home/<USER>/ui/BuildingInfoUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/home/<USER>/ui/HomeUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/home/<USER>/ui/MapModeUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/home/<USER>/ui/PlaneShowUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/home/<USER>/ui/PopupUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/home/<USER>/ui/ShopUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/home/<USER>/ui/SkyIslandUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/home/<USER>/ui/TalentUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/home/<USER>/ui/ToastUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/home/<USER>/ui/TopUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/home/<USER>/ui/WheelSpinnerUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/home/<USER>/ui/dialogue/DialogueUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/home/<USER>/ui/fight/RogueSelectIcon.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/home/<USER>/ui/fight/RogueUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/home/<USER>/ui/friend/FriendAddUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/home/<USER>/ui/friend/FriendCellUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/home/<USER>/ui/friend/FriendListUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/home/<USER>/ui/friend/FriendStrangerUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/home/<USER>/ui/friend/FriendUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/home/<USER>/ui/mail/MailCellUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/home/<USER>/ui/mail/MailUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/home/<USER>/ui/pk/PKHistoryCellUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/home/<USER>/ui/pk/PKHistoryUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/home/<USER>/ui/pk/PKRewardIcon.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/home/<USER>/ui/pk/PKUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/home_plane/script/HomePlaneEntry.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/home_plane/script/module/PlaneEvent.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/home_plane/script/module/PlaneTypes.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/home_plane/script/ui/PlaneCombineResultUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/home_plane/script/ui/PlaneEquipInfoUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/home_plane/script/ui/PlaneUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/home_plane/script/ui/components/back_pack/BagGrid.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/home_plane/script/ui/components/back_pack/BagItem.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/home_plane/script/ui/components/back_pack/SortTypeDropdown.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/home_plane/script/ui/components/back_pack/Tabs.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/home_plane/script/ui/components/display/CombineDisplay.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/home_plane/script/ui/components/display/EquipDisplay.ts\"), () => import(\"file:///E:/M2Game/Client/assets/editor/emitter/EmitterEditor.ts\"), () => import(\"file:///E:/M2Game/Client/assets/editor/gizmos/EmitterGizmo.ts\"), () => import(\"file:///E:/M2Game/Client/assets/editor/gizmos/GizmoDrawer.ts\"), () => import(\"file:///E:/M2Game/Client/assets/editor/gizmos/GizmoManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/editor/gizmos/GizmoUtils.ts\"), () => import(\"file:///E:/M2Game/Client/assets/editor/level/LevelEditorBaseUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/editor/level/LevelEditorCondition.ts\"), () => import(\"file:///E:/M2Game/Client/assets/editor/level/LevelEditorElemUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/editor/level/LevelEditorEventUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/editor/level/LevelEditorLayerUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/editor/level/LevelEditorUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/editor/level/LevelEditorWaveParam.ts\"), () => import(\"file:///E:/M2Game/Client/assets/editor/level/LevelEditorWaveUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/editor/level/utils.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/AAA/init_cs_proto.js\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/AutoGen/Luban/schema.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/AutoGen/PB/cs_proto.js\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ColliderTest.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/GameFunc.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/GameIns.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/bullet/BulletController.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/bullet/BulletSystem.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/bullet/Easing.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/bullet/Emitter.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/bullet/EventGroup.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/bullet/EventRunner.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/bullet/ObjectPool.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/bullet/PropertyContainer.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/bullet/StateMachine.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/bullet/actions/BulletEventActions.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/bullet/actions/EmitterEventActions.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/bullet/actions/IEventAction.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/bullet/conditions/BulletEventConditions.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/bullet/conditions/EmitterEventConditions.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/bullet/conditions/IEventCondition.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/collider-system/FBoxCollider.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/collider-system/FCircleCollider.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/collider-system/FCollider.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/collider-system/FColliderManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/collider-system/FPolygonCollider.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/collider-system/Intersection.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/collider-system/QuadTree.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/const/GameConst.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/const/GameEnum.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/const/GameResourceList.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/data/BossData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/data/BulletEventData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/data/EnemyData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/data/EnemyWave.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/data/GameMapData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/data/MainPlaneFightData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/data/MapItemData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/data/StageData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/data/TrackData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/data/bullet/BulletData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/data/bullet/EmitterData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/data/bullet/EventActionType.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/data/bullet/EventConditionType.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/data/bullet/EventGroupData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/data/bullet/ExpressionValue.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/level/LevelItem.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/level/LevelItemEvent.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/manager/BattleManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/manager/BossManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/manager/BulletManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/manager/EnemyManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/manager/GameDataManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/manager/GamePlaneManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/manager/GameResManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/manager/GameRuleManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/manager/GlobalDataManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/manager/HurtEffectManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/manager/MainPlaneManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/manager/SceneManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/manager/StageManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/manager/WaveManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/move/DefaultMoveModifier.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/move/IMovable.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/move/Movable.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/scenes/GameMain.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/base/AttackPoint.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/base/BaseComp.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/base/Controller.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/base/Entity.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/base/ImageSequence.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/base/NodeMove.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/base/PfFrameAnim.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/base/TrackComponent.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/base/UIAnimMethods.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/bullet/Bullet.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/bullet/BulletFly.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/bullet/BulletNew.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/bullet/CircleZoomFly.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/bulletDanmu/AimCircleScreen.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/bulletDanmu/AimSingleLineScreen.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/bulletDanmu/BaseScreen.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/bulletDanmu/CircleScreen.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/bulletDanmu/CircleZoomScreen.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/bulletDanmu/LoftScreen.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/layer/BattleLayer.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/layer/EffectLayer.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/layer/EnemyEffectLayer.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/map/GameMapRun.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/map/LevelBaseUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/map/LevelCondition.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/map/LevelElemUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/map/LevelEventUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/map/LevelLayerUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/map/LevelWaveUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/FightEntity.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/PlaneWithWeapon.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/boss/BossPlane.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyPlane.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyPlaneRole.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyShootComponent.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/mainPlane/FireShells.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/mainPlane/MainPlaneFight.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/skill/BuffComp.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/skill/SkillComp.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/weapon/Weapon.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/utils/Helper.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/utils/RPN.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/utils/Tools.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/wave/Wave.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/world/level/Baker/Baker.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/world/level/Baker/LevelBaker.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/world/level/Baker/PathBaker.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/world/level/Baker/PointBaker.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/world/level/Data/PathPoint.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/IMgr.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Luban/LubanMgr.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/MainUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/MyApp.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Network/NetMgr.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/PlatformSDK/DevLogin.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/PlatformSDK/DevLoginData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/PlatformSDK/IPlatformSDK.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/PlatformSDK/WXLogin.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ResUpdate/ResUpdate.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ResUpdate/RootPersist.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ResUpdate/audioManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Utils/Logger.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/core/base/PlaneManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/core/base/ResManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/core/base/SingletonBase.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/leveldata/condition/LevelDataEventCondtion.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/leveldata/condition/LevelDataEventCondtionDelayDistance.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/leveldata/condition/LevelDataEventCondtionDelayTime.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/leveldata/condition/LevelDataEventCondtionWave.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/leveldata/condition/newCondition.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/leveldata/leveldata.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/leveldata/trigger/LevelDataEventTrigger.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/leveldata/trigger/LevelDataEventTriggerAudio.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/leveldata/trigger/LevelDataEventTriggerLog.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/leveldata/trigger/LevelDataEventTriggerWave.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/leveldata/trigger/newTrigger.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ui/DevLoginUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ui/LoadingUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ui/UIMgr.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ui/game/MBoomUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ui/res/PlaneRes.ts\")];\n    for (const request of requests) {\n        try {\n            await request();\n        } catch (_err) {\n            // The error should have been caught by executor.\n        }\n    }\n})();\n    "]}