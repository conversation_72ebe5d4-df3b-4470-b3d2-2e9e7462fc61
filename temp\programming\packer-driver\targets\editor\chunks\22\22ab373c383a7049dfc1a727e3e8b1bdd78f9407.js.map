{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/utils/RPN.ts"], "names": ["FunctionRegistry", "RPNProgram", "RPNCalculator", "compileRPNInternal", "tokens", "registry", "opts", "code", "consts", "varIndex", "Map", "varNames", "pushConst", "v", "push", "OpCode", "PUSH_CONST", "length", "pushVar", "name", "idx", "get", "undefined", "set", "PUSH_VAR", "emitBinary", "op", "unknownIsVar", "unknownIdentifierIsVar", "strictVars", "raw", "t", "trim", "NUM_RE", "test", "parseFloat", "ADD", "SUB", "MUL", "DIV", "MOD", "POW", "NEG", "LT", "LE", "GT", "GE", "EQ", "NEQ", "AND", "OR", "NOT", "startsWith", "parts", "split", "arity", "parseInt", "meta", "getByNameAndArity", "Error", "CALL", "id", "tokenize", "expr", "out", "re", "m", "exec", "s", "type", "value", "infixToRPN", "output", "opStack", "i", "next", "argCount", "pop", "j", "rawOp", "prev", "isUnary", "op<PERSON>ey", "OPS", "o1", "top", "o2", "assoc", "prec", "fn", "before", "finalArity", "by<PERSON><PERSON>", "byId", "register", "impl", "Number", "isInteger", "map", "existing", "getSingleByName", "size", "Array", "from", "values", "getById", "constructor", "evaluate", "ctx", "varVals", "stack", "ip", "b", "a", "Math", "pow", "Boolean", "fnId", "args", "getVariables", "unary", "registerBuiltins", "compile", "isArray", "r", "abs", "floor", "ceil", "round", "sin", "cos", "tan", "min", "max", "x", "lo", "hi", "random", "A", "B", "cond"], "mappings": ";;;iBAgBaA,gB,EAiDAC,U,EAkUAC,a;;AAlPb,WAASC,kBAAT,CAA4BC,MAA5B,EAA8CC,QAA9C,EAA0EC,IAA1E,EAA6G;AAAA;;AAC3G,UAAMC,IAAc,GAAG,EAAvB;AACA,UAAMC,MAAkB,GAAG,EAA3B;AACA,UAAMC,QAAQ,GAAG,IAAIC,GAAJ,EAAjB;AACA,UAAMC,QAAkB,GAAG,EAA3B;;AAEA,UAAMC,SAAS,GAAIC,CAAD,IAAiB;AAAEL,MAAAA,MAAM,CAACM,IAAP,CAAYD,CAAZ;AAAgBN,MAAAA,IAAI,CAACO,IAAL,CAAUC,MAAM,CAACC,UAAjB,EAA6BR,MAAM,CAACS,MAAP,GAAgB,CAA7C;AAAkD,KAAvG;;AACA,UAAMC,OAAO,GAAIC,IAAD,IAAkB;AAChC,UAAIC,GAAG,GAAGX,QAAQ,CAACY,GAAT,CAAaF,IAAb,CAAV;;AACA,UAAIC,GAAG,KAAKE,SAAZ,EAAuB;AAAEF,QAAAA,GAAG,GAAGT,QAAQ,CAACM,MAAf;AAAuBR,QAAAA,QAAQ,CAACc,GAAT,CAAaJ,IAAb,EAAmBC,GAAnB;AAAyBT,QAAAA,QAAQ,CAACG,IAAT,CAAcK,IAAd;AAAsB;;AAC/FZ,MAAAA,IAAI,CAACO,IAAL,CAAUC,MAAM,CAACS,QAAjB,EAA2BJ,GAA3B;AACD,KAJD;;AAKA,UAAMK,UAAU,GAAIC,EAAD,IAAgBnB,IAAI,CAACO,IAAL,CAAUY,EAAV,CAAnC;;AAEA,UAAMC,YAAY,4BAAGrB,IAAH,oBAAGA,IAAI,CAAEsB,sBAAT,oCAAmC,IAArD;AACA,UAAMC,UAAU,uBAAGvB,IAAH,oBAAGA,IAAI,CAAEuB,UAAT,+BAAuB,IAAvC;;AAEA,SAAK,MAAMC,GAAX,IAAkB1B,MAAlB,EAA0B;AACxB,YAAM2B,CAAC,GAAGD,GAAG,CAACE,IAAJ,EAAV;AACA,UAAI,CAACD,CAAL,EAAQ,SAFgB,CAGxB;;AACA,UAAIE,MAAM,CAACC,IAAP,CAAYH,CAAZ,CAAJ,EAAoB;AAAEnB,QAAAA,SAAS,CAACuB,UAAU,CAACJ,CAAD,CAAX,CAAT;AAA0B;AAAW;;AAC3D,UAAI,UAAUG,IAAV,CAAeH,CAAf,CAAJ,EAAuB;AAAEnB,QAAAA,SAAS,CAAC,IAAD,CAAT;AAAiB;AAAW;;AACrD,UAAI,WAAWsB,IAAX,CAAgBH,CAAhB,CAAJ,EAAwB;AAAEnB,QAAAA,SAAS,CAAC,KAAD,CAAT;AAAkB;AAAW,OAN/B,CAQxB;;;AACA,cAAQmB,CAAR;AACE,aAAK,GAAL;AAAUN,UAAAA,UAAU,CAACV,MAAM,CAACqB,GAAR,CAAV;AAAwB;;AAClC,aAAK,GAAL;AAAUX,UAAAA,UAAU,CAACV,MAAM,CAACsB,GAAR,CAAV;AAAwB;;AAClC,aAAK,GAAL;AAAUZ,UAAAA,UAAU,CAACV,MAAM,CAACuB,GAAR,CAAV;AAAwB;;AAClC,aAAK,GAAL;AAAUb,UAAAA,UAAU,CAACV,MAAM,CAACwB,GAAR,CAAV;AAAwB;;AAClC,aAAK,GAAL;AAAUd,UAAAA,UAAU,CAACV,MAAM,CAACyB,GAAR,CAAV;AAAwB;;AAClC,aAAK,GAAL;AAAUf,UAAAA,UAAU,CAACV,MAAM,CAAC0B,GAAR,CAAV;AAAwB;;AAClC,aAAK,KAAL;AAAYlC,UAAAA,IAAI,CAACO,IAAL,CAAUC,MAAM,CAAC2B,GAAjB;AAAuB;AAAU;;AAC7C,aAAK,GAAL;AAAUjB,UAAAA,UAAU,CAACV,MAAM,CAAC4B,EAAR,CAAV;AAAuB;;AACjC,aAAK,IAAL;AAAWlB,UAAAA,UAAU,CAACV,MAAM,CAAC6B,EAAR,CAAV;AAAuB;;AAClC,aAAK,GAAL;AAAUnB,UAAAA,UAAU,CAACV,MAAM,CAAC8B,EAAR,CAAV;AAAuB;;AACjC,aAAK,IAAL;AAAWpB,UAAAA,UAAU,CAACV,MAAM,CAAC+B,EAAR,CAAV;AAAuB;;AAClC,aAAK,IAAL;AAAWrB,UAAAA,UAAU,CAACV,MAAM,CAACgC,EAAR,CAAV;AAAuB;;AAClC,aAAK,IAAL;AAAWtB,UAAAA,UAAU,CAACV,MAAM,CAACiC,GAAR,CAAV;AAAwB;;AACnC,aAAK,IAAL;AAAWvB,UAAAA,UAAU,CAACV,MAAM,CAACkC,GAAR,CAAV;AAAwB;;AACnC,aAAK,IAAL;AAAWxB,UAAAA,UAAU,CAACV,MAAM,CAACmC,EAAR,CAAV;AAAuB;;AAClC,aAAK,GAAL;AAAU3C,UAAAA,IAAI,CAACO,IAAL,CAAUC,MAAM,CAACoC,GAAjB;AAAuB;AAhBnC,OATwB,CA4BxB;;;AACA,UAAIpB,CAAC,CAACqB,UAAF,CAAa,OAAb,CAAJ,EAA2B;AACzB,cAAMC,KAAK,GAAGtB,CAAC,CAACuB,KAAF,CAAQ,GAAR,CAAd,CADyB,CAEzB;;AACA,cAAMnC,IAAI,GAAGkC,KAAK,CAAC,CAAD,CAAlB;AACA,cAAME,KAAK,GAAGC,QAAQ,CAACH,KAAK,CAAC,CAAD,CAAN,EAAW,EAAX,CAAtB;AACA,cAAMI,IAAI,GAAGpD,QAAQ,CAACqD,iBAAT,CAA2BvC,IAA3B,EAAiCoC,KAAjC,CAAb;AACA,YAAI,CAACE,IAAL,EAAW,MAAM,IAAIE,KAAJ,CAAW,aAAYxC,IAAK,gBAAeoC,KAAM,qBAAjD,CAAN;AACXhD,QAAAA,IAAI,CAACO,IAAL,CAAUC,MAAM,CAAC6C,IAAjB,EAAuBH,IAAI,CAACI,EAA5B,EAAgCN,KAAhC;AACA;AACD,OAtCuB,CAwCxB;;;AACA,UAAI5B,YAAJ,EAAkB;AAChBT,QAAAA,OAAO,CAACa,CAAD,CAAP;AACA;AACD;;AACD,YAAM,IAAI4B,KAAJ,CAAW,kBAAiB5B,CAAE,IAA9B,CAAN;AACD;;AAED,WAAO,IAAI9B,UAAJ,CAAeM,IAAf,EAAqBC,MAArB,EAA6BG,QAA7B,EAAuCN,QAAvC,EAAiDwB,UAAjD,CAAP;AACD,G,CAED;;;AAGA,WAASiC,QAAT,CAAkBC,IAAlB,EAAyC;AACvC,UAAMC,GAAY,GAAG,EAArB;AACA,UAAMC,EAAE,GAAG,qFAAX;AACA,QAAIC,CAAJ;;AACA,WAAO,CAACA,CAAC,GAAGD,EAAE,CAACE,IAAH,CAAQJ,IAAR,CAAL,MAAwB,IAA/B,EAAqC;AACnC,YAAMK,CAAC,GAAGF,CAAC,CAAC,CAAD,CAAX;AACA,UAAI,oBAAoBhC,IAApB,CAAyBkC,CAAzB,CAAJ,EAAiCJ,GAAG,CAAClD,IAAJ,CAAS;AAACuD,QAAAA,IAAI,EAAC,QAAN;AAAgBC,QAAAA,KAAK,EAACF;AAAtB,OAAT,EAAjC,KACK,IAAI,gBAAgBlC,IAAhB,CAAqBkC,CAArB,CAAJ,EAA6BJ,GAAG,CAAClD,IAAJ,CAAS;AAACuD,QAAAA,IAAI,EAAC,MAAN;AAAcC,QAAAA,KAAK,EAACF;AAApB,OAAT,EAA7B,KACA,IAAIA,CAAC,KAAK,GAAV,EAAeJ,GAAG,CAAClD,IAAJ,CAAS;AAACuD,QAAAA,IAAI,EAAC,QAAN;AAAgBC,QAAAA,KAAK,EAACF;AAAtB,OAAT,EAAf,KACA,IAAIA,CAAC,KAAK,GAAV,EAAeJ,GAAG,CAAClD,IAAJ,CAAS;AAACuD,QAAAA,IAAI,EAAC,QAAN;AAAgBC,QAAAA,KAAK,EAACF;AAAtB,OAAT,EAAf,KACA,IAAIA,CAAC,KAAK,GAAV,EAAeJ,GAAG,CAAClD,IAAJ,CAAS;AAACuD,QAAAA,IAAI,EAAC,OAAN;AAAeC,QAAAA,KAAK,EAACF;AAArB,OAAT,EAAf,KACA,IAAI,mBAAmBlC,IAAnB,CAAwBkC,CAAxB,KAA8BA,CAAC,KAAK,IAApC,IAA4CA,CAAC,KAAK,IAAtD,EAA4DJ,GAAG,CAAClD,IAAJ,CAAS;AAACuD,QAAAA,IAAI,EAAC,IAAN;AAAYC,QAAAA,KAAK,EAACF;AAAlB,OAAT,EAA5D,KACAJ,GAAG,CAAClD,IAAJ,CAAS;AAACuD,QAAAA,IAAI,EAAC,OAAN;AAAeC,QAAAA,KAAK,EAACF;AAArB,OAAT;AACN;;AACD,WAAOJ,GAAP;AACD,G,CAED;;;AAsBA,WAASO,UAAT,CAAoBR,IAApB,EAAkC1D,QAAlC,EAAwE;AACtE,UAAMD,MAAM,GAAG0D,QAAQ,CAACC,IAAD,CAAvB;AACA,UAAMS,MAAgB,GAAG,EAAzB;AACA,UAAMC,OAAsF,GAAG,EAA/F,CAHsE,CAKtE;;AACA,SAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGtE,MAAM,CAACa,MAA3B,EAAmCyD,CAAC,EAApC,EAAwC;AACtC,YAAM3C,CAAC,GAAG3B,MAAM,CAACsE,CAAD,CAAhB;;AAEA,UAAI3C,CAAC,CAACsC,IAAF,KAAW,QAAX,IAAuBtC,CAAC,CAACsC,IAAF,KAAW,MAAtC,EAA8C;AAC5CG,QAAAA,MAAM,CAAC1D,IAAP,CAAYiB,CAAC,CAACuC,KAAd;AACA;AACD;;AAED,UAAIvC,CAAC,CAACsC,IAAF,KAAW,OAAf,EAAwB;AACtB;AACA,cAAMM,IAAI,GAAGvE,MAAM,CAACsE,CAAC,GAAC,CAAH,CAAnB;;AACA,YAAIC,IAAI,IAAIA,IAAI,CAACN,IAAL,KAAc,QAA1B,EAAoC;AAClC;AACAI,UAAAA,OAAO,CAAC3D,IAAR,CAAa;AAACuD,YAAAA,IAAI,EAAC,MAAN;AAAcC,YAAAA,KAAK,EAAEvC,CAAC,CAACuC,KAAvB;AAA8BM,YAAAA,QAAQ,EAAE;AAAxC,WAAb;AACD,SAHD,MAGO;AACL;AACAJ,UAAAA,MAAM,CAAC1D,IAAP,CAAYiB,CAAC,CAACuC,KAAd;AACD;;AACD;AACD;;AAED,UAAIvC,CAAC,CAACsC,IAAF,KAAW,OAAf,EAAwB;AACtB;AACA,eAAOI,OAAO,CAACxD,MAAR,GAAiB,CAAjB,IAAsBwD,OAAO,CAACA,OAAO,CAACxD,MAAR,GAAe,CAAhB,CAAP,CAA0BoD,IAA1B,KAAmC,QAAhE,EAA0E;AACxE,gBAAM3C,EAAE,GAAG+C,OAAO,CAACI,GAAR,EAAX;AACA,cAAInD,EAAE,CAAC2C,IAAH,KAAY,IAAhB,EAAsBG,MAAM,CAAC1D,IAAP,CAAYY,EAAE,CAAC4C,KAAf,EAAtB,KACK,IAAI5C,EAAE,CAAC2C,IAAH,KAAY,MAAhB,EAAwB;AAAA;;AAC3B;AACAG,YAAAA,MAAM,CAAC1D,IAAP,CAAa,QAAOY,EAAE,CAAC4C,KAAM,IAAjB,gBAAqB5C,EAAE,CAACkD,QAAxB,2BAAoC,CAAG,EAAnD;AACD;AACF,SATqB,CAUtB;AACA;;;AACA,aAAK,IAAIE,CAAC,GAAGL,OAAO,CAACxD,MAAR,GAAiB,CAA9B,EAAiC6D,CAAC,IAAI,CAAtC,EAAyCA,CAAC,EAA1C,EAA8C;AAC5C,cAAIL,OAAO,CAACK,CAAD,CAAP,CAAWT,IAAX,KAAoB,QAAxB,EAAkC;AAChC;AACA,gBAAIS,CAAC,GAAG,CAAJ,IAASL,OAAO,CAACK,CAAC,GAAC,CAAH,CAAP,CAAaT,IAAb,KAAsB,MAAnC,EAA2C;AAAA;;AACzCI,cAAAA,OAAO,CAACK,CAAC,GAAC,CAAH,CAAP,CAAaF,QAAb,GAAwB,sBAACH,OAAO,CAACK,CAAC,GAAC,CAAH,CAAP,CAAaF,QAAd,gCAA0B,CAA1B,IAA+B,CAAvD;AACD;;AACD;AACD;AACF;;AACD;AACD;;AAED,UAAI7C,CAAC,CAACsC,IAAF,KAAW,IAAf,EAAqB;AACnB,cAAMU,KAAK,GAAGhD,CAAC,CAACuC,KAAhB,CADmB,CAEnB;;AACA,cAAMU,IAAI,GAAG5E,MAAM,CAACsE,CAAC,GAAC,CAAH,CAAnB;AACA,cAAMO,OAAO,GAAGF,KAAK,KAAK,GAAV,KACdL,CAAC,KAAK,CAAN,IACCM,IAAI,KAAKA,IAAI,CAACX,IAAL,KAAc,IAAd,IAAsBW,IAAI,CAACX,IAAL,KAAc,QAApC,IAAgDW,IAAI,CAACX,IAAL,KAAc,OAAnE,CAFS,CAAhB;AAKA,cAAMa,KAAK,GAAID,OAAO,IAAIF,KAAK,KAAK,GAAtB,GAA6B,KAA7B,GAAqCA,KAAnD;AAEA,YAAI,EAAEG,KAAK,IAAIC,GAAX,CAAJ,EAAqB,MAAM,IAAIxB,KAAJ,CAAW,qBAAoBoB,KAAM,GAArC,CAAN;AAErB,cAAMK,EAAE,GAAGD,GAAG,CAACD,KAAD,CAAd;;AACA,eAAOT,OAAO,CAACxD,MAAR,GAAiB,CAAxB,EAA2B;AACzB,gBAAMoE,GAAG,GAAGZ,OAAO,CAACA,OAAO,CAACxD,MAAR,GAAiB,CAAlB,CAAnB;AACA,cAAIoE,GAAG,CAAChB,IAAJ,KAAa,IAAjB,EAAuB;AACvB,gBAAMiB,EAAE,GAAGH,GAAG,CAACE,GAAG,CAACf,KAAL,CAAd;AACA,cAAI,CAACgB,EAAL,EAAS;;AACT,cAAKF,EAAE,CAACG,KAAH,KAAa,MAAb,IAAuBH,EAAE,CAACI,IAAH,IAAWF,EAAE,CAACE,IAAtC,IAAgDJ,EAAE,CAACG,KAAH,KAAa,OAAb,IAAwBH,EAAE,CAACI,IAAH,GAAUF,EAAE,CAACE,IAAzF,EAAgG;AAC9FhB,YAAAA,MAAM,CAAC1D,IAAP,CAAY2D,OAAO,CAACI,GAAR,GAAeP,KAA3B;AACD,WAFD,MAEO;AACR;;AACDG,QAAAA,OAAO,CAAC3D,IAAR,CAAa;AAACuD,UAAAA,IAAI,EAAC,IAAN;AAAYC,UAAAA,KAAK,EAAEY;AAAnB,SAAb;AACA;AACD;;AAED,UAAInD,CAAC,CAACsC,IAAF,KAAW,QAAf,EAAyB;AACvBI,QAAAA,OAAO,CAAC3D,IAAR,CAAa;AAACuD,UAAAA,IAAI,EAAC,QAAN;AAAgBC,UAAAA,KAAK,EAAC;AAAtB,SAAb,EADuB,CAEvB;;AACA;AACD;;AAED,UAAIvC,CAAC,CAACsC,IAAF,KAAW,QAAf,EAAyB;AACvB;AACA,eAAOI,OAAO,CAACxD,MAAR,GAAiB,CAAjB,IAAsBwD,OAAO,CAACA,OAAO,CAACxD,MAAR,GAAe,CAAhB,CAAP,CAA0BoD,IAA1B,KAAmC,QAAhE,EAA0E;AACxE,gBAAM3C,EAAE,GAAG+C,OAAO,CAACI,GAAR,EAAX;AACA,cAAInD,EAAE,CAAC2C,IAAH,KAAY,IAAhB,EAAsBG,MAAM,CAAC1D,IAAP,CAAYY,EAAE,CAAC4C,KAAf,EAAtB,KACK,IAAI5C,EAAE,CAAC2C,IAAH,KAAY,MAAhB,EAAwB;AAAA;;AAC3B;AACAG,YAAAA,MAAM,CAAC1D,IAAP,CAAa,QAAOY,EAAE,CAAC4C,KAAM,IAAjB,iBAAqB5C,EAAE,CAACkD,QAAxB,4BAAoC,CAAG,EAAnD;AACD;AACF;;AACD,YAAIH,OAAO,CAACxD,MAAR,KAAmB,CAAvB,EAA0B,MAAM,IAAI0C,KAAJ,CAAU,wBAAV,CAAN,CAVH,CAWvB;;AACAc,QAAAA,OAAO,CAACI,GAAR,GAZuB,CAavB;;AACA,YAAIJ,OAAO,CAACxD,MAAR,GAAiB,CAAjB,IAAsBwD,OAAO,CAACA,OAAO,CAACxD,MAAR,GAAe,CAAhB,CAAP,CAA0BoD,IAA1B,KAAmC,MAA7D,EAAqE;AACnE,gBAAMoB,EAAE,GAAGhB,OAAO,CAACI,GAAR,EAAX,CADmE,CAEnE;;AACA,gBAAMa,MAAM,GAAGtF,MAAM,CAACsE,CAAC,GAAC,CAAH,CAArB;AACA,cAAIiB,UAAJ;;AAEA,cAAID,MAAM,IAAIA,MAAM,CAACrB,IAAP,KAAgB,QAA9B,EAAwC;AACtC;AACAsB,YAAAA,UAAU,GAAG,CAAb;AACD,WAHD,MAGO;AAAA;;AACL;AACAA,YAAAA,UAAU,mBAAGF,EAAE,CAACb,QAAN,2BAAkB,CAA5B;AACD;;AAEDJ,UAAAA,MAAM,CAAC1D,IAAP,CAAa,QAAO2E,EAAE,CAACnB,KAAM,IAAGqB,UAAW,EAA3C;AACD;;AACD;AACD;AACF,KApHqE,CAsHtE;;;AACA,WAAOlB,OAAO,CAACxD,MAAR,GAAiB,CAAxB,EAA2B;AACzB,YAAMS,EAAE,GAAG+C,OAAO,CAACI,GAAR,EAAX;AACA,UAAInD,EAAE,CAAC2C,IAAH,KAAY,QAAZ,IAAwB3C,EAAE,CAAC2C,IAAH,KAAY,QAAxC,EAAkD,MAAM,IAAIV,KAAJ,CAAU,wBAAV,CAAN;AAClD,UAAIjC,EAAE,CAAC2C,IAAH,KAAY,IAAhB,EAAsBG,MAAM,CAAC1D,IAAP,CAAYY,EAAE,CAAC4C,KAAf,EAAtB,KACK,IAAI5C,EAAE,CAAC2C,IAAH,KAAY,MAAhB,EAAwB;AAAA;;AAC3B;AACAG,QAAAA,MAAM,CAAC1D,IAAP,CAAa,QAAOY,EAAE,CAAC4C,KAAM,IAAjB,iBAAoB5C,EAAE,CAACkD,QAAvB,4BAAmC,CAAE,EAAjD;AACD;AACF;;AACD,WAAOJ,MAAP;AACD,G,CAED;;;;;;;;;;;;;;;;2EAlYA;AACA;AACA;AAMA;;;kCAQaxE,gB,GAAN,MAAMA,gBAAN,CAAuB;AAAA;AAC5B;AAD4B,eAEpB4F,MAFoB,GAEX,IAAIlF,GAAJ,EAFW;AAAA,eAGpBmF,IAHoB,GAGH,EAHG;AAAA;;AAK5BC,QAAAA,QAAQ,CAAC3E,IAAD,EAAeoC,KAAf,EAA8BwC,IAA9B,EAAmD;AACzD,cAAI,CAACC,MAAM,CAACC,SAAP,CAAiB1C,KAAjB,CAAD,IAA4BA,KAAK,GAAG,CAAxC,EAA2C,MAAM,IAAII,KAAJ,CAAU,oBAAV,CAAN;AAC3C,cAAIuC,GAAG,GAAG,KAAKN,MAAL,CAAYvE,GAAZ,CAAgBF,IAAhB,CAAV;;AACA,cAAI,CAAC+E,GAAL,EAAU;AAAEA,YAAAA,GAAG,GAAG,IAAIxF,GAAJ,EAAN;AAAiB,iBAAKkF,MAAL,CAAYrE,GAAZ,CAAgBJ,IAAhB,EAAsB+E,GAAtB;AAA6B;;AAC1D,gBAAMC,QAAQ,GAAGD,GAAG,CAAC7E,GAAJ,CAAQkC,KAAR,CAAjB;;AACA,cAAI4C,QAAJ,EAAc;AACZA,YAAAA,QAAQ,CAACJ,IAAT,GAAgBA,IAAhB;AACA,mBAAOI,QAAP;AACD;;AACD,gBAAM1C,IAAY,GAAG;AAAEI,YAAAA,EAAE,EAAE,KAAKgC,IAAL,CAAU5E,MAAhB;AAAwBE,YAAAA,IAAxB;AAA8BoC,YAAAA,KAA9B;AAAqCwC,YAAAA;AAArC,WAArB;AACAG,UAAAA,GAAG,CAAC3E,GAAJ,CAAQgC,KAAR,EAAeE,IAAf;AACA,eAAKoC,IAAL,CAAU/E,IAAV,CAAe2C,IAAf;AACA,iBAAOA,IAAP;AACD;;AAEDC,QAAAA,iBAAiB,CAACvC,IAAD,EAAeoC,KAAf,EAAkD;AACjE,gBAAM2C,GAAG,GAAG,KAAKN,MAAL,CAAYvE,GAAZ,CAAgBF,IAAhB,CAAZ;AACA,iBAAO+E,GAAG,GAAGA,GAAG,CAAC7E,GAAJ,CAAQkC,KAAR,CAAH,GAAoBjC,SAA9B;AACD,SAvB2B,CAyB5B;;;AACA8E,QAAAA,eAAe,CAACjF,IAAD,EAAmC;AAChD,gBAAM+E,GAAG,GAAG,KAAKN,MAAL,CAAYvE,GAAZ,CAAgBF,IAAhB,CAAZ;AACA,cAAI,CAAC+E,GAAL,EAAU,OAAO5E,SAAP;AACV,cAAI4E,GAAG,CAACG,IAAJ,KAAa,CAAjB,EAAoB,OAAOC,KAAK,CAACC,IAAN,CAAWL,GAAG,CAACM,MAAJ,EAAX,EAAyB,CAAzB,CAAP;AACpB,iBAAOlF,SAAP;AACD;;AAEDmF,QAAAA,OAAO,CAAC5C,EAAD,EAAqB;AAC1B,iBAAO,KAAKgC,IAAL,CAAUhC,EAAV,CAAP;AACD;;AAnC2B,O,GAsC9B;;;AACK9C,MAAAA,M,0BAAAA,M;AAAAA,QAAAA,M,CAAAA,M;AAAAA,QAAAA,M,CAAAA,M;AAAAA,QAAAA,M,CAAAA,M;AAAAA,QAAAA,M,CAAAA,M;AAAAA,QAAAA,M,CAAAA,M;AAAAA,QAAAA,M,CAAAA,M;AAAAA,QAAAA,M,CAAAA,M;AAAAA,QAAAA,M,CAAAA,M;AAAAA,QAAAA,M,CAAAA,M;AAAAA,QAAAA,M,CAAAA,M;AAAAA,QAAAA,M,CAAAA,M;AAAAA,QAAAA,M,CAAAA,M;AAAAA,QAAAA,M,CAAAA,M;AAAAA,QAAAA,M,CAAAA,M;AAAAA,QAAAA,M,CAAAA,M;AAAAA,QAAAA,M,CAAAA,M;AAAAA,QAAAA,M,CAAAA,M;AAAAA,QAAAA,M,CAAAA,M;AAAAA,QAAAA,M,CAAAA,M;eAAAA,M;QAAAA,M;;4BAUQd,U,GAAN,MAAMA,UAAN,CAAiB;AAOtByG,QAAAA,WAAW,CAACnG,IAAD,EAAiBC,MAAjB,EAAqCG,QAArC,EAAyDN,QAAzD,EAAqFwB,UAAU,GAAG,IAAlG,EAAwG;AAAA,eANlGtB,IAMkG;AAAA,eALlGC,MAKkG;AAAA,eAJlGG,QAIkG;AAAA,eAHlGN,QAGkG;AAAA,eAFlGwB,UAEkG;AACjH,eAAKtB,IAAL,GAAYA,IAAZ;AACA,eAAKC,MAAL,GAAcA,MAAd;AACA,eAAKG,QAAL,GAAgBA,QAAhB;AACA,eAAKN,QAAL,GAAgBA,QAAhB;AACA,eAAKwB,UAAL,GAAkBA,UAAlB;AACD;;AAED8E,QAAAA,QAAQ,CAACC,GAAD,EAA4B;AAClC,gBAAMC,OAAmB,GAAG,IAAIP,KAAJ,CAAU,KAAK3F,QAAL,CAAcM,MAAxB,CAA5B;;AACA,eAAK,IAAIyD,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAK/D,QAAL,CAAcM,MAAlC,EAA0CyD,CAAC,EAA3C,EAA+C;AAC7C,kBAAMvD,IAAI,GAAG,KAAKR,QAAL,CAAc+D,CAAd,CAAb;AACA,kBAAM7D,CAAC,GAAG+F,GAAG,CAACzF,IAAD,CAAb;;AACA,gBAAIN,CAAC,KAAKS,SAAV,EAAqB;AACnB,kBAAI,KAAKO,UAAT,EAAqB,MAAM,IAAI8B,KAAJ,CAAW,qBAAoBxC,IAAK,eAApC,CAAN;AACrB0F,cAAAA,OAAO,CAACnC,CAAD,CAAP,GAAa,CAAb;AACD,aAHD,MAGO;AACLmC,cAAAA,OAAO,CAACnC,CAAD,CAAP,GAAa7D,CAAb;AACD;AACF;;AAED,gBAAMiG,KAAiB,GAAG,EAA1B;AACA,gBAAMvG,IAAI,GAAG,KAAKA,IAAlB;AACA,cAAIwG,EAAE,GAAG,CAAT;;AACA,iBAAOA,EAAE,GAAGxG,IAAI,CAACU,MAAjB,EAAyB;AACvB,oBAAQV,IAAI,CAACwG,EAAE,EAAH,CAAZ;AACE,mBAAKhG,MAAM,CAACC,UAAZ;AAAwB;AACtB,wBAAMI,GAAG,GAAGb,IAAI,CAACwG,EAAE,EAAH,CAAhB;AAAwBD,kBAAAA,KAAK,CAAChG,IAAN,CAAW,KAAKN,MAAL,CAAYY,GAAZ,CAAX;AAA8B;AACvD;;AACD,mBAAKL,MAAM,CAACS,QAAZ;AAAsB;AACpB,wBAAMJ,GAAG,GAAGb,IAAI,CAACwG,EAAE,EAAH,CAAhB;AAAwBD,kBAAAA,KAAK,CAAChG,IAAN,CAAW+F,OAAO,CAACzF,GAAD,CAAlB;AAA0B;AACnD;;AACD,mBAAKL,MAAM,CAACqB,GAAZ;AAAiB;AAAE,wBAAM4E,CAAC,GAAGhB,MAAM,CAACc,KAAK,CAACjC,GAAN,EAAD,CAAhB;AAA+B,wBAAMoC,CAAC,GAAGjB,MAAM,CAACc,KAAK,CAACjC,GAAN,EAAD,CAAhB;AAA+BiC,kBAAAA,KAAK,CAAChG,IAAN,CAAWmG,CAAC,GAAGD,CAAf;AAAmB;AAAQ;;AAC5G,mBAAKjG,MAAM,CAACsB,GAAZ;AAAiB;AAAE,wBAAM2E,CAAC,GAAGhB,MAAM,CAACc,KAAK,CAACjC,GAAN,EAAD,CAAhB;AAA+B,wBAAMoC,CAAC,GAAGjB,MAAM,CAACc,KAAK,CAACjC,GAAN,EAAD,CAAhB;AAA+BiC,kBAAAA,KAAK,CAAChG,IAAN,CAAWmG,CAAC,GAAGD,CAAf;AAAmB;AAAQ;;AAC5G,mBAAKjG,MAAM,CAACuB,GAAZ;AAAiB;AAAE,wBAAM0E,CAAC,GAAGhB,MAAM,CAACc,KAAK,CAACjC,GAAN,EAAD,CAAhB;AAA+B,wBAAMoC,CAAC,GAAGjB,MAAM,CAACc,KAAK,CAACjC,GAAN,EAAD,CAAhB;AAA+BiC,kBAAAA,KAAK,CAAChG,IAAN,CAAWmG,CAAC,GAAGD,CAAf;AAAmB;AAAQ;;AAC5G,mBAAKjG,MAAM,CAACwB,GAAZ;AAAiB;AAAE,wBAAMyE,CAAC,GAAGhB,MAAM,CAACc,KAAK,CAACjC,GAAN,EAAD,CAAhB;AAA+B,wBAAMoC,CAAC,GAAGjB,MAAM,CAACc,KAAK,CAACjC,GAAN,EAAD,CAAhB;AAA+BiC,kBAAAA,KAAK,CAAChG,IAAN,CAAWmG,CAAC,GAAGD,CAAf;AAAmB;AAAQ;;AAC5G,mBAAKjG,MAAM,CAACyB,GAAZ;AAAiB;AAAE,wBAAMwE,CAAC,GAAGhB,MAAM,CAACc,KAAK,CAACjC,GAAN,EAAD,CAAhB;AAA+B,wBAAMoC,CAAC,GAAGjB,MAAM,CAACc,KAAK,CAACjC,GAAN,EAAD,CAAhB;AAA+BiC,kBAAAA,KAAK,CAAChG,IAAN,CAAWmG,CAAC,GAAGD,CAAf;AAAmB;AAAQ;;AAC5G,mBAAKjG,MAAM,CAAC0B,GAAZ;AAAiB;AAAE,wBAAMuE,CAAC,GAAGhB,MAAM,CAACc,KAAK,CAACjC,GAAN,EAAD,CAAhB;AAA+B,wBAAMoC,CAAC,GAAGjB,MAAM,CAACc,KAAK,CAACjC,GAAN,EAAD,CAAhB;AAA+BiC,kBAAAA,KAAK,CAAChG,IAAN,CAAWoG,IAAI,CAACC,GAAL,CAASF,CAAT,EAAYD,CAAZ,CAAX;AAA4B;AAAQ;;AACrH,mBAAKjG,MAAM,CAAC2B,GAAZ;AAAiB;AAAE,wBAAMuE,CAAC,GAAGjB,MAAM,CAACc,KAAK,CAACjC,GAAN,EAAD,CAAhB;AAA+BiC,kBAAAA,KAAK,CAAChG,IAAN,CAAW,CAACmG,CAAZ;AAAgB;AAAQ;;AAC1E,mBAAKlG,MAAM,CAAC4B,EAAZ;AAAgB;AAAE,wBAAMqE,CAAC,GAAGhB,MAAM,CAACc,KAAK,CAACjC,GAAN,EAAD,CAAhB;AAA+B,wBAAMoC,CAAC,GAAGjB,MAAM,CAACc,KAAK,CAACjC,GAAN,EAAD,CAAhB;AAA+BiC,kBAAAA,KAAK,CAAChG,IAAN,CAAWmG,CAAC,GAAGD,CAAf;AAAmB;AAAQ;;AAC3G,mBAAKjG,MAAM,CAAC6B,EAAZ;AAAgB;AAAE,wBAAMoE,CAAC,GAAGhB,MAAM,CAACc,KAAK,CAACjC,GAAN,EAAD,CAAhB;AAA+B,wBAAMoC,CAAC,GAAGjB,MAAM,CAACc,KAAK,CAACjC,GAAN,EAAD,CAAhB;AAA+BiC,kBAAAA,KAAK,CAAChG,IAAN,CAAWmG,CAAC,IAAID,CAAhB;AAAoB;AAAQ;;AAC5G,mBAAKjG,MAAM,CAAC8B,EAAZ;AAAgB;AAAE,wBAAMmE,CAAC,GAAGhB,MAAM,CAACc,KAAK,CAACjC,GAAN,EAAD,CAAhB;AAA+B,wBAAMoC,CAAC,GAAGjB,MAAM,CAACc,KAAK,CAACjC,GAAN,EAAD,CAAhB;AAA+BiC,kBAAAA,KAAK,CAAChG,IAAN,CAAWmG,CAAC,GAAGD,CAAf;AAAmB;AAAQ;;AAC3G,mBAAKjG,MAAM,CAAC+B,EAAZ;AAAgB;AAAE,wBAAMkE,CAAC,GAAGhB,MAAM,CAACc,KAAK,CAACjC,GAAN,EAAD,CAAhB;AAA+B,wBAAMoC,CAAC,GAAGjB,MAAM,CAACc,KAAK,CAACjC,GAAN,EAAD,CAAhB;AAA+BiC,kBAAAA,KAAK,CAAChG,IAAN,CAAWmG,CAAC,IAAID,CAAhB;AAAoB;AAAQ;;AAC5G,mBAAKjG,MAAM,CAACgC,EAAZ;AAAgB;AAAE,wBAAMiE,CAAC,GAAGF,KAAK,CAACjC,GAAN,EAAV;AAAuB,wBAAMoC,CAAC,GAAGH,KAAK,CAACjC,GAAN,EAAV;AAAuBiC,kBAAAA,KAAK,CAAChG,IAAN,CAAWmG,CAAC,KAAKD,CAAjB;AAAqB;AAAQ;;AAC7F,mBAAKjG,MAAM,CAACiC,GAAZ;AAAiB;AAAE,wBAAMgE,CAAC,GAAGF,KAAK,CAACjC,GAAN,EAAV;AAAuB,wBAAMoC,CAAC,GAAGH,KAAK,CAACjC,GAAN,EAAV;AAAuBiC,kBAAAA,KAAK,CAAChG,IAAN,CAAWmG,CAAC,KAAKD,CAAjB;AAAqB;AAAQ;;AAC9F,mBAAKjG,MAAM,CAACkC,GAAZ;AAAiB;AAAE,wBAAM+D,CAAC,GAAGI,OAAO,CAACN,KAAK,CAACjC,GAAN,EAAD,CAAjB;AAAgC,wBAAMoC,CAAC,GAAGG,OAAO,CAACN,KAAK,CAACjC,GAAN,EAAD,CAAjB;AAAgCiC,kBAAAA,KAAK,CAAChG,IAAN,CAAWmG,CAAC,IAAID,CAAhB;AAAoB;AAAQ;;AAC/G,mBAAKjG,MAAM,CAACmC,EAAZ;AAAgB;AAAE,wBAAM8D,CAAC,GAAGI,OAAO,CAACN,KAAK,CAACjC,GAAN,EAAD,CAAjB;AAAgC,wBAAMoC,CAAC,GAAGG,OAAO,CAACN,KAAK,CAACjC,GAAN,EAAD,CAAjB;AAAgCiC,kBAAAA,KAAK,CAAChG,IAAN,CAAWmG,CAAC,IAAID,CAAhB;AAAoB;AAAQ;;AAC9G,mBAAKjG,MAAM,CAACoC,GAAZ;AAAiB;AAAE,wBAAM8D,CAAC,GAAGG,OAAO,CAACN,KAAK,CAACjC,GAAN,EAAD,CAAjB;AAAgCiC,kBAAAA,KAAK,CAAChG,IAAN,CAAW,CAACmG,CAAZ;AAAgB;AAAQ;;AAC3E,mBAAKlG,MAAM,CAAC6C,IAAZ;AAAkB;AAChB,wBAAMyD,IAAI,GAAG9G,IAAI,CAACwG,EAAE,EAAH,CAAjB;AAAyB,wBAAMxD,KAAK,GAAGhD,IAAI,CAACwG,EAAE,EAAH,CAAlB;AACzB,wBAAMO,IAAI,GAAG,IAAIhB,KAAJ,CAAoB/C,KAApB,CAAb;;AACA,uBAAK,IAAImB,CAAC,GAAGnB,KAAK,GAAG,CAArB,EAAwBmB,CAAC,IAAI,CAA7B,EAAgCA,CAAC,EAAjC,EAAqC4C,IAAI,CAAC5C,CAAD,CAAJ,GAAUoC,KAAK,CAACjC,GAAN,EAAV;;AACrC,wBAAMpB,IAAI,GAAG,KAAKpD,QAAL,CAAcoG,OAAd,CAAsBY,IAAtB,CAAb;AACA,wBAAMrD,GAAG,GAAGP,IAAI,CAACsC,IAAL,CAAUuB,IAAV,CAAZ;AACAR,kBAAAA,KAAK,CAAChG,IAAN,CAAWkD,GAAX;AACA;AACD;;AACD;AAAS,sBAAM,IAAIL,KAAJ,CAAW,kBAAkBpD,IAAI,CAACwG,EAAE,GAAC,CAAJ,CAAQ,EAAzC,CAAN;AAhCX;AAkCD;;AAED,cAAID,KAAK,CAAC7F,MAAN,KAAiB,CAArB,EAAwB,MAAM,IAAI0C,KAAJ,CAAW,8BAA6BmD,KAAK,CAAC7F,MAAO,UAArD,CAAN;AACxB,iBAAO6F,KAAK,CAAC,CAAD,CAAZ;AACD;;AAEDS,QAAAA,YAAY,GAAsB;AAAE,iBAAO,KAAK5G,QAAZ;AAAuB;;AAxErC,O,GA2ExB;;;AAGMsB,MAAAA,M,GAAS,2B;AA4FTkD,MAAAA,G,GAA8B;AAClC;AACA,aAAK;AAAEK,UAAAA,IAAI,EAAE,CAAR;AAAWD,UAAAA,KAAK,EAAE;AAAlB,SAF6B;AAGlC,eAAO;AAAEC,UAAAA,IAAI,EAAE,CAAR;AAAWD,UAAAA,KAAK,EAAE,OAAlB;AAA2BiC,UAAAA,KAAK,EAAE;AAAlC,SAH2B;AAIlC,aAAK;AAAEhC,UAAAA,IAAI,EAAE,CAAR;AAAWD,UAAAA,KAAK,EAAE,OAAlB;AAA2BiC,UAAAA,KAAK,EAAE;AAAlC,SAJ6B;AAKlC,aAAK;AAAEhC,UAAAA,IAAI,EAAE,CAAR;AAAWD,UAAAA,KAAK,EAAE;AAAlB,SAL6B;AAMlC,aAAK;AAAEC,UAAAA,IAAI,EAAE,CAAR;AAAWD,UAAAA,KAAK,EAAE;AAAlB,SAN6B;AAOlC,aAAK;AAAEC,UAAAA,IAAI,EAAE,CAAR;AAAWD,UAAAA,KAAK,EAAE;AAAlB,SAP6B;AAQlC,aAAK;AAAEC,UAAAA,IAAI,EAAE,CAAR;AAAWD,UAAAA,KAAK,EAAE;AAAlB,SAR6B;AASlC,aAAK;AAAEC,UAAAA,IAAI,EAAE,CAAR;AAAWD,UAAAA,KAAK,EAAE;AAAlB,SAT6B;AAUlC,aAAK;AAAEC,UAAAA,IAAI,EAAE,CAAR;AAAWD,UAAAA,KAAK,EAAE;AAAlB,SAV6B;AAWlC,cAAM;AAAEC,UAAAA,IAAI,EAAE,CAAR;AAAWD,UAAAA,KAAK,EAAE;AAAlB,SAX4B;AAYlC,aAAK;AAAEC,UAAAA,IAAI,EAAE,CAAR;AAAWD,UAAAA,KAAK,EAAE;AAAlB,SAZ6B;AAalC,cAAM;AAAEC,UAAAA,IAAI,EAAE,CAAR;AAAWD,UAAAA,KAAK,EAAE;AAAlB,SAb4B;AAclC,cAAM;AAAEC,UAAAA,IAAI,EAAE,CAAR;AAAWD,UAAAA,KAAK,EAAE;AAAlB,SAd4B;AAelC,cAAM;AAAEC,UAAAA,IAAI,EAAE,CAAR;AAAWD,UAAAA,KAAK,EAAE;AAAlB,SAf4B;AAgBlC,cAAM;AAAEC,UAAAA,IAAI,EAAE,CAAR;AAAWD,UAAAA,KAAK,EAAE;AAAlB,SAhB4B;AAiBlC,cAAM;AAAEC,UAAAA,IAAI,EAAE,CAAR;AAAWD,UAAAA,KAAK,EAAE;AAAlB;AAjB4B,O;;+BAwJvBrF,a,GAAN,MAAMA,aAAN,CAAoB;AAGzBwG,QAAAA,WAAW,CAACe,gBAAgB,GAAG,IAApB,EAA0B;AAAA,eAF5BpH,QAE4B,GAFjB,IAAIL,gBAAJ,EAEiB;AACnC,cAAIyH,gBAAJ,EAAsB,KAAKA,gBAAL;AACvB;;AAEDC,QAAAA,OAAO,CAAC3D,IAAD,EAA0BzD,IAA1B,EAA6D;AAClE,cAAIF,MAAJ;AACA,cAAIkG,KAAK,CAACqB,OAAN,CAAc5D,IAAd,CAAJ,EAAyB3D,MAAM,GAAG2D,IAAT,CAAzB,KACK;AACH3D,YAAAA,MAAM,GAAGmE,UAAU,CAACR,IAAD,EAAO,KAAK1D,QAAZ,CAAnB;AACD;AACD,iBAAOF,kBAAkB,CAACC,MAAD,EAAS,KAAKC,QAAd,EAAwBC,IAAxB,CAAzB;AACD;;AAEDwF,QAAAA,QAAQ,CAAC3E,IAAD,EAAeoC,KAAf,EAA8BwC,IAA9B,EAAiD;AACvD,eAAK1F,QAAL,CAAcyF,QAAd,CAAuB3E,IAAvB,EAA6BoC,KAA7B,EAAoCwC,IAApC;AACD;;AAEO0B,QAAAA,gBAAgB,GAAS;AAC/B,gBAAMG,CAAC,GAAG,KAAKvH,QAAf,CAD+B,CAE/B;;AACAuH,UAAAA,CAAC,CAAC9B,QAAF,CAAW,KAAX,EAAkB,CAAlB,EAAqB,CAAC,CAACmB,CAAD,CAAD,KAASC,IAAI,CAACW,GAAL,CAAS7B,MAAM,CAACiB,CAAD,CAAf,CAA9B;AACAW,UAAAA,CAAC,CAAC9B,QAAF,CAAW,OAAX,EAAoB,CAApB,EAAuB,CAAC,CAACmB,CAAD,CAAD,KAASC,IAAI,CAACY,KAAL,CAAW9B,MAAM,CAACiB,CAAD,CAAjB,CAAhC;AACAW,UAAAA,CAAC,CAAC9B,QAAF,CAAW,MAAX,EAAmB,CAAnB,EAAsB,CAAC,CAACmB,CAAD,CAAD,KAASC,IAAI,CAACa,IAAL,CAAU/B,MAAM,CAACiB,CAAD,CAAhB,CAA/B;AACAW,UAAAA,CAAC,CAAC9B,QAAF,CAAW,OAAX,EAAoB,CAApB,EAAuB,CAAC,CAACmB,CAAD,CAAD,KAASC,IAAI,CAACc,KAAL,CAAWhC,MAAM,CAACiB,CAAD,CAAjB,CAAhC;AACAW,UAAAA,CAAC,CAAC9B,QAAF,CAAW,KAAX,EAAkB,CAAlB,EAAqB,CAAC,CAACmB,CAAD,CAAD,KAASC,IAAI,CAACe,GAAL,CAASjC,MAAM,CAACiB,CAAD,CAAf,CAA9B;AACAW,UAAAA,CAAC,CAAC9B,QAAF,CAAW,KAAX,EAAkB,CAAlB,EAAqB,CAAC,CAACmB,CAAD,CAAD,KAASC,IAAI,CAACgB,GAAL,CAASlC,MAAM,CAACiB,CAAD,CAAf,CAA9B;AACAW,UAAAA,CAAC,CAAC9B,QAAF,CAAW,KAAX,EAAkB,CAAlB,EAAqB,CAAC,CAACmB,CAAD,CAAD,KAASC,IAAI,CAACiB,GAAL,CAASnC,MAAM,CAACiB,CAAD,CAAf,CAA9B,EAT+B,CAU/B;;AACAW,UAAAA,CAAC,CAAC9B,QAAF,CAAW,KAAX,EAAkB,CAAlB,EAAqB,CAAC,CAACmB,CAAD,EAAID,CAAJ,CAAD,KAAYE,IAAI,CAACkB,GAAL,CAASpC,MAAM,CAACiB,CAAD,CAAf,EAAoBjB,MAAM,CAACgB,CAAD,CAA1B,CAAjC;AACAY,UAAAA,CAAC,CAAC9B,QAAF,CAAW,KAAX,EAAkB,CAAlB,EAAqB,CAAC,CAACmB,CAAD,EAAID,CAAJ,CAAD,KAAYE,IAAI,CAACmB,GAAL,CAASrC,MAAM,CAACiB,CAAD,CAAf,EAAoBjB,MAAM,CAACgB,CAAD,CAA1B,CAAjC,EAZ+B,CAa/B;;AACAY,UAAAA,CAAC,CAAC9B,QAAF,CAAW,OAAX,EAAoB,CAApB,EAAuB,CAAC,CAACwC,CAAD,EAAIC,EAAJ,EAAQC,EAAR,CAAD,KAAiBtB,IAAI,CAACmB,GAAL,CAASrC,MAAM,CAACuC,EAAD,CAAf,EAAqBrB,IAAI,CAACkB,GAAL,CAASpC,MAAM,CAACwC,EAAD,CAAf,EAAqBxC,MAAM,CAACsC,CAAD,CAA3B,CAArB,CAAxC,EAd+B,CAe/B;;AACAV,UAAAA,CAAC,CAAC9B,QAAF,CAAW,MAAX,EAAmB,CAAnB,EAAsB,MAAMoB,IAAI,CAACuB,MAAL,EAA5B;AACAb,UAAAA,CAAC,CAAC9B,QAAF,CAAW,MAAX,EAAmB,CAAnB,EAAsB,CAAC,CAACmB,CAAD,EAAID,CAAJ,CAAD,KAAY;AAChC,kBAAM0B,CAAC,GAAG1C,MAAM,CAACiB,CAAD,CAAhB;AAAA,kBAAqB0B,CAAC,GAAG3C,MAAM,CAACgB,CAAD,CAA/B;AAAoC,mBAAO0B,CAAC,GAAGxB,IAAI,CAACuB,MAAL,MAAiBE,CAAC,GAAGD,CAArB,CAAX;AACrC,WAFD,EAjB+B,CAoB/B;;AACAd,UAAAA,CAAC,CAAC9B,QAAF,CAAW,KAAX,EAAkB,CAAlB,EAAqB,CAAC,CAAC8C,IAAD,EAAO3B,CAAP,EAAUD,CAAV,CAAD,KAAmB4B,IAAI,GAAG3B,CAAH,GAAOD,CAAnD;AACAY,UAAAA,CAAC,CAAC9B,QAAF,CAAW,MAAX,EAAmB,CAAnB,EAAsB,CAAC,CAACwC,CAAD,CAAD,KAASlB,OAAO,CAACkB,CAAD,CAAtC,EAtB+B,CAuB/B;;AACAV,UAAAA,CAAC,CAAC9B,QAAF,CAAW,MAAX,EAAmB,CAAnB,EAAsB,CAAC,CAACmB,CAAD,EAAGD,CAAH,EAAKjF,CAAL,CAAD,KAAaiE,MAAM,CAACiB,CAAD,CAAN,GAAY,CAACjB,MAAM,CAACgB,CAAD,CAAN,GAAUhB,MAAM,CAACiB,CAAD,CAAjB,IAAwBjB,MAAM,CAACjE,CAAD,CAA7E;AACD;;AA7CwB,O,GAgD3B;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;;yBACe7B,a", "sourcesContent": ["// rpn_calculator_infix.ts\r\n// Complete TypeScript: Infix -> RPN -> Bytecode VM\r\n// Usage example at bottom.\r\n\r\nexport type RPNValue = number | boolean;\r\nexport interface RPNContext { [name: string]: RPNValue | undefined; }\r\nexport type RPNFn = (args: RPNValue[]) => RPNValue;\r\n\r\n// -------- Function registry (supporting multiple arities per name) --------\r\ninterface FnMeta {\r\n  id: number;\r\n  name: string;\r\n  arity: number;\r\n  impl: RPNFn;\r\n}\r\n\r\nexport class FunctionRegistry {\r\n  // name -> (arity -> FnMeta)\r\n  private byName = new Map<string, Map<number, FnMeta>>();\r\n  private byId: FnMeta[] = [];\r\n\r\n  register(name: string, arity: number, impl: RPNFn): FnMeta {\r\n    if (!Number.isInteger(arity) || arity < 0) throw new Error('arity must be >= 0');\r\n    let map = this.byName.get(name);\r\n    if (!map) { map = new Map(); this.byName.set(name, map); }\r\n    const existing = map.get(arity);\r\n    if (existing) {\r\n      existing.impl = impl;\r\n      return existing;\r\n    }\r\n    const meta: FnMeta = { id: this.byId.length, name, arity, impl };\r\n    map.set(arity, meta);\r\n    this.byId.push(meta);\r\n    return meta;\r\n  }\r\n\r\n  getByNameAndArity(name: string, arity: number): FnMeta | undefined {\r\n    const map = this.byName.get(name);\r\n    return map ? map.get(arity) : undefined;\r\n  }\r\n\r\n  // convenience: get any meta for a name when only one arity exists\r\n  getSingleByName(name: string): FnMeta | undefined {\r\n    const map = this.byName.get(name);\r\n    if (!map) return undefined;\r\n    if (map.size === 1) return Array.from(map.values())[0];\r\n    return undefined;\r\n  }\r\n\r\n  getById(id: number): FnMeta {\r\n    return this.byId[id];\r\n  }\r\n}\r\n\r\n// ---------------- Bytecode VM ----------------\r\nenum OpCode {\r\n  PUSH_CONST = 0,\r\n  PUSH_VAR = 1,\r\n  ADD = 2, SUB = 3, MUL = 4, DIV = 5, MOD = 6, POW = 7,\r\n  NEG = 8,\r\n  LT = 9, LE = 10, GT = 11, GE = 12, EQ = 13, NEQ = 14,\r\n  AND = 15, OR = 16, NOT = 17,\r\n  CALL = 18,\r\n}\r\n\r\nexport class RPNProgram {\r\n  private readonly code: number[];\r\n  private readonly consts: RPNValue[];\r\n  private readonly varNames: string[];\r\n  private readonly registry: FunctionRegistry;\r\n  private readonly strictVars: boolean;\r\n\r\n  constructor(code: number[], consts: RPNValue[], varNames: string[], registry: FunctionRegistry, strictVars = true) {\r\n    this.code = code;\r\n    this.consts = consts;\r\n    this.varNames = varNames;\r\n    this.registry = registry;\r\n    this.strictVars = strictVars;\r\n  }\r\n\r\n  evaluate(ctx: RPNContext): RPNValue {\r\n    const varVals: RPNValue[] = new Array(this.varNames.length);\r\n    for (let i = 0; i < this.varNames.length; i++) {\r\n      const name = this.varNames[i];\r\n      const v = ctx[name];\r\n      if (v === undefined) {\r\n        if (this.strictVars) throw new Error(`Missing variable '${name}' in context.`);\r\n        varVals[i] = 0;\r\n      } else {\r\n        varVals[i] = v;\r\n      }\r\n    }\r\n\r\n    const stack: RPNValue[] = [];\r\n    const code = this.code;\r\n    let ip = 0;\r\n    while (ip < code.length) {\r\n      switch (code[ip++] as OpCode) {\r\n        case OpCode.PUSH_CONST: {\r\n          const idx = code[ip++]; stack.push(this.consts[idx]); break;\r\n        }\r\n        case OpCode.PUSH_VAR: {\r\n          const idx = code[ip++]; stack.push(varVals[idx]); break;\r\n        }\r\n        case OpCode.ADD: { const b = Number(stack.pop()); const a = Number(stack.pop()); stack.push(a + b); break; }\r\n        case OpCode.SUB: { const b = Number(stack.pop()); const a = Number(stack.pop()); stack.push(a - b); break; }\r\n        case OpCode.MUL: { const b = Number(stack.pop()); const a = Number(stack.pop()); stack.push(a * b); break; }\r\n        case OpCode.DIV: { const b = Number(stack.pop()); const a = Number(stack.pop()); stack.push(a / b); break; }\r\n        case OpCode.MOD: { const b = Number(stack.pop()); const a = Number(stack.pop()); stack.push(a % b); break; }\r\n        case OpCode.POW: { const b = Number(stack.pop()); const a = Number(stack.pop()); stack.push(Math.pow(a, b)); break; }\r\n        case OpCode.NEG: { const a = Number(stack.pop()); stack.push(-a); break; }\r\n        case OpCode.LT: { const b = Number(stack.pop()); const a = Number(stack.pop()); stack.push(a < b); break; }\r\n        case OpCode.LE: { const b = Number(stack.pop()); const a = Number(stack.pop()); stack.push(a <= b); break; }\r\n        case OpCode.GT: { const b = Number(stack.pop()); const a = Number(stack.pop()); stack.push(a > b); break; }\r\n        case OpCode.GE: { const b = Number(stack.pop()); const a = Number(stack.pop()); stack.push(a >= b); break; }\r\n        case OpCode.EQ: { const b = stack.pop(); const a = stack.pop(); stack.push(a === b); break; }\r\n        case OpCode.NEQ: { const b = stack.pop(); const a = stack.pop(); stack.push(a !== b); break; }\r\n        case OpCode.AND: { const b = Boolean(stack.pop()); const a = Boolean(stack.pop()); stack.push(a && b); break; }\r\n        case OpCode.OR: { const b = Boolean(stack.pop()); const a = Boolean(stack.pop()); stack.push(a || b); break; }\r\n        case OpCode.NOT: { const a = Boolean(stack.pop()); stack.push(!a); break; }\r\n        case OpCode.CALL: {\r\n          const fnId = code[ip++]; const arity = code[ip++];\r\n          const args = new Array<RPNValue>(arity);\r\n          for (let i = arity - 1; i >= 0; i--) args[i] = stack.pop() as RPNValue;\r\n          const meta = this.registry.getById(fnId);\r\n          const out = meta.impl(args);\r\n          stack.push(out);\r\n          break;\r\n        }\r\n        default: throw new Error(`Unknown opcode ${(code[ip-1])}`);\r\n      }\r\n    }\r\n\r\n    if (stack.length !== 1) throw new Error(`Invalid program: stack has ${stack.length} values.`);\r\n    return stack[0];\r\n  }\r\n\r\n  getVariables(): readonly string[] { return this.varNames; }\r\n}\r\n\r\n// ---------------- Compiler from RPN tokens to bytecode ----------------\r\nexport interface CompileOptions { unknownIdentifierIsVar?: boolean; strictVars?: boolean; }\r\n\r\nconst NUM_RE = /^(?:[-+]?\\d+(?:\\.\\d+)?)$/i;\r\n\r\nfunction compileRPNInternal(tokens: string[], registry: FunctionRegistry, opts?: CompileOptions): RPNProgram {\r\n  const code: number[] = [];\r\n  const consts: RPNValue[] = [];\r\n  const varIndex = new Map<string, number>();\r\n  const varNames: string[] = [];\r\n\r\n  const pushConst = (v: RPNValue) => { consts.push(v); code.push(OpCode.PUSH_CONST, consts.length - 1); };\r\n  const pushVar = (name: string) => {\r\n    let idx = varIndex.get(name);\r\n    if (idx === undefined) { idx = varNames.length; varIndex.set(name, idx); varNames.push(name); }\r\n    code.push(OpCode.PUSH_VAR, idx);\r\n  };\r\n  const emitBinary = (op: OpCode) => code.push(op);\r\n\r\n  const unknownIsVar = opts?.unknownIdentifierIsVar ?? true;\r\n  const strictVars = opts?.strictVars ?? true;\r\n\r\n  for (const raw of tokens) {\r\n    const t = raw.trim();\r\n    if (!t) continue;\r\n    // constants\r\n    if (NUM_RE.test(t)) { pushConst(parseFloat(t)); continue; }\r\n    if (/^true$/i.test(t)) { pushConst(true); continue; }\r\n    if (/^false$/i.test(t)) { pushConst(false); continue; }\r\n\r\n    // Operators\r\n    switch (t) {\r\n      case '+': emitBinary(OpCode.ADD); continue;\r\n      case '-': emitBinary(OpCode.SUB); continue;\r\n      case '*': emitBinary(OpCode.MUL); continue;\r\n      case '/': emitBinary(OpCode.DIV); continue;\r\n      case '%': emitBinary(OpCode.MOD); continue;\r\n      case '^': emitBinary(OpCode.POW); continue;\r\n      case 'neg': code.push(OpCode.NEG); continue; // unary neg\r\n      case '<': emitBinary(OpCode.LT); continue;\r\n      case '<=': emitBinary(OpCode.LE); continue;\r\n      case '>': emitBinary(OpCode.GT); continue;\r\n      case '>=': emitBinary(OpCode.GE); continue;\r\n      case '==': emitBinary(OpCode.EQ); continue;\r\n      case '!=': emitBinary(OpCode.NEQ); continue;\r\n      case '&&': emitBinary(OpCode.AND); continue;\r\n      case '||': emitBinary(OpCode.OR); continue;\r\n      case '!': code.push(OpCode.NOT); continue;\r\n    }\r\n\r\n    // Function token produced by infix parser: format FUNC:name:arity\r\n    if (t.startsWith('FUNC:')) {\r\n      const parts = t.split(':');\r\n      // parts[0] == 'FUNC'\r\n      const name = parts[1];\r\n      const arity = parseInt(parts[2], 10);\r\n      const meta = registry.getByNameAndArity(name, arity);\r\n      if (!meta) throw new Error(`Function '${name}' with arity ${arity} is not registered.`);\r\n      code.push(OpCode.CALL, meta.id, arity);\r\n      continue;\r\n    }\r\n\r\n    // identifier -> variable or error\r\n    if (unknownIsVar) {\r\n      pushVar(t);\r\n      continue;\r\n    }\r\n    throw new Error(`Unknown token '${t}'.`);\r\n  }\r\n\r\n  return new RPNProgram(code, consts, varNames, registry, strictVars);\r\n}\r\n\r\n// ---------------- Infix -> RPN (Shunting-yard) ----------------\r\ntype Token = { type: 'number'|'ident'|'op'|'lparen'|'rparen'|'comma'|'bool', value: string };\r\n\r\nfunction tokenize(expr: string): Token[] {\r\n  const out: Token[] = [];\r\n  const re = /\\s*([0-9]*\\.?[0-9]+|[A-Za-z_][A-Za-z0-9_]*|<=|>=|==|!=|&&|\\|\\||[+\\-*/%^(),!<>])\\s*/g;\r\n  let m: RegExpExecArray | null;\r\n  while ((m = re.exec(expr)) !== null) {\r\n    const s = m[1];\r\n    if (/^[0-9]*\\.?[0-9]+$/.test(s)) out.push({type:'number', value:s});\r\n    else if (/^true|false$/i.test(s)) out.push({type:'bool', value:s});\r\n    else if (s === '(') out.push({type:'lparen', value:s});\r\n    else if (s === ')') out.push({type:'rparen', value:s});\r\n    else if (s === ',') out.push({type:'comma', value:s});\r\n    else if (/^[+\\-*/%^!<>]=?$/.test(s) || s === '&&' || s === '||') out.push({type:'op', value:s});\r\n    else out.push({type:'ident', value:s});\r\n  }\r\n  return out;\r\n}\r\n\r\n// Operator properties\r\ninterface OpInfo { prec: number; assoc: 'left'|'right'|'none'; unary?: boolean; }\r\nconst OPS: Record<string, OpInfo> = {\r\n  // unary '!' handled separately; unary '-' will be 'neg'\r\n  '^': { prec: 7, assoc: 'right' },\r\n  'neg': { prec: 6, assoc: 'right', unary: true },\r\n  '!': { prec: 6, assoc: 'right', unary: true },\r\n  '*': { prec: 5, assoc: 'left' },\r\n  '/': { prec: 5, assoc: 'left' },\r\n  '%': { prec: 5, assoc: 'left' },\r\n  '+': { prec: 4, assoc: 'left' },\r\n  '-': { prec: 4, assoc: 'left' },\r\n  '<': { prec: 3, assoc: 'left' },\r\n  '<=': { prec: 3, assoc: 'left' },\r\n  '>': { prec: 3, assoc: 'left' },\r\n  '>=': { prec: 3, assoc: 'left' },\r\n  '==': { prec: 2, assoc: 'left' },\r\n  '!=': { prec: 2, assoc: 'left' },\r\n  '&&': { prec: 1, assoc: 'left' },\r\n  '||': { prec: 0, assoc: 'left' },\r\n};\r\n\r\nfunction infixToRPN(expr: string, registry: FunctionRegistry): string[] {\r\n  const tokens = tokenize(expr);\r\n  const output: string[] = [];\r\n  const opStack: Array<{type:'op'|'func'|'lparen'|'rparen', value: string, argCount?: number}> = [];\r\n\r\n  // helper: when an identifier is followed by '(', it's a function name\r\n  for (let i = 0; i < tokens.length; i++) {\r\n    const t = tokens[i];\r\n\r\n    if (t.type === 'number' || t.type === 'bool') {\r\n      output.push(t.value);\r\n      continue;\r\n    }\r\n\r\n    if (t.type === 'ident') {\r\n      // If next token is lparen -> function\r\n      const next = tokens[i+1];\r\n      if (next && next.type === 'lparen') {\r\n        // push function onto opStack; track argument count (start at 1 for first arg, will increment on commas)\r\n        opStack.push({type:'func', value: t.value, argCount: 1});\r\n      } else {\r\n        // variable\r\n        output.push(t.value);\r\n      }\r\n      continue;\r\n    }\r\n\r\n    if (t.type === 'comma') {\r\n      // pop operators to output until left paren\r\n      while (opStack.length > 0 && opStack[opStack.length-1].type !== 'lparen') {\r\n        const op = opStack.pop()!;\r\n        if (op.type === 'op') output.push(op.value);\r\n        else if (op.type === 'func') {\r\n          // shouldn't reach here on comma, but handle defensively\r\n          output.push(`FUNC:${op.value}:${(op.argCount ?? 0)}`);\r\n        }\r\n      }\r\n      // increment argCount of function on stack (function must be below the left paren)\r\n      // find nearest func below the lparen\r\n      for (let j = opStack.length - 1; j >= 0; j--) {\r\n        if (opStack[j].type === 'lparen') {\r\n          // Look for function below the lparen\r\n          if (j > 0 && opStack[j-1].type === 'func') {\r\n            opStack[j-1].argCount = (opStack[j-1].argCount ?? 1) + 1;\r\n          }\r\n          break;\r\n        }\r\n      }\r\n      continue;\r\n    }\r\n\r\n    if (t.type === 'op') {\r\n      const rawOp = t.value;\r\n      // detect unary minus: if '-' and (start or after lparen or after comma or after another operator)\r\n      const prev = tokens[i-1];\r\n      const isUnary = rawOp === '-' && (\r\n        i === 0 ||\r\n        (prev && (prev.type === 'op' || prev.type === 'lparen' || prev.type === 'comma'))\r\n      );\r\n\r\n      const opKey = (isUnary && rawOp === '-') ? 'neg' : rawOp;\r\n\r\n      if (!(opKey in OPS)) throw new Error(`Unknown operator '${rawOp}'`);\r\n\r\n      const o1 = OPS[opKey];\r\n      while (opStack.length > 0) {\r\n        const top = opStack[opStack.length - 1];\r\n        if (top.type !== 'op') break;\r\n        const o2 = OPS[top.value];\r\n        if (!o2) break;\r\n        if ((o1.assoc === 'left' && o1.prec <= o2.prec) || (o1.assoc === 'right' && o1.prec < o2.prec)) {\r\n          output.push(opStack.pop()!.value);\r\n        } else break;\r\n      }\r\n      opStack.push({type:'op', value: opKey});\r\n      continue;\r\n    }\r\n\r\n    if (t.type === 'lparen') {\r\n      opStack.push({type:'lparen', value:'('});\r\n      // if the token before lparen is a function, we already pushed the function earlier.\r\n      continue;\r\n    }\r\n\r\n    if (t.type === 'rparen') {\r\n      // pop until left paren\r\n      while (opStack.length > 0 && opStack[opStack.length-1].type !== 'lparen') {\r\n        const op = opStack.pop()!;\r\n        if (op.type === 'op') output.push(op.value);\r\n        else if (op.type === 'func') {\r\n          // shouldn't normally be here\r\n          output.push(`FUNC:${op.value}:${(op.argCount ?? 0)}`);\r\n        }\r\n      }\r\n      if (opStack.length === 0) throw new Error('Mismatched parentheses');\r\n      // pop the left paren\r\n      opStack.pop();\r\n      // if top is a function, pop it to output with correct arity\r\n      if (opStack.length > 0 && opStack[opStack.length-1].type === 'func') {\r\n        const fn = opStack.pop()!;\r\n        // determine if there was any argument: check token before current index (i)\r\n        const before = tokens[i-1];\r\n        let finalArity: number;\r\n\r\n        if (before && before.type === 'lparen') {\r\n          // empty function call like f()\r\n          finalArity = 0;\r\n        } else {\r\n          // function has arguments, use the argCount we've been tracking\r\n          finalArity = fn.argCount ?? 1;\r\n        }\r\n\r\n        output.push(`FUNC:${fn.value}:${finalArity}`);\r\n      }\r\n      continue;\r\n    }\r\n  }\r\n\r\n  // pop remaining ops\r\n  while (opStack.length > 0) {\r\n    const op = opStack.pop()!;\r\n    if (op.type === 'lparen' || op.type === 'rparen') throw new Error('Mismatched parentheses');\r\n    if (op.type === 'op') output.push(op.value);\r\n    else if (op.type === 'func') {\r\n      // function with no parentheses? shouldn't happen\r\n      output.push(`FUNC:${op.value}:${op.argCount ?? 0}`);\r\n    }\r\n  }\r\n  return output;\r\n}\r\n\r\n// ---------------- Public RPNCalculator integrating infix parser ----------------\r\nexport class RPNCalculator {\r\n  readonly registry = new FunctionRegistry();\r\n\r\n  constructor(registerBuiltins = true) {\r\n    if (registerBuiltins) this.registerBuiltins();\r\n  }\r\n\r\n  compile(expr: string | string[], opts?: CompileOptions): RPNProgram {\r\n    let tokens: string[];\r\n    if (Array.isArray(expr)) tokens = expr;\r\n    else {\r\n      tokens = infixToRPN(expr, this.registry);\r\n    }\r\n    return compileRPNInternal(tokens, this.registry, opts);\r\n  }\r\n\r\n  register(name: string, arity: number, impl: RPNFn): void {\r\n    this.registry.register(name, arity, impl);\r\n  }\r\n\r\n  private registerBuiltins(): void {\r\n    const r = this.registry;\r\n    // unary\r\n    r.register('abs', 1, ([a]) => Math.abs(Number(a)));\r\n    r.register('floor', 1, ([a]) => Math.floor(Number(a)));\r\n    r.register('ceil', 1, ([a]) => Math.ceil(Number(a)));\r\n    r.register('round', 1, ([a]) => Math.round(Number(a)));\r\n    r.register('sin', 1, ([a]) => Math.sin(Number(a)));\r\n    r.register('cos', 1, ([a]) => Math.cos(Number(a)));\r\n    r.register('tan', 1, ([a]) => Math.tan(Number(a)));\r\n    // min/max 2-arity\r\n    r.register('min', 2, ([a, b]) => Math.min(Number(a), Number(b)));\r\n    r.register('max', 2, ([a, b]) => Math.max(Number(a), Number(b)));\r\n    // clamp(x, lo, hi)\r\n    r.register('clamp', 3, ([x, lo, hi]) => Math.max(Number(lo), Math.min(Number(hi), Number(x))));\r\n    // rand: support rand() and rand(a,b)\r\n    r.register('rand', 0, () => Math.random());\r\n    r.register('rand', 2, ([a, b]) => {\r\n      const A = Number(a), B = Number(b); return A + Math.random() * (B - A);\r\n    });\r\n    // select ternary: sel(cond, a, b)\r\n    r.register('sel', 3, ([cond, a, b]) => (cond ? a : b));\r\n    r.register('bool', 1, ([x]) => Boolean(x));\r\n    // lerp\r\n    r.register('lerp', 3, ([a,b,t]) => Number(a) + (Number(b)-Number(a)) * Number(t));\r\n  }\r\n}\r\n\r\n// ---------------- Example usage ----------------\r\n/*\r\nconst calc = new RPNCalculator();\r\n\r\nconst p1 = calc.compile('(a + b) * 2');\r\nconsole.log('vars:', p1.getVariables()); // [ 'a', 'b' ]\r\nconsole.log('eval1', p1.evaluate({ a: 3, b: 4 })); // 14\r\n\r\nconst p2 = calc.compile('sin(x) + cos(y)');\r\nconsole.log('eval2', p2.evaluate({ x: Math.PI/2, y: 0 })); // 2\r\n\r\nconst p3 = calc.compile('rand(0, 100) + min(3,5) * max(7,9)');\r\nconsole.log('eval3', p3.evaluate({})); // rand + 3*9\r\n\r\n// custom function\r\nconst calc2 = new RPNCalculator();\r\ncalc2.register('sum3', 3, ([a,b,c]) => Number(a)+Number(b)+Number(c));\r\nconst p4 = calc2.compile('sum3(1,2,3) * 2');\r\nconsole.log('eval4', p4.evaluate({})); // 12\r\n\r\n// unary minus\r\nconst p5 = calc.compile('-a * 5');\r\nconsole.log('eval5', p5.evaluate({ a: 2 })); // -10\r\n*/\r\n\r\n// Export default if you like\r\nexport default RPNCalculator;\r\n"]}